# Clariti Enterprise Permitting - Complete Study Guide

## Course Overview: "Permitting with Clariti Enterprise"

### Course Objectives
By the end of this course, you will be able to:

1. **Define and describe** the packaged fields used for permit classification
2. **Define and describe** the process for identifying when additional classification fields are needed  
3. **Design additional** classification fields for use in the creation of new permit types

---

## Chapter 1: Introduction ✅

### What is Clariti Permitting?

**Think of it like a digital city hall system** - Clariti Enterprise is a comprehensive solution that helps cities and municipalities manage the entire permitting process from application to completion.

#### Key Concept: **Permit Classification**
- One of the most critical processes in implementing Clariti's Permitting system
- It's like **organizing a massive library** - you need to classify and group different types of permits so the city knows how to handle them
- This classification enables automation of:
  - Submissions required
  - Reviews needed  
  - Fees charged
  - Inspections required
  - Whether an occupancy permit is needed

#### The Four Primary Classification Fields
These are the "DNA" of every permit - they determine how the permit behaves:

1. **Record Type** 
2. **Work Type**
3. **Use Class** 
4. **Use Type**

---

## Chapter 2: Parcels ✅

### Understanding Parcels

**Think of a parcel like a property's "ID card"** - it's a unit of land with:
- Geographic and legal boundaries
- Valuation and zoning identifiers  
- Ownership designation
- Land use designation

**Why Parcels Matter:**
- All permits must be attached to a specific parcel
- All privately owned land in a city has a defined parcel managed by an Assessor
- Historic parcels can be marked as inactive but remain attached to existing permits

---

## Chapter 3: Permit Journey ✅

### The Permit Lifecycle

**Think of this like a roadmap** - every permit follows a journey from start to finish:

```
Application → Review → Issuance → Inspection → Certificate of Occupancy → Closed
```

#### Key Journey Phases:
1. **Application** - Applicant submits and pays initial fees
2. **Review** - Staff evaluates for compliance  
3. **Issuance** - Permit is approved and document generated
4. **Inspection** - Physical verification work meets code
5. **Certificate of Occupancy** - Building approved for use
6. **Closed** - Permit lifecycle complete

#### Important Journey Concepts:

**Multiple Permits, Same Journey:**
- Different permit types can share the same journey
- But they have radically different outcomes because they use different **Templates**
- **Example:** A single-family home and patio cover both go through the same basic journey, but require different inspections and fees

---

## Chapter 4: Permit Types 1 - Essentials (Current Chapter)

### The Data Challenge

**The Big Problem:** Enterprise can collect massive amounts of data during a permit's lifecycle. The challenge is determining which data is actually useful for classification and automation.

**The Solution:** Start with a careful review of the client's current permit types and identify the core classification needs.

---

### The Four Core Classification Fields Explained

Think of these as **building blocks** - each field adds another layer of specificity to how the permit is handled.

#### 1. Record Type 
**What it is:** The highest level classification of permits
**Think of it as:** The main category in your filing system

**Examples:**
- Building Permit
- Quick Permit  
- Trade Permit

**Real-world example:** 
- A single-family home = Building Permit
- A patio cover = Quick Permit

#### 2. Work Type
**What it is:** Represents the kind of work being performed
**Think of it as:** What exactly is being built or modified

**Examples:**
- New construction
- Electrical installation
- Repair
- Alteration

**Real-world example:** Building a new single-family home = "New Build"

#### 3. Use Class
**What it is:** Designation of residential vs. commercial
**Think of it as:** Who will use this building

**Examples:**
- Residential
- Commercial

**Why it matters:** Often used in Building Permits but not Quick or Trade permits. Commercial typically has different requirements than residential.

#### 4. Use Type  
**What it is:** Defines what the building will be used for
**Think of it as:** The specific purpose of the building

**Examples:**
- Single Family Dwelling
- Duplex  
- Multi-family Dwelling
- Townhouse
- Industrial Building
- Mixed Use

**Real-world example:** A large industrial project with hazardous waste components would have a specific Use Type that triggers additional requirements.

---

### Example of Complete Classification

Here's how the four fields work together:

| Record Type | Work Type | Use Class | Use Type |
|-------------|-----------|-----------|----------|
| Building Permit | New | Residential | Single Family Dwelling |
| Building Permit | Addition | Residential | Duplex |  
| Quick Permit | Demolition | Commercial | Mixed Use |
| Trade Permit | Electrical | Residential | Mobile Home |

---

### Adding New Fields

**Key Principle:** Understanding the four standard fields is crucial, but you'll likely need additional fields based on client discussions.

#### When to Add New Fields:
- When standard fields don't capture important classification differences
- When additional fields drive different behaviors in the Permit Journey and Templates
- When you need to reduce the number of required Journeys while representing all necessary permit types

#### Goal: **Efficiency**
- Reduce the number of required Journeys  
- Still represent all necessary permit types
- Enable proper automation

---

### Real-World Examples

#### Example 1: Sign Permits
**Scenario:** Wall-mounted signs vs. ground-mounted signs
- **Problem:** Different inspection requirements
- **Solution:** Add custom field for "Sign Installation Type"
- **Impact:** Affects both Permit and Inspection Template requirements

#### Example 2: Fee Structure  
**Scenario:** Commercial permits charged higher fees than residential
- **Problem:** Use Class field alone determines fee structure
- **Solution:** Ensure Use Class field is included in Fee template
- **Impact:** Automatically applies correct fee structure

---

### Implementation Process: Putting it All Together

#### Step 1: Investigate and Understand
- Review client's current permit setup
- Understand their specific business processes
- Identify unique requirements

#### Step 2: Determine Journey Factors  
- What factors affect Permit Journey behavior?
- Map out different journey requirements

#### Step 3: Determine Template Factors
- What factors affect Templates?
- Consider fees, inspections, reviews, requirements

#### Step 4: Start with Core Fields
- Begin with the four common fields for classification
- Build foundational structure

#### Step 5: Add Accommodating Fields
- Add other fields to accommodate specific factors
- Ensure all business requirements are met

---

## Key Terms Glossary

**Applicant:** The person applying for a permit (Property owner or Contractor)

**Application:** The submission that starts a Permit process

**Console:** The Clariti interface that administrators and city staff use (the "back-end")

**Creation Mapping:** Specifies what values should be added to a Permit record from a Template

**Cycle:** One round of reviews or inspections in a Milestone

**Journey:** The stages a permit goes through from application to completion

**Milestone:** Controls workflow and manages reviews/inspections by tracking pass/fail status

**Portal:** The interface applicants use to submit permits and track activity

**Template:** Reference records that create additional records when predetermined conditions are met

**Template Object Mapping:** Connects controlling fields to set Template conditions

---

## Study Tips

### 1. **Think Real-World**
Always relate concepts back to actual city permitting processes you might be familiar with.

### 2. **Remember the Hierarchy**
Record Type → Work Type → Use Class → Use Type (from broad to specific)

### 3. **Focus on Automation**
Every classification decision should enable better automation of the permitting process.

### 4. **Start Simple**
Use the four core fields first, then add complexity as needed.

### 5. **Consider the User**
Think about both the city staff using the Console and applicants using the Portal.

---

## Next Steps

After mastering Chapter 4, you'll move on to:
- **Chapter 5: Template Mapping** - How to connect permit types to their required processes
- **Chapter 6: Valuation and Fees** - Setting up fee structures and work item calculations

---

## Quick Review Questions

1. What are the four primary classification fields?
2. What's the difference between Use Class and Use Type?
3. When would you add additional fields beyond the core four?
4. How do Templates relate to permit classification?
5. What's the ultimate goal of permit classification?

*Remember: The goal is to create an efficient, automated system that serves both city staff and permit applicants effectively.*