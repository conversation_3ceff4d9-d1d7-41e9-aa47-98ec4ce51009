# **Credit Lifecycle Management System**
## **Feature Proposal**

---

## **📋 What We're Building**

An automated system to manage promotional users (VIP Access & Early Users) through their credit lifecycle - from initial signup to potential premium conversion.

---

## **🎯 Business Logic**

### **User Types:**
- **VIP Access** (Referral users): 550,000 credits for 30 days
- **Early Users** (First 1000): 220,000 credits for 30 days
- **Regular Users**: 50,000 credits (permanent free)

### **Lifecycle Rules:**
1. **Day 30**: Transition to Grace Period → 50,000 credits
2. **12 Months**: Grace period to upgrade to premium
3. **After 12 Months**: Permanent free tier (if no purchase)
4. **Any Time**: Premium purchase → Full benefits restored

---

## **🔄 Complete User Journey**

```mermaid
graph TD
    A[User Signs Up] --> B{User Type?}
    
    B -->|Referral Code| C[VIP Access<br/>550k Credits<br/>30 Days]
    B -->|First 1000| D[Early Users<br/>220k Credits<br/>30 Days]
    B -->|Regular| E[Free Plan<br/>50k Credits<br/>Permanent]
    
    C --> F[Day 30 Transition]
    D --> F
    
    F --> G[Grace Period<br/>50k Credits<br/>12 Months]
    
    G --> H{User Action?}
    H -->|Purchases Premium| I[Premium Active<br/>Full Benefits]
    H -->|No Purchase<br/>12 Months| J[Permanent Free<br/>50k Credits]
    
    I -->|Subscription Ends| G
    
    style C fill:#e1f5fe
    style D fill:#f3e5f5
    style G fill:#fff3e0
    style I fill:#e8f5e8
    style J fill:#ffebee
```

---

## **🏗️ How It Works**

### **1. Status Tracking**
- **Database Fields**: Add expiry dates to user subscriptions
- **Real-time Checks**: Verify status when user logs in or uses API
- **Automated Processing**: Daily job for inactive users

### **2. User Experience**
- **Dashboard Warnings**: Show expiry countdown and upgrade options
- **Email Notifications**: 7, 3, 1 day warnings before transitions
- **Smooth Transitions**: Preserve all user data and project history

### **3. Data Preservation**
```json
{
  "user_id": "user123",
  "status": "grace_period",
  "credits": 50000,
  "grace_expires_at": "2025-02-01",
  "preserved_usage": {
    "original_plan": "vip_access",
    "credits_used": 180000,
    "transition_date": "2024-02-01"
  }
}
```

---

## **📧 Communication Strategy**

### **User Notifications:**

#### **Before Expiry (30-day period):**
- **Day 23**: "Your VIP credits expire in 7 days"
- **Day 27**: "3 days left - upgrade to keep benefits"
- **Day 29**: "Final day - don't lose your premium access"

#### **During Grace Period (12 months):**
- **Month 1**: "Welcome to grace period - 12 months to upgrade"
- **Month 6**: "6 months left in your grace period"
- **Month 9**: "3 months left - special upgrade offer"
- **Month 11**: "Final month - upgrade now or lose premium features"

#### **Transition Messages:**
- **Grace Start**: "Account updated - you now have 50k credits and 12 months to upgrade"
- **Premium Purchase**: "Welcome back to premium - all benefits restored"
- **Permanent Free**: "You're now on our free plan - upgrade anytime"

---

## **🔍 Tracking & Monitoring**

### **User Status Detection:**
```javascript
// Real-time status queries
- Users expiring today
- Users expiring in 3 days  
- Users expiring in 7 days
- Grace period users (by months remaining)
- Overdue transitions (missed by system)
```

### **Admin Dashboard:**
- **Today's Transitions**: Users moving between tiers
- **Expiry Timeline**: Visual calendar of upcoming transitions
- **Conversion Tracking**: Grace period users who upgraded
- **System Health**: Successful vs failed transitions

---

## **⚙️ Technical Implementation**

### **Core Components:**

#### **1. Subscription Checker Service**
- Check user status on login/API calls
- Perform transitions when needed
- Update user dashboard in real-time

#### **2. Background Processor**
- Daily job for inactive users
- Process missed transitions
- Send scheduled notifications

#### **3. Notification Engine**
- Email alerts for users
- Dashboard warnings
- Admin system alerts

### **Database Changes:**
- Add `promotional_expires_at` field
- Add `grace_expires_at` field  
- Add `preserved_usage` object
- Create indexes for efficient queries

---

## **🎯 User Experience Flow**

### **What Users See:**

#### **Dashboard Status Banner:**
```
⚠️ Your VIP Access expires in 5 days! 
   [Upgrade Now] [View Usage]
   
🎁 Grace Period: 8 months left to upgrade
   [View Premium Plans] [Usage History]
```

#### **Email Notifications:**
- Clear subject lines: "Your Kavia credits expire in 3 days"
- Usage summary: "You've used 180,000 of 550,000 credits"
- Clear action: "Upgrade now to keep your premium features"
- Reassurance: "All your projects and data will remain safe"

---

## **📊 System Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Login    │    │ Status Checker  │    │   Database      │
│                 │───▶│                 │───▶│                 │
│ • Dashboard     │    │ • Check Expiry  │    │ • Update Status │
│ • API Calls     │    │ • Send Alerts   │    │ • Log Changes   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ User Interface  │    │ Email Service   │    │ Admin Dashboard │
│                 │    │                 │    │                 │
│ • Status Banner │    │ • Expiry Alerts │    │ • Monitor Users │
│ • Upgrade CTA   │    │ • Grace Reminders│    │ • Track Metrics │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## **🚀 Implementation Plan**

### **Phase 1: Core System (2 weeks)**
- Add expiry fields to database
- Create status checking service
- Build basic transition logic

### **Phase 2: User Experience (2 weeks)**  
- Dashboard status displays
- Email notification system
- User-facing transition flows

### **Phase 3: Automation (2 weeks)**
- Background jobs for inactive users
- Admin monitoring dashboard
- System health checks

---

## **💡 Key Benefits**

### **For Users:**
- **Clear Communication**: Always know account status
- **Data Safety**: Projects and history preserved
- **Flexible Upgrade**: 12-month grace period
- **No Surprises**: Advance warnings before changes

### **For Business:**
- **Automated System**: Minimal manual work
- **Conversion Window**: 12 months to upgrade users
- **User Retention**: Grace period keeps users engaged
- **Scalable Solution**: Handles growth automatically

---

**This system ensures a smooth, transparent experience for users while maximizing opportunities for premium conversions through automated lifecycle management.**
