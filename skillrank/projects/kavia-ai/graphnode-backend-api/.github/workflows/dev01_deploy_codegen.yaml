name: Codegenservice Deployment

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: k-dev01
  REPO_NAME: codegenservice

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main

      - name: Build and Push codegenservice Docker Image
        uses: duplocloud/actions/build-image@main
        id: build-and-push-codegenservice
        with:
          push: true
          repo: ${{ env.REPO_NAME }}
          dockerfile: Dockerfile.dev
          platforms: linux/amd64
          cache: false
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          tags: ${{ github.sha }}

  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main
        with:
          admin: true

      - name: Create Kubeconfig
        run: |
          aws eks update-kubeconfig \
            --name duploinfra-dev \
            --region us-west-2

        # Step 1: Update the Duplo config‑map before touching any pods
      - name: update image-id for codegen in duploctl
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          echo $GIT_SHA
          NEW_IMAGE_TAG=${IMAGE##*:}
          echo $NEW_IMAGE_TAG
          echo "finding config map"
          duploctl configmap find codegenservicedeploymentdevconfig -o yaml > /tmp/test7.yaml
          cat /tmp/test7.yaml
          echo "defining image prefix"
          IMAGE_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"
          echo "using sed"
          sed -i -E "s|(${IMAGE_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml
          echo "opening final file"
          cat /tmp/test7.yaml
          echo "updating existing config map"
          duploctl configmap update codegenservicedeploymentdevconfig -f /tmp/test7.yaml

      # Step 2: Graceful deployment with buffer management
      - name: Deploy to DEV environment (Graceful)
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          NAMESPACE=duploservices-k-dev01
          SERVICE=codegen
          BATCH_SIZE=2
          BUFFER_COUNT=4

          # Collect all available pods first (don't update immediately)
          AVAILABLE_PODS=()
          while IFS= read -r line; do
            if [[ -z "$line" ]]; then
              continue
            fi
            
            read -r POD READY STATUS RESTARTS AGE <<< "$line"
            echo "Processing pod: $POD with status: $STATUS"
            
            if [[ "$STATUS" != "Running" ]]; then
              echo "Skipping non-running pod: $POD"
              continue
            fi

            # Skip zoltan-dev pods
            if [[ "$POD" =~ ^zoltan-dev- ]]; then
              echo "Skipping zoltan-dev pod: $POD"
              continue
            fi

            BASE=$(echo "$POD" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')

            if kubectl exec "$POD" -c "$BASE" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "Screen session detected in $POD — skipping image update"
              CONFIGMAP_NAME="pod-status-${BASE}"
              echo "Patching config map $CONFIGMAP_NAME for tracking"
              kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: Could not update config map $CONFIGMAP_NAME"
              continue
            fi

            # Add to available pods list instead of updating immediately
            AVAILABLE_PODS+=("$BASE")
            echo "Added $BASE to available pods list"
          done < <(kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers)

          TOTAL_AVAILABLE=${#AVAILABLE_PODS[@]}
          echo "Total available pods for update: $TOTAL_AVAILABLE"

          # Safety check - ensure we have enough pods to maintain buffer
          if [ "$TOTAL_AVAILABLE" -le "$BUFFER_COUNT" ]; then
            echo "Not enough pods ($TOTAL_AVAILABLE) to maintain buffer ($BUFFER_COUNT). Skipping deployment."
            echo "Please ensure more pods are available before deploying."
            exit 1
          fi

          # Calculate how many pods to update (keeping buffer)
          PODS_TO_UPDATE=$((TOTAL_AVAILABLE - BUFFER_COUNT))
          if [ "$PODS_TO_UPDATE" -gt "$BATCH_SIZE" ]; then
            PODS_TO_UPDATE=$BATCH_SIZE
          fi

          echo "Deployment Plan:"
          echo "  - Total available pods: $TOTAL_AVAILABLE"
          echo "  - Pods to update: $PODS_TO_UPDATE"
          echo "  - Buffer pods (remain available): $((TOTAL_AVAILABLE - PODS_TO_UPDATE))"

          # Update only the calculated number of pods
          UPDATED_COUNT=0
          for i in $(seq 0 $((PODS_TO_UPDATE - 1))); do
            if [ $i -lt ${#AVAILABLE_PODS[@]} ]; then
              DEPLOYMENT=${AVAILABLE_PODS[$i]}
              echo "🛠 Updating deployment/$DEPLOYMENT ($((i + 1))/$PODS_TO_UPDATE)"
              
              if kubectl set image deployment/$DEPLOYMENT $DEPLOYMENT=$IMAGE -n $NAMESPACE 2>&1 | grep -q 'not found'; then
                echo "Deployment $DEPLOYMENT not found, skipping"
              else
                echo "Deployment $DEPLOYMENT updated successfully"
                UPDATED_COUNT=$((UPDATED_COUNT + 1))
              fi
            fi
          done

          echo "Graceful deployment completed!"
          echo "Summary: Updated $UPDATED_COUNT deployments, kept $((TOTAL_AVAILABLE - UPDATED_COUNT)) pods available for users"
  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs:
      - build
      - deploy
    if: always() && needs.build.result != 'skipped' && needs.deploy.result != 'skipped'
    steps:
      - name: Debug Job Results
        run: |
          echo "build.result  = '${{ needs.build.result }}'"
          echo "deploy.result = '${{ needs.deploy.result }}'"

      - name: Slack Deployment Succeeded
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation deployment complete* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation deployment failed* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}
