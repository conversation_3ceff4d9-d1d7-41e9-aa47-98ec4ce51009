name: Codegenservice Deployment

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  DUPLO_HOST: https://duplo.cloud.kavia.ai
  DUPLO_TOKEN: ${{ secrets.DUPLO_TOKEN }}
  DUPLO_TENANT: k-dev01
  REPO_NAME: codegenservice

jobs:
  build:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    outputs:
      backend_image: ${{ steps.build-and-push-codegenservice.outputs.image }}:${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main

      - name: Build and Push codegenservice Docker Image
        uses: duplocloud/actions/build-image@main
        id: build-and-push-codegenservice
        with:
          push: true
          repo: ${{ env.REPO_NAME }}
          dockerfile: Dockerfile.dev
          platforms: linux/amd64
          cache: false
          build-args: |
            AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}
          tags: ${{ github.sha }}

  deploy:
    if: contains(github.event.head_commit.message, 'codegendeploy')
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Duplo Setup
        uses: duplocloud/actions@main
        with:
          admin: true

      - name: Create Kubeconfig
        run: |
          aws eks update-kubeconfig \
            --name duploinfra-dev \
            --region us-west-2

        # Step 1: Update the Duplo config‑map before touching any pods
      - name: update image-id for codegen in duploctl
        env:
          IMAGE: "${{ needs.build.outputs.backend_image }}"
        run: |
          echo "Image being used: $IMAGE"
          GIT_SHA="${{ github.sha }}"
          echo $GIT_SHA
          NEW_IMAGE_TAG=${IMAGE##*:}
          echo $NEW_IMAGE_TAG
          echo "finding config map"
          duploctl configmap find codegenservicedeploymentdevconfig -o yaml > /tmp/test7.yaml
          cat /tmp/test7.yaml
          echo "defining image prefix"
          IMAGE_PREFIX="127214169382.dkr.ecr.us-west-2.amazonaws.com/codegenservice"
          echo "using sed"
          sed -i -E "s|(${IMAGE_PREFIX}):[a-zA-Z0-9._-]+|\1:${NEW_IMAGE_TAG}|g" /tmp/test7.yaml
          echo "opening final file"
          cat /tmp/test7.yaml
          echo "updating existing config map"
          duploctl configmap update codegenservicedeploymentdevconfig -f /tmp/test7.yaml

      # Step 2: Loop through running pods and update only their image
      - name: Deploy to DEV environment
        env:
          IMAGE: ${{ needs.build.outputs.backend_image }}
          GIT_SHA: ${{ github.sha }}
        run: |
          echo "Image being used: $IMAGE"
          echo "Git SHA: $GIT_SHA"
          
          # Get running pods and process them
          kubectl get pods -l service=$SERVICE -n $NAMESPACE --no-headers | while IFS= read -r line; do
            if [[ -z "$line" ]]; then
              continue
            fi
            
            # Parse pod information
            read -r POD READY STATUS RESTARTS AGE <<< "$line"
            echo "Processing pod: $POD with status: $STATUS"
            
            # Skip non-running pods
            if [[ "$STATUS" != "Running" ]]; then
              echo "Skipping non-running pod: $POD"
              continue
            fi

            # Skip zoltan-dev pods
            if [[ "$POD" =~ ^zoltan-dev- ]]; then
              echo "Skipping zoltan-dev pod: $POD"
              continue
            fi

            # Extract base deployment name
            BASE=$(echo "$POD" | sed -E 's/-[a-z0-9]+-[a-z0-9]+$//')
            
            # Check for screen sessions
            if kubectl exec "$POD" -c "$BASE" -n $NAMESPACE -- screen -ls 2>/dev/null | grep -q 'Detached\|Attached'; then
              echo "🧷 Screen session detected in $POD — skipping image update"
              CONFIGMAP_NAME="pod-status-${BASE}"
              echo "Patching config map $CONFIGMAP_NAME for tracking"
              
              kubectl patch configmap "$CONFIGMAP_NAME" -n $NAMESPACE \
                --type merge \
                -p "{\"data\": {\"deployment-status\": \"not-up-to-date\", \"image-sha\": \"${GIT_SHA}\"}}" \
                2>/dev/null || echo "Warning: Could not update config map $CONFIGMAP_NAME"
              continue
            fi

            echo "🛠 Updating image for deployment/$BASE"
            if kubectl set image deployment/$BASE $BASE=$IMAGE -n $NAMESPACE 2>&1 | grep -q 'not found'; then
              echo "  ⚠️  Deployment $BASE not found, skipping"
            else
              echo "  ✅  Deployment $BASE updated successfully"
            fi
          done
  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs:
      - build
      - deploy
    if: always() && needs.build.result != 'skipped' && needs.deploy.result != 'skipped'
    steps:
      - name: Debug Job Results
        run: |
          echo "build.result  = '${{ needs.build.result }}'"
          echo "deploy.result = '${{ needs.deploy.result }}'"

      - name: Slack Deployment Succeeded
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ *Code Generation deployment complete* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}

      - name: Slack Deployment Failed
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          curl -X POST \
            -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 *Code Generation deployment failed* on branch \`${{ github.ref_name }}\` in *${{ github.repository }}*.\nView logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}" \
            ${{ secrets.SLACK_HOOK_DEV_DEPLOY }}
