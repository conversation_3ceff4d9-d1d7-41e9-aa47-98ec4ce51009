# Deployment Queue System - TaskQueue Integration

## Overview

The Deployment Controller integrates with TaskQueue to provide comprehensive tracking and monitoring of the entire build and deployment pipeline. This system captures every aspect of the deployment process, from initial trigger to final completion, enabling real-time monitoring, debugging, and analytics.

## Event Categories

All deployment events are tagged with `"type": "deployment_queue"` and include one of the following event types:

- **`build_started`** - Initial deployment trigger
- **`build_log`** - Real-time build output streaming
- **`build_completed`** - Docker process completion (success/failure)
- **`build_final_status`** - Overall build result with full context

## Queue Structure

Events are enqueued using:
```python
task_queue.enqueue(task_id, event_data)
```

Where `task_id` identifies the specific deployment session and `event_data` contains the event details.

---

## Scenario 1: Build Started

**Trigger:** Deployment process begins in `_handle_start_deploy()`

**When:** User initiates a new deployment request

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_started",
  "deployment_id": "abc12345",
  "deployment_type": "web",
  "build_command": "npm run build",
  "root_path": "/home/<USER>/workspace/my-project",
  "project_id": "proj_67890",
  "branch_name": "main", 
  "message": "🚀 Build process started",
  "timestamp": "2024-01-15T10:30:00.123Z"
}
```

**Use Cases:**
- Track deployment initiation
- Monitor deployment frequency
- Capture build configuration
- Correlate with user actions

---

## Scenario 2: Live Build Logs

**Trigger:** Docker container outputs build logs via `BuildOutputCallback.on_output()`

**When:** Real-time streaming of build process output

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_log",
  "message": "🔨 > my-app@1.0.0 build",
  "timestamp": "2024-01-15T10:30:15.456Z",
  "log_level": "info"
}
```

```json
{
  "type": "deployment_queue",
  "event_type": "build_log",
  "message": "🔨 Creating an optimized production build...",
  "timestamp": "2024-01-15T10:30:25.789Z", 
  "log_level": "info"
}
```

**Use Cases:**
- Real-time build monitoring
- Debug build issues
- Performance analysis
- User progress indication

**Characteristics:**
- High frequency events (multiple per second)
- Sequential ordering important
- Each log line is a separate event

---

## Scenario 3: Build Process Completed (Success)

**Trigger:** Docker process exits successfully via `BuildOutputCallback.on_exit()`

**When:** Build command finishes with exit code 0

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_completed",
  "status": "success",
  "message": "✅ Build process completed successfully!",
  "exit_code": 0,
  "timestamp": "2024-01-15T10:32:00.111Z"
}
```

**Use Cases:**
- Immediate success notification
- Trigger next pipeline stage
- Performance metrics collection

---

## Scenario 4: Build Process Completed (Failure)

**Trigger:** Docker process exits with error via `BuildOutputCallback.on_exit()`

**When:** Build command finishes with non-zero exit code

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_completed",
  "status": "failed",
  "message": "❌ Build process failed with exit code: 1",
  "exit_code": 1,
  "timestamp": "2024-01-15T10:31:45.222Z"
}
```

**Use Cases:**
- Immediate failure notification
- Stop pipeline progression
- Alert system integration
- Error rate tracking

---

## Scenario 5: Final Build Status (Success)

**Trigger:** Build method completes successfully after all validations

**When:** Overall build process succeeds with database updates

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_final_status",
  "deployment_id": "abc12345",
  "status": "success", 
  "message": "Build completed successfully",
  "build_path": "/home/<USER>/workspace/my-project",
  "return_code": 0,
  "timestamp": "2024-01-15T10:32:01.333Z"
}
```

**Use Cases:**
- Final deployment status
- Database state confirmation
- Analytics and reporting
- Artifact path tracking

---

## Scenario 6: Final Build Status (Failure)

**Trigger:** Build method detects failure from exit code

**When:** Build validation fails after process completion

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_final_status",
  "deployment_id": "abc12345",
  "status": "failed",
  "message": "Build failed with return code 1", 
  "build_path": "/home/<USER>/workspace/my-project",
  "return_code": 1,
  "timestamp": "2024-01-15T10:31:46.444Z"
}
```

**Use Cases:**
- Final failure confirmation
- Error categorization
- Cleanup triggering
- Support ticket creation

---

## Scenario 7: Build Error (File Not Found)

**Trigger:** FileNotFoundError exception during build setup

**When:** Root path doesn't exist or is inaccessible

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_final_status",
  "deployment_id": "abc12345",
  "status": "failed",
  "message": "Path error: Root path does not exist: /invalid/path",
  "error_type": "FileNotFoundError",
  "return_code": -1,
  "timestamp": "2024-01-15T10:30:05.555Z"
}
```

**Use Cases:**
- Configuration validation
- Environment setup issues
- Path resolution problems
- Infrastructure monitoring

---

## Scenario 8: Build Error (General Exception)

**Trigger:** Any other exception during build process

**When:** Unexpected errors (Docker issues, permissions, etc.)

**Example Queue Data:**
```json
{
  "type": "deployment_queue",
  "event_type": "build_final_status", 
  "deployment_id": "abc12345",
  "status": "failed",
  "message": "Build process failed: Docker daemon not running",
  "error_type": "Exception",
  "return_code": -1,
  "timestamp": "2024-01-15T10:30:10.666Z"
}
```

**Use Cases:**
- Infrastructure problem detection
- Service health monitoring
- Emergency alerts
- System diagnostics

---

## Event Flow Patterns

### Successful Deployment Timeline
```
1. build_started        → Initial trigger
2. build_log (multiple) → Real-time progress  
3. build_completed      → Process finished
4. build_final_status   → Success confirmation
```

### Failed Deployment Timeline
```
1. build_started        → Initial trigger
2. build_log (some)     → Partial progress
3. build_completed      → Process failed  
4. build_final_status   → Error details
```

### Exception Timeline
```
1. build_started        → Initial trigger
2. build_final_status   → Immediate error
```

---

## Integration Guidelines

### Queue Consumer Implementation

```python
# Example consumer pattern
def process_deployment_events(task_id):
    while True:
        event = task_queue.dequeue(task_id)
        if not event:
            break
            
        data = event['data']
        
        if data['type'] == 'deployment_queue':
            handle_deployment_event(data)

def handle_deployment_event(data):
    event_type = data['event_type']
    
    if event_type == 'build_started':
        # Initialize deployment tracking
        track_deployment_start(data)
        
    elif event_type == 'build_log':
        # Stream logs to UI/monitoring
        stream_build_output(data)
        
    elif event_type == 'build_completed':
        # Handle immediate completion
        handle_build_completion(data)
        
    elif event_type == 'build_final_status':
        # Final status processing
        finalize_deployment(data)
```

### Event Correlation

All events for a single deployment can be correlated using:
- **`task_id`** - Unique session identifier
- **`deployment_id`** - Deployment-specific ID (when available)
- **`timestamp`** - Chronological ordering

### Error Handling

```python
# Graceful error handling
try:
    task_queue.enqueue(task_id, event_data)
except Exception as e:
    # Queue errors should not break deployment
    logger.warning(f"Failed to enqueue event: {e}")
    # Continue with deployment process
```

---

## Monitoring and Analytics

### Key Metrics Available

1. **Deployment Frequency**: Count of `build_started` events
2. **Success Rate**: Ratio of successful to failed `build_final_status`
3. **Build Duration**: Time between `build_started` and `build_completed`
4. **Error Categories**: Distribution of `error_type` values
5. **Log Volume**: Count of `build_log` events per deployment

### Performance Indicators

- **Average Build Time**: `build_completed.timestamp - build_started.timestamp`
- **Error Rate**: Failed deployments / Total deployments
- **Infrastructure Health**: Frequency of "Exception" vs "FileNotFoundError"

---

## Best Practices

### For Queue Consumers

1. **Process in Order**: Respect timestamp ordering for accurate flow
2. **Handle Missing Events**: Some scenarios may skip intermediate events
3. **Implement Timeouts**: Don't wait indefinitely for expected events
4. **Error Recovery**: Handle malformed or unexpected event data
5. **Rate Limiting**: Build logs can be high-frequency

### For Integration

1. **Use task_id**: Always correlate events by task_id
2. **Check event_type**: Different events require different handling
3. **Validate Data**: Ensure required fields exist before processing
4. **Log Processing**: Keep audit trail of queue consumption
5. **Cleanup**: Remove processed events to prevent queue buildup

---

## Troubleshooting

### Common Issues

**Missing Events**: Check TaskQueue error logs, may indicate enqueue failures

**Out of Order Events**: Verify timestamp-based sorting in consumer

**High Memory Usage**: Implement queue cleanup for completed deployments

**Duplicate Processing**: Use event IDs to detect and skip duplicates

### Debug Queries

```python
# Check queue length
length = task_queue.get_queue_length(task_id)

# Peek at pending events
events = task_queue.peek(task_id, count=10)

# List all active deployments
active = task_queue.list_active_tasks()
```

---

## Event Schema Reference

### Common Fields
- `type`: Always "deployment_queue"
- `event_type`: One of the 4 event types
- `timestamp`: ISO 8601 timestamp
- `message`: Human-readable description

### Event-Specific Fields

**build_started:**
- `deployment_id`, `deployment_type`, `build_command`
- `root_path`, `project_id`, `branch_name`

**build_log:**
- `log_level` (typically "info")

**build_completed:**
- `status` ("success" or "failed")
- `exit_code` (integer)

**build_final_status:**
- `deployment_id`, `status`, `return_code`
- `build_path` (for success)
- `error_type` (for failures)

---

This queue system provides comprehensive visibility into the deployment pipeline, enabling robust monitoring, debugging, and analytics capabilities for deployment operations. 