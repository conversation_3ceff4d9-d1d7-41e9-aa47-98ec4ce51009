import argparse
import sys
import os
from app.utils.datetime_utils import generate_timestamp
import json
from app.utils.aws.secrets_loader import get_vertex_secret
from code_generation_core_agent.config import config as code_generation_config
from code_generation_core_agent.project_schemas import ProjectSchema, ContainerType
import yaml
from app.connection.establish_db_connection import get_node_db
import threading

def create_project_work_item(project: ProjectSchema, containers_to_remove = []):
    overview_data = {
        "project_name": project.overview.project_name,
        "description": project.overview.description,
        'env': project.overview.env,
    }
    print(f"overview_data --------------------------------------------------------------------------> {overview_data}")
    # Get framework information by container type
    frontends = project.get_containers_by_type(ContainerType.FRONTEND)
    if frontends:
        overview_data["frontend_framework"] = frontends[0].framework

    backends = project.get_containers_by_type(ContainerType.BACKEND)
    if backends:
        overview_data["backend_framework"] = backends[0].framework

    databases = project.get_containers_by_type(ContainerType.DATABASE)
    if databases:
        overview_data["database_framework"] = databases[0].framework

    mobiles = project.get_containers_by_type(ContainerType.MOBILE)
    if mobiles:
        overview_data["mobile_framework"] = mobiles[0].framework

    all_containers = []

    print(generate_timestamp(),containers_to_remove, project.containers)
    # We can use a helper function to convert all containers to the work item format
    for container in project.containers:
        if container.container_name not in containers_to_remove:
            try:
                container.env.update(project.overview.env)
            except Exception as e:
                print(generate_timestamp(),f"Error updating env for container {container.container_name}: {e}")
            all_containers.append(container.to_work_item_format())

    project_work_item = {
        'project_type': 'multi_container',
        'project_name': overview_data['project_name'],
        'description': overview_data['description'],
        'env':overview_data['env'],
        'containers': all_containers,
        'overview': overview_data,
        '3rd_party_services': project.overview.third_party_services,
        'figma_components': "",
        'manifest_path': manifest_path # Mandatory for multi-project setup
    }
    return project_work_item

def setup_google_credentials(persistent_path="google_credentials.json"):
    """Setup Google credentials - optimized for threading."""
    try:
        # Check if credentials file already exists
        if os.path.exists(persistent_path):
            print(generate_timestamp(),f"✅ Using existing credentials file at: {persistent_path}")
            # Set the environment variable
            absolute_path = os.path.abspath(persistent_path)
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = absolute_path
            return absolute_path
        
        print(generate_timestamp(),"🔄 Fetching Google credentials from secrets...")
        
        # Get the vertex secret data
        credentials_data = get_vertex_secret()
        
        # Print type and sample of credentials
        print(generate_timestamp(),f"📋 Type of credentials: {type(credentials_data)}")

        # Write to the persistent path
        with open(persistent_path, 'w') as f:
            # Handle both dictionary and string cases
            if isinstance(credentials_data, dict):
                json.dump(credentials_data, f)
            else:
                # If it's already a string, write directly
                f.write(credentials_data)
        
        # Get absolute path
        absolute_path = os.path.abspath(persistent_path)
        
        # Set the environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = absolute_path
        
        print(generate_timestamp(),f"✅ GOOGLE_APPLICATION_CREDENTIALS set to: {absolute_path}")
        return absolute_path
        
    except Exception as e:
        print(generate_timestamp(),f"❌ Error setting up credentials: {e}")
        return None

def setup_google_credentials_daemon(persistent_path="google_credentials.json"):
    """
    Setup Google credentials in a daemon thread.
    Returns immediately, credentials are set up in background.
    """
    def _setup_credentials():
        try:
            print(generate_timestamp(),"🔄 Starting Google credentials setup in daemon thread...")
            path = setup_google_credentials(persistent_path)
            if path:
                print(generate_timestamp(),f"✅ Google credentials daemon setup completed: {path}")
            else:
                print(generate_timestamp(),"⚠️  Google credentials daemon setup failed")
        except Exception as e:
            print(generate_timestamp(),f"❌ Error in daemon credential setup: {e}")
    
    # Create and start daemon thread
    thread = threading.Thread(target=_setup_credentials, daemon=True)
    thread.start()
    print(generate_timestamp(),"Google credentials setup started in daemon thread", generate_timestamp())
    
    return thread

def write_formatted_yaml(content, file_path):
    """Write content as properly formatted YAML"""
    try:
        # If content is a JSON string, parse it first
        if isinstance(content, str):
            try:
                parsed_content = json.loads(content)
            except json.JSONDecodeError:
                # If not JSON, treat as regular string
                parsed_content = content
        else:
            parsed_content = content
        
        # Write as YAML with proper formatting
        with open(file_path, 'w', encoding='utf-8') as file:
            if isinstance(parsed_content, (dict, list)):
                yaml.dump(parsed_content, file, 
                        default_flow_style=False, 
                        indent=2, 
                        allow_unicode=True,
                        sort_keys=False)
            else:
                file.write(str(parsed_content))
                
    except Exception as e:
        # Fallback: write as formatted JSON
        with open(file_path, 'w', encoding='utf-8') as file:
            if isinstance(content, str):
                try:
                    parsed = json.loads(content)
                    file.write(json.dumps(parsed, indent=2, ensure_ascii=False))
                except:
                    file.write(content)
            else:
                file.write(json.dumps(content, indent=2, ensure_ascii=False))
# Call the function to setup credentials
setup_google_credentials_daemon()

# Print current path for debugging
print(generate_timestamp(),"Current PYTHONPATH:", os.environ.get("PYTHONPATH"))
print(generate_timestamp(),"Current sys.path:", sys.path)
os.environ['GOOGLE_PROJECT_ID'] = "vertex-ai-sandbox-447902"
os.environ['GOOGLE_LOCATION'] = "us-central1"


# Ensure the app directory is in the path
app_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if app_root not in sys.path:
    sys.path.insert(0, app_root)

from app.connection.establish_db_connection import get_mongo_db
from app.core.constants import TaskStatus, TASKS_COLLECTION_NAME
from app.core.code_generation import execute_code_generation_agent
from app.utils.batch_utils import task_execute, task_execute_maintenance
from app.utils.async_utils import async_to_sync
import json
import traceback
import sys
from app.connection.tenant_middleware import tenant_context
from app.celery_app import user_context
from app.models.code_generation_model import AgentParams
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings

from app.utils.code_generation_utils import get_codegeneration_path
from app.utils.project_utils import find_host

print(generate_timestamp(),"PHASE 1, START TIME: ", generate_timestamp())
def parse_arguments():
    parser = argparse.ArgumentParser(description='Code Generation Job Runner')
    parser.add_argument('--input_args', type=str, required=True,
                      help='JSON string containing input arguments')
    parser.add_argument('--stage', type=str, required=True,
                      help='Stage for code generation')
    return parser.parse_args()
args = parse_arguments()
input_arguments = json.loads(args.input_args)
print(generate_timestamp(),input_arguments)
user_id = input_arguments.get("user_id")
user_context.set(user_id)
if user_id is not None:
    os.environ["user_id"] = user_id
batch_job_trigger = os.environ.get("BATCH_JOB_TRIGGER", False)
print(generate_timestamp(),batch_job_trigger)
task_id = input_arguments.get("task_id")
os.environ["task_id"] = task_id
retry = input_arguments.get("retry")
tenant_id = input_arguments.get("tenant_id")
print(generate_timestamp(),["screen", "-L", "-Logfile", f"/tmp/kavia/workspace/logs/{task_id}.log", "-dmS", task_id, "python", "app/batch_jobs/jobs.py", "--input_args", args.input_args, "--stage", args.stage])
print(generate_timestamp(),"Tenant_id for code generation", tenant_id)
tenant_context.set(tenant_id)

if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
    db = get_mongo_db(user_id=user_id).db
else:
    db = get_mongo_db().db
node_db = get_node_db()
ws_client = WebSocketClient(task_id, settings.WEBSOCKET_URI)
agent_name = input_arguments.get("agent_name")
if not agent_name:
    agent_name = "CodeGeneration"
        
try:
    ws_client.connect()
except:
    pass

path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)

task = db[TASKS_COLLECTION_NAME].find_one({ "_id": task_id })

print(generate_timestamp(),task)
encrypted_scm_id = task.get("encrypted_scm_id")
if encrypted_scm_id:
    os.environ["encrypted_scm_id"] = encrypted_scm_id

resume = task.get("resume", False)
if resume:
    print(generate_timestamp(),"Resuming task", task_id)
    os.environ["resume"] = str(resume)
    
new_repo_creation = task.get("new_repo_creation","")
os.environ["new_repo_creation"] = str(new_repo_creation)
project_repository = task.get("repositories")
os.environ["repositories"] = json.dumps(project_repository)
iframe = task.get("iframe")
os.environ["iframe"] = str(iframe)
if iframe:
    try:
        host = find_host(iframe, include_scheme=True)
        os.environ["host"] = host
        print(generate_timestamp(),"Host for iframe", host)
    except Exception as e:
        print(generate_timestamp(),"Error finding host for iframe", str(e))
        pass
    
llm_model = code_generation_config.get("LLM", "model") or input_arguments.get("llm_model")
project_id = int(input_arguments.get("project_id"))

agent_params = AgentParams(
    llm_model=llm_model,
    agent_name=agent_name
)
print(generate_timestamp(),"agent details", agent_params.model_dump_json())
if agent_name == "CodeMaintenance":
    print(generate_timestamp(),"++ STARTING CODE MAINTENANCE SESSION ++")
elif agent_name == "DocumentCreation":
    print(generate_timestamp(),"++ STARTING CODE MAINTENANCE SESSION ++")
else:
    container_ids = task.get("container_ids")
    print(generate_timestamp(),"Starting code generation job")


print(generate_timestamp(),f"Executing code generation for project {project_id}")

try:
    if agent_name == "CodeMaintenance":
        project_details, work_items = async_to_sync(task_execute_maintenance(project_id))
        if task.get("project_manifest"):
            yaml_string = task.get("project_manifest")
            write_formatted_yaml(yaml_string, f"{path}/.project_manifest.yaml")
            print(generate_timestamp(),"MANIFEST CONTENT", yaml_string)
            project_reloaded = ProjectSchema.load_from_file(f"{path}/.project_manifest.yaml")
            manifest_path = f"{path}/.project_manifest.yaml"
            work_items = create_project_work_item(project_reloaded)
            work_items["manifest_path"] = f"{path}/.project_manifest.yaml"

            # Restore documents from MongoDB to kavia-docs folder during resume
            try:
                from app.utils.code_generation_utils import restore_docs_during_resume
                restore_result = async_to_sync(restore_docs_during_resume(task_id, agent_name))
                if restore_result["status"] == "success":
                    print(generate_timestamp(), f"✅ Restored {restore_result['successful_restores']} documents to kavia-docs")
                elif restore_result["status"] == "no_documents":
                    print(generate_timestamp(), "ℹ️ No documents found in MongoDB, skipping kavia-docs creation")
                else:
                    print(generate_timestamp(), f"⚠️ Document restore failed: {restore_result['message']}")
            except Exception as restore_error:
                print(generate_timestamp(), f"❌ Error restoring documents: {restore_error}")
        elif project_details.get('Manifest'):
            try:
                yaml_content  = project_details.get('Manifest')
                code_gen_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
                manifest_path = f"{code_gen_path}/.project_manifest.yaml"
                
                # Ensure the directory exists and write YAML content
                os.makedirs(code_gen_path, exist_ok=True)
                # Get repository information from project_details
                current_repositories = project_details.get('repositories','')
                if current_repositories:
                    current_repositories:dict = json.loads(current_repositories)
                    current_repositories = current_repositories.values()
                else:
                    raise Exception("Repository was not created by Kavia")
                write_formatted_yaml(yaml_content, manifest_path)
                # Update manifest with repository information
                
                
                # Reload the updated manifest
                project_reloaded = ProjectSchema.load_from_file(manifest_path)
                containers_to_remove = []
                
                print (current_repositories, project_reloaded.containers)
                for container in project_reloaded.containers:     
                    name: str = container.container_name
                    found = False
                    
                    for repository in current_repositories:
                        if repository['container_name'] == name:
                            container.workspace = repository['repositoryName']
                            container.container_root = f"{repository['repositoryName']}/{container.container_name}"
                            found = True
                            
                    if not found:
                        containers_to_remove.append(name)
                        
                            
                for container in project_reloaded.containers:  
                    if container.container_name in containers_to_remove:
                        project_reloaded.remove_container(container.container_name)
                    
                
                project_reloaded.save_to_manifest(manifest_path)
                # Create work item with repository information
                work_items = create_project_work_item(project_reloaded, containers_to_remove=containers_to_remove)
                work_items["manifest_path"] = manifest_path

                # Restore documents from MongoDB to kavia-docs folder during resume
                try:
                    from app.utils.code_generation_utils import restore_docs_during_resume
                    restore_result = async_to_sync(restore_docs_during_resume(task_id, agent_name))
                    if restore_result["status"] == "success":
                        print(generate_timestamp(), f"✅ Restored {restore_result['successful_restores']} documents to kavia-docs")
                    elif restore_result["status"] == "no_documents":
                        print(generate_timestamp(), "ℹ️ No documents found in MongoDB, skipping kavia-docs creation")
                    else:
                        print(generate_timestamp(), f"⚠️ Document restore failed: {restore_result['message']}")
                except Exception as restore_error:
                    print(generate_timestamp(), f"❌ Error restoring documents: {restore_error}")
            except Exception as e:
                traceback.print_exc()
                print(generate_timestamp(),"Error in maintainence manifest", e)

            
    elif agent_name == "DocumentCreation":

        project_details, work_items = async_to_sync(task_execute_maintenance(project_id))

        work_items = {
            "project_node_id": project_id,
            "github_links": [f"https://github.com/{repo}" for repo in task["repositories"]["selected_repos"]]
        }

        # Restore documents from MongoDB to kavia-docs folder during resume
        try:
            from app.utils.code_generation_utils import restore_docs_during_resume
            restore_result = async_to_sync(restore_docs_during_resume(task_id, agent_name))
            if restore_result["status"] == "success":
                print(generate_timestamp(), f"✅ Restored {restore_result['successful_restores']} documents to kavia-docs")
            elif restore_result["status"] == "no_documents":
                print(generate_timestamp(), "ℹ️ No documents found in MongoDB, skipping kavia-docs creation")
            else:
                print(generate_timestamp(), f"⚠️ Document restore failed: {restore_result['message']}")
        except Exception as restore_error:
            print(generate_timestamp(), f"❌ Error restoring documents: {restore_error}")
    
    else:
        test_case = input_arguments.get("test_case")
        project_details, work_items = async_to_sync(task_execute(project_id, container_ids=container_ids, db=node_db, test_case=test_case, mongo_db=db))
    
        if project_details.get('Manifest') and agent_name == "CodeGeneration" :
            
            yaml_content  = project_details.get('Manifest')
            code_gen_path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
            manifest_path = f"{code_gen_path}/.project_manifest.yaml"
            
            # Ensure the directory exists and write YAML content
            os.makedirs(code_gen_path, exist_ok=True)
        
            # Get repository information from project_details
            current_repositories = project_details.get('current_repositories',[])
            
            
            
            write_formatted_yaml(yaml_content, manifest_path)
            # Update manifest with repository information
            
            
            # Reload the updated manifest
            project_reloaded = ProjectSchema.load_from_file(manifest_path)
            containers_to_remove = []
            
            for container in project_reloaded.containers:     
                name: str = container.container_name
                found = False
                
                for repository in current_repositories:
                    if repository['container_name'].replace(' ','').replace('-','_') == name.replace(' ','').replace('-','_'):
                        container.workspace = repository['repositoryName']
                        container.container_root = f"{repository['repositoryName']}/{container.container_name}"
                        found = True
                        
                if not found:
                    containers_to_remove.append(name)
                    
                        
            for container in project_reloaded.containers:
                container.env.update(project_reloaded.overview.env) 
                if container.container_name in containers_to_remove:
                    project_reloaded.remove_container(container.container_name)
                
            
            project_reloaded.save_to_manifest(manifest_path)
            # Create work item with repository information
            work_items = create_project_work_item(project_reloaded, containers_to_remove=containers_to_remove)
            work_items["manifest_path"] = manifest_path

            # Restore documents from MongoDB to kavia-docs folder during resume
            try:
                from app.utils.code_generation_utils import restore_docs_during_resume
                restore_result = async_to_sync(restore_docs_during_resume(task_id, agent_name))
                if restore_result["status"] == "success":
                    print(generate_timestamp(), f"✅ Restored {restore_result['successful_restores']} documents to kavia-docs")
                elif restore_result["status"] == "no_documents":
                    print(generate_timestamp(), "ℹ️ No documents found in MongoDB, skipping kavia-docs creation")
                else:
                    print(generate_timestamp(), f"⚠️ Document restore failed: {restore_result['message']}")
            except Exception as restore_error:
                print(generate_timestamp(), f"❌ Error restoring documents: {restore_error}")

    

    print(generate_timestamp(),"Project Details: ", project_details)
    print(generate_timestamp(),"WorkItem Detais: ", work_items)
    
    execute_code_generation_agent(project_details, work_items, task_id=task_id , agent_params=agent_params, ws_client=ws_client, mongo_db=db)
    
except Exception as e:
    print(generate_timestamp(),f"Failed to execute code generation: {str(e)}")
    error_traceback = traceback.format_exc()
    print(generate_timestamp(),"\nFull traceback:")
    print(generate_timestamp(),error_traceback)

    db[TASKS_COLLECTION_NAME].update_one(
        {"_id": task_id}, 
        {"$set": {
            "status": TaskStatus.FAILED,
            "execution_status": TaskStatus.FAILED
        }}
    )
    sys.exit(1)

