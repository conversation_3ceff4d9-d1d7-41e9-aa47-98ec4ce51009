# app/models/product_model.py
from pydantic import BaseModel, Field
from typing import Optional

class ProductListResponse(BaseModel):
    """Response model for product list endpoint with complete metadata."""
    
    # Basic product information
    product_id: str = Field(..., description="Stripe product ID")
    product_name: str = Field(..., description="Product name")
    product_description: str = Field(..., description="Product description")
    
    # Pricing information
    price_id: str = Field(..., description="Stripe price ID")
    currency: str = Field(..., description="Currency code (e.g., 'usd')")
    price: str = Field(..., description="Formatted price string")
    
    # Recurring pricing details
    is_recurring: bool = Field(..., description="Whether this is a recurring subscription")
    recurring_interval: Optional[str] = Field(None, description="Recurring interval (e.g., 'month', 'year')")
    recurring_interval_count: Optional[int] = Field(None, description="Number of intervals between charges")
    
    # Metadata fields from Stripe
    credits: Optional[int] = Field(None, description="Main credits amount")
    default_credits: Optional[int] = Field(None, description="Default credits for the product")
    display_credits: Optional[int] = Field(None, description="Credits amount to display to users")
    visibility: Optional[int] = Field(None, description="Product visibility flag (0=hidden, 1=visible)")
    
    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            # Add any custom encoders if needed
        }
        schema_extra = {
            "example": {
                "product_id": "prod_SmPPog1xkuhHza",
                "product_name": "Early Users",
                "product_description": "First 1000 Free users signing in will get 20$ worth credit",
                "price_id": "price_1RqqagCI2zbViAE2XU0Hx3i6",
                "currency": "usd",
                "price": "0.0",
                "is_recurring": False,
                "recurring_interval": None,
                "recurring_interval_count": None,
                "credits": 220000,
                "default_credits": 50000,
                "display_credits": 200000,
                "visibility": 1
            }
        }