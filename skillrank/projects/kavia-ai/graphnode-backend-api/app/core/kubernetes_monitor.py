from kubernetes import client, config
from kubernetes.client.rest import ApiException
from typing import Dict, List, Optional, <PERSON>ple
import re
import datetime
import time
import json
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
from app.utils.project_utils import get_kubernetes_environment
from app.core.Settings import settings, STAGE
from app.utils.datetime_utils import generate_timestamp
import os


def get_environment_and_namespace() -> Tuple[str, str]:
    """
    Get the environment and namespace based on settings.
    
    Returns:
        Tuple of (environment, namespace)
    """
    environment = get_kubernetes_environment()
    
    namespace_map = {
        'dev': 'duploservices-k-dev01',
        'qa': 'duploservices-k-qa01',
        'beta': 'duploservices-k-beta01'
    }
    
    namespace = namespace_map.get(environment, 'duploservices-k-dev01')
    
    return environment, namespace


class KubernetesAvailablePodsManager:
    """
    Direct Kubernetes API-based available pods management with atomic operations.
    """
    
    def __init__(self, environment: str = None, namespace: str = None, use_local_kubeconfig: bool = False):
        """Initialize Kubernetes client for specific environment."""
        self.environment = environment or self._detect_environment()
        self.namespace = namespace or self._get_namespace()
        self.use_local_kubeconfig = use_local_kubeconfig
        self._init_kubernetes_client()
    
    def _detect_environment(self) -> str:
        """Detect environment from settings."""
        if hasattr(settings, 'ENVIRONMENT'):
            return settings.ENVIRONMENT.lower()
        return STAGE.lower() if STAGE else 'dev'
    
    def _get_namespace(self) -> str:
        """Get namespace based on environment."""
        namespace_map = {
            'dev': 'duploservices-k-dev01',
            'qa': 'duploservices-k-qa01', 
            'beta': 'duploservices-k-beta01',
            'prod': 'duploservices-k-prod01'
        }
        return namespace_map.get(self.environment, 'duploservices-k-dev01')
    
    def _init_kubernetes_client(self):
        """Initialize Kubernetes client with proper configuration."""
        try:
            # Try in-cluster config first
            if self.use_local_kubeconfig:
                kubeconfig = self._load_kubeconfig_from_ssm()
                config.load_kube_config_from_dict(kubeconfig)
            else:
                config.load_incluster_config()
        except config.ConfigException:
            try:
                # Fall back to local kubeconfig
                config.load_kube_config()
            except config.ConfigException:
                # Load from SSM based on environment
                kubeconfig = self._load_kubeconfig_from_ssm()
                config.load_kube_config_from_dict(kubeconfig)
        
        self.core_v1 = client.CoreV1Api()
    
    def _load_kubeconfig_from_ssm(self) -> dict:
        """Load kubeconfig from SSM based on environment."""
        loader_map = {
            'dev': load_ssm_dev_param,
            'qa': load_ssm_qa_param,
            'beta': load_ssm_qa_param,
            'prod': load_ssm_pre_prod_param
        }
        loader = loader_map.get(self.environment, load_ssm_dev_param)
        return loader()
    
    def get_available_codegen_pods(self, sort_by_age: bool = True) -> Tuple[List[Dict], int]:
        """
        Get all running pods with label 'service=codegen' that have status 'available'
        by checking their corresponding ConfigMaps directly from Kubernetes.
        
        Args:
            sort_by_age: If True, sort pods by age (oldest first)
        
        Returns:
            Tuple of (list of pod detail dictionaries, count of available pods)
        """
        start_time = time.time()
        
        try:
            # Get all running pods with the specified label
            pods = self.core_v1.list_namespaced_pod(
                namespace=self.namespace,
                label_selector="service=codegen",
                field_selector="status.phase=Running"
            )
        except ApiException as e:
            print(f"Error listing pods: {e}")
            return [], 0
        
        pod_details = []
        
        # For each pod, check if status is "available" using ConfigMap
        for pod in pods.items:
            pod_name = pod.metadata.name
            print(f"---- Processing {pod_name} ----")
            
            try:
                # Extract the project ID from the pod name
                match = re.match(r'([\w\d]+)-' + self.environment + r'-', pod_name)
                if not match:
                    print(f"Could not extract project ID from pod name: {pod_name}")
                    continue
                    
                project_id = match.group(1)
                configmap_name = f"pod-status-{project_id}-{self.environment}"
                
                # Check pod availability status
                is_available = self._check_pod_availability(configmap_name, pod_name)
                
                # Also check if pod is fully ready (all containers ready)
                is_ready = self._check_pod_readiness(pod)
                
                if is_available and is_ready:
                    pod_info = self._extract_pod_details(pod, project_id)
                    pod_details.append(pod_info)
                    print(f"Pod {pod_name} is available and ready")
                else:
                    if not is_available:
                        print(f"Pod {pod_name} is not available (ConfigMap status)")
                    elif not is_ready:
                        print(f"Pod {pod_name} is not ready (containers not ready)")
                    else:
                        print(f"Pod {pod_name} is not available")
                
            except Exception as e:
                print(f"Error processing pod {pod_name}: {e}")
        
        # Sort pods by age (oldest first) if requested
        if sort_by_age and pod_details:
            pod_details.sort(key=lambda x: x['age'])
            print(f"Sorted {len(pod_details)} pods by age (oldest first)")
        
        count = len(pod_details)
        end_time = time.time()
        
        print(f"Available pods: {count}")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        
        return pod_details, count

    def get_used_codegen_pods(self) -> Tuple[List[Dict], int]:
        """
        Get all running pods with label 'service=codegen' that have status 'used'
        by checking their corresponding ConfigMaps directly from Kubernetes.
        
        Returns:
            Tuple of (list of used pod detail dictionaries, count of used pods)
        """
        start_time = time.time()
        
        try:
            # Get all running pods with the specified label
            pods = self.core_v1.list_namespaced_pod(
                namespace=self.namespace,
                label_selector="service=codegen",
                field_selector="status.phase=Running"
            )
        except ApiException as e:
            print(f"Error listing pods: {e}")
            return [], 0
        
        pod_details = []
        
        # For each pod, check if status is "used" using ConfigMap
        for pod in pods.items:
            pod_name = pod.metadata.name
            print(f"---- Processing {pod_name} ----")
            
            try:
                # Extract the project ID from the pod name
                match = re.match(r'([\w\d]+)-' + self.environment + r'-', pod_name)
                if not match:
                    print(f"Could not extract project ID from pod name: {pod_name}")
                    continue
                    
                project_id = match.group(1)
                configmap_name = f"pod-status-{project_id}-{self.environment}"
                
                # Check pod usage status
                is_used, pod_data = self._check_pod_usage(configmap_name, pod_name)
                
                # Also check if pod is fully ready (all containers ready)
                is_ready = self._check_pod_readiness(pod)
                
                if is_used and is_ready:
                    pod_info = self._extract_pod_details(pod, project_id)
                    # Add usage-specific information
                    pod_info["pod_id"] = project_id
                    pod_info["pod_name"] = pod_name
                    pod_info["usage_status"] = "used"
                    pod_info["assigned_at"] = pod_data.get("assigned_at", "")
                    pod_info["session_id"] = pod_data.get("session_id", "")
                    pod_info["tenant_id"] = pod_data.get("tenant_id", "")
                    pod_info["application_project_id"] = pod_data.get("project_id","")
                    pod_info["environment"] = self.environment
                    pod_details.append(pod_info)
                    print(f"Pod {pod_name} is used and ready")
                else:
                    if not is_used:
                        print(f"Pod {pod_name} is not used (ConfigMap status)")
                    elif not is_ready:
                        print(f"Pod {pod_name} is not ready (containers not ready)")
                    else:
                        print(f"Pod {pod_name} is not used")
                
            except Exception as e:
                print(f"Error processing pod {pod_name}: {e}")
        
        count = len(pod_details)
        end_time = time.time()
        
        print(f"Used pods: {count}")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        
        return pod_details, count

    def _check_pod_usage(self, configmap_name: str, pod_name: str) -> Tuple[bool, Dict]:
        """
        Check if a pod is used by examining its ConfigMap status.
        
        Args:
            configmap_name: Name of the ConfigMap to check
            pod_name: Name of the pod being checked
            
        Returns:
            Tuple of (True if pod is used, ConfigMap data dict)
        """
        try:
            # Get the ConfigMap for this pod
            config_map = self.core_v1.read_namespaced_config_map(
                name=configmap_name,
                namespace=self.namespace
            )
            
            # Check pod status from ConfigMap
            pod_status = config_map.data.get("pod-status", "")
            config_map_data = config_map.data if config_map.data else {}
            print(f"Pod {pod_name} status from ConfigMap: {pod_status}")
            
            return pod_status == "used", config_map_data
            
        except ApiException as api_e:
            if api_e.status == 404:
                # If ConfigMap doesn't exist, assume pod is not used
                print(f"ConfigMap {configmap_name} not found, assuming pod is not used")
                return False, {}
            else:
                print(f"Error getting ConfigMap for pod {pod_name}: {api_e}")
                return False, {}
        except Exception as e:
            print(f"Unexpected error checking ConfigMap for pod {pod_name}: {e}")
            return False, {}
    
    def get_one_available_pod_and_mark_as_used(self, session_id: str, tenant_id: str, project_id: str, 
                                               max_retries: int = 5, retry_delay: float = 0.1) -> Optional[Dict]:
        """
        Atomically assign the oldest available pod using Kubernetes optimistic locking.
        
        Args:
            session_id: Session ID for tracking
            tenant_id: Tenant ID for tracking
            project_id: Project ID for tracking
            max_retries: Maximum number of retries for atomic operations
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dict containing pod information if successful, None otherwise
        """
        for attempt in range(max_retries):
            try:
                # Get available pods sorted by age (oldest first)
                available_pods, count = self.get_available_codegen_pods(sort_by_age=True)
                
                if count == 0:
                    print("No available pods found")
                    return None
                
                # Try to claim pods in order (oldest first)
                for pod in available_pods:
                    claimed_pod = self._claim_pod_atomic_with_optimistic_locking(
                        pod, session_id, tenant_id, project_id
                    )
                    if claimed_pod:
                        print(f"Successfully claimed pod {pod['name']} on attempt {attempt + 1}")
                        return claimed_pod
                
                # If all pods were claimed by others, wait and retry
                if attempt < max_retries - 1:
                    print(f"All pods claimed by others, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    # Exponential backoff
                    retry_delay *= 1.5
                
            except Exception as e:
                print(f"Error in pod assignment attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 1.5
        
        print(f"Failed to assign pod after {max_retries} attempts")
        return None
    
    def _claim_pod_atomic_with_optimistic_locking(self, pod: Dict, session_id: str, 
                                                  tenant_id: str, project_id: str) -> Optional[Dict]:
        """
        Atomically claim a pod using Kubernetes optimistic locking with resource versions.
        This ensures true atomicity by leveraging Kubernetes' built-in optimistic concurrency control.
        
        Args:
            pod: Pod dictionary containing pod information
            session_id: Session ID for tracking
            tenant_id: Tenant ID for tracking
            project_id: Project ID for tracking
            
        Returns:
            Pod dictionary if successfully claimed, None otherwise
        """
        pod_project_id = str(pod.get("project_id", "")).lower()
        configmap_name = f"pod-status-{pod_project_id}-{self.environment}"
        
        try:
            # Step 1: Try to read the current ConfigMap to get its resource version
            try:
                current_configmap = self.core_v1.read_namespaced_config_map(
                    name=configmap_name,
                    namespace=self.namespace
                )
                
                # Check if already used
                current_status = current_configmap.data.get("pod-status", "")
                if current_status == "used":
                    print(f"Pod {pod['name']} already used, status: {current_status}")
                    return None
                
                # Step 2: Create updated ConfigMap with resource version for optimistic locking
                updated_configmap = self._create_updated_configmap_with_resource_version(
                    current_configmap, session_id, tenant_id, project_id
                )
                
                # Step 3: Replace ConfigMap with resource version check (atomic operation)
                try:
                    self.core_v1.replace_namespaced_config_map(
                        name=configmap_name,
                        namespace=self.namespace,
                        body=updated_configmap
                    )
                    print(f"Successfully claimed pod {pod['name']} using optimistic locking")
                    return pod
                    
                except ApiException as e:
                    if e.status == 409:  # Conflict - resource version mismatch
                        print(f"Optimistic locking conflict for pod {pod['name']}: ConfigMap was modified by another process")
                        return None
                    else:
                        print(f"Error replacing ConfigMap for pod {pod['name']}: {e}")
                        return None
                
            except ApiException as e:
                if e.status == 404:
                    # ConfigMap doesn't exist, create it atomically
                    return self._create_configmap_atomic(configmap_name, session_id, tenant_id, project_id, pod)
                else:
                    print(f"Error reading ConfigMap for pod {pod['name']}: {e}")
                    return None
                    
        except Exception as e:
            print(f"Unexpected error claiming pod {pod['name']}: {e}")
            return None
    
    def _create_updated_configmap_with_resource_version(self, current_configmap, session_id: str, 
                                                        tenant_id: str, project_id: str) -> Dict:
        """
        Create an updated ConfigMap with the current resource version for optimistic locking.
        
        Args:
            current_configmap: Current ConfigMap object from Kubernetes
            session_id: Session ID for tracking
            tenant_id: Tenant ID for tracking  
            project_id: Project ID for tracking
            
        Returns:
            Updated ConfigMap dictionary
        """
        updated_configmap = {
            "apiVersion": "v1",
            "kind": "ConfigMap",
            "metadata": {
                "name": current_configmap.metadata.name,
                "namespace": current_configmap.metadata.namespace,
                "resourceVersion": current_configmap.metadata.resource_version,  # Critical for optimistic locking
                "uid": current_configmap.metadata.uid,
                "labels": dict(current_configmap.metadata.labels) if current_configmap.metadata.labels else {},
                "annotations": dict(current_configmap.metadata.annotations) if current_configmap.metadata.annotations else {}
            },
            "data": {
                "pod-status": "used",
                "session_id": session_id,
                "tenant_id": tenant_id,
                "project_id": project_id,
                "assigned_at": generate_timestamp(),
                "environment": self.environment,
                "claim_method": "optimistic_locking"
            }
        }
        
        return updated_configmap
    
    def _create_configmap_atomic(self, configmap_name: str, session_id: str, tenant_id: str, 
                                 project_id: str, pod: Dict) -> Optional[Dict]:
        """
        Atomically create a new ConfigMap for a pod that doesn't have one.
        
        Args:
            configmap_name: Name of the ConfigMap to create
            session_id: Session ID for tracking
            tenant_id: Tenant ID for tracking
            project_id: Project ID for tracking
            pod: Pod dictionary
            
        Returns:
            Pod dictionary if successfully created, None otherwise
        """
        configmap_body = {
            "apiVersion": "v1",
            "kind": "ConfigMap",
            "metadata": {
                "name": configmap_name,
                "namespace": self.namespace,
                "labels": {
                    "app": "codegen",
                    "environment": self.environment,
                    "pod-manager": "kubernetes-available-pods"
                }
            },
            "data": {
                "pod-status": "used",
                "session_id": session_id,
                "tenant_id": tenant_id,
                "project_id": project_id,
                "assigned_at": generate_timestamp(),
                "environment": self.environment,
                "claim_method": "create_new"
            }
        }
        
        try:
            self.core_v1.create_namespaced_config_map(
                namespace=self.namespace,
                body=configmap_body
            )
            print(f"Successfully created new ConfigMap for pod {pod['name']}")
            return pod
            
        except ApiException as e:
            if e.status == 409:  # Already exists (race condition)
                print(f"ConfigMap {configmap_name} already exists (created by another process)")
                return None
            else:
                print(f"Error creating ConfigMap for pod {pod['name']}: {e}")
                return None
    
    def release_pod(self, pod_name: str, session_id: str = None) -> bool:
        """
        Release a pod by marking it as available, with optional session validation.
        
        Args:
            pod_name: Name of the pod to release
            session_id: Optional session ID for validation
            
        Returns:
            True if successfully released, False otherwise
        """
        try:
            # Extract project ID from pod name
            match = re.match(r'([\w\d]+)-' + self.environment + r'-', pod_name)
            if not match:
                print(f"Could not extract project ID from pod name: {pod_name}")
                return False
                
            project_id = match.group(1)
            configmap_name = f"pod-status-{project_id}-{self.environment}"
            
            # Use optimistic locking for release as well
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Read current ConfigMap
                    current_configmap = self.core_v1.read_namespaced_config_map(
                        name=configmap_name,
                        namespace=self.namespace
                    )
                    
                    # Optional session validation
                    if session_id:
                        current_session = current_configmap.data.get("session_id", "")
                        if current_session != session_id:
                            print(f"Session ID mismatch for pod {pod_name}: expected {session_id}, got {current_session}")
                            return False
                    
                    # Create updated ConfigMap
                    updated_configmap = self._create_release_configmap_with_resource_version(
                        current_configmap, session_id
                    )
                    
                    # Replace with resource version check
                    self.core_v1.replace_namespaced_config_map(
                        name=configmap_name,
                        namespace=self.namespace,
                        body=updated_configmap
                    )
                    
                    print(f"Successfully released pod {pod_name}")
                    return True
                    
                except ApiException as e:
                    if e.status == 409:  # Conflict - retry
                        print(f"Conflict releasing pod {pod_name}, retrying... (attempt {attempt + 1})")
                        time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        print(f"Error releasing pod {pod_name}: {e}")
                        return False
                        
            print(f"Failed to release pod {pod_name} after {max_retries} attempts")
            return False
            
        except Exception as e:
            print(f"Unexpected error releasing pod {pod_name}: {e}")
            return False
    
    def _create_release_configmap_with_resource_version(self, current_configmap, session_id: str = None) -> Dict:
        """
        Create a ConfigMap for releasing a pod with optimistic locking.
        
        Args:
            current_configmap: Current ConfigMap object
            session_id: Optional session ID for tracking
            
        Returns:
            Updated ConfigMap dictionary
        """
        # Preserve existing data but mark as available
        existing_data = dict(current_configmap.data) if current_configmap.data else {}
        
        # Update status and add release information
        existing_data.update({
            "pod-status": "available",
            "released_at": generate_timestamp(),
            "released_by_session": session_id if session_id else existing_data.get("session_id", ""),
            "previous_session_id": existing_data.get("session_id", ""),
            "previous_tenant_id": existing_data.get("tenant_id", "")
        })
        
        # Remove active session data
        existing_data.pop("session_id", None)
        existing_data.pop("tenant_id", None)
        
        updated_configmap = {
            "apiVersion": "v1",
            "kind": "ConfigMap",
            "metadata": {
                "name": current_configmap.metadata.name,
                "namespace": current_configmap.metadata.namespace,
                "resourceVersion": current_configmap.metadata.resource_version,
                "uid": current_configmap.metadata.uid,
                "labels": dict(current_configmap.metadata.labels) if current_configmap.metadata.labels else {},
                "annotations": dict(current_configmap.metadata.annotations) if current_configmap.metadata.annotations else {}
            },
            "data": existing_data
        }
        
        return updated_configmap

    def get_one_available_pod_and_mark_as_used_legacy(self, session_id, tenant_id) -> Optional[Dict]:
        """Legacy method - kept for backward compatibility."""
        try:
            available_pods, count = self.get_available_codegen_pods()
            if count == 0:
                print("No available pods found")
                return None
            selected_pod = available_pods[0] if available_pods else None
            project_id = selected_pod.get("project_id", "")
            
            project_id = str(project_id).lower()
            configmap_name = f"pod-status-{project_id}-{self.environment}"
            print(f"ConfigMap name: {configmap_name}")
        
            # Create the strategic merge patch with session tracking
            patch_data = {
                "pod-status": "used",
                "session_id": session_id,
                "tenant_id": tenant_id,
                "assigned_at": generate_timestamp(),
                "environment": self.environment
            }
        
            patch_body = {"data": patch_data}
            
            # Print debug info
            print(f"Attempting to patch ConfigMap {configmap_name} in namespace {self.namespace} with data: {patch_data}")
            
            # Patch the ConfigMap with strategic merge
            result = self.core_v1.patch_namespaced_config_map(
                name=configmap_name,
                namespace=self.namespace,
                body=patch_body
            )
            
            print(f"ConfigMap {configmap_name} updated successfully with session tracking")
            return selected_pod
            
        except ApiException as e:
            print(f"Error updating ConfigMap: API error code {e.status}, reason: {e.reason}")
            print(f"API response body: {e.body}")
            return None
        except Exception as e:
            print(f"Unexpected error updating ConfigMap: {str(e)}")
            return None

    def _check_pod_readiness(self, pod) -> bool:
        """
        Check if a pod is fully ready (all containers are ready).
        
        Args:
            pod: Kubernetes pod object
            
        Returns:
            True if all containers are ready, False otherwise
        """
        try:
            # Check if pod is in Running phase
            if pod.status.phase != "Running":
                return False
            
            # Get container statuses
            container_statuses = pod.status.container_statuses or []
            
            if not container_statuses:
                return False
            
            # Check that all containers are ready
            all_ready = all(container.ready for container in container_statuses)
            
            # Calculate ready status for logging
            ready_containers = sum(1 for container in container_statuses if container.ready)
            total_containers = len(container_statuses)
            
            print(f"Pod readiness check: {ready_containers}/{total_containers} containers ready")
            
            return all_ready
            
        except Exception as e:
            print(f"Error checking pod readiness: {e}")
            return False
    
    def _check_pod_availability(self, configmap_name: str, pod_name: str) -> bool:
        """
        Check if a pod is available by examining its ConfigMap status.
        
        Args:
            configmap_name: Name of the ConfigMap to check
            pod_name: Name of the pod being checked
            
        Returns:
            True if pod is available, False otherwise
        """
        try:
            # Get the ConfigMap for this pod
            config_map = self.core_v1.read_namespaced_config_map(
                name=configmap_name,
                namespace=self.namespace
            )
            
            # Check pod status from ConfigMap
            pod_status = config_map.data.get("pod-status", "")
            print(f"Pod {pod_name} status from ConfigMap: {pod_status}")
            
            return pod_status == "available"
            
        except ApiException as api_e:
            if api_e.status == 404:
                # If ConfigMap doesn't exist, assume pod is available
                print(f"ConfigMap {configmap_name} not found, assuming pod is available")
                return False
            else:
                print(f"Error getting ConfigMap for pod {pod_name}: {api_e}")
                return False
        except Exception as e:
            print(f"Unexpected error checking ConfigMap for pod {pod_name}: {e}")
            return False
    
    def _extract_pod_details(self, pod, project_id: str) -> Dict:
        """
        Extract detailed information from a Kubernetes pod object.
        
        Args:
            pod: Kubernetes pod object
            project_id: Project ID extracted from pod name
            
        Returns:
            Dictionary containing pod details
        """
        # Get container statuses
        container_statuses = pod.status.container_statuses or []
        
        # Calculate ready status (all containers ready / total containers)
        ready_containers = sum(1 for container in container_statuses if container.ready)
        total_containers = len(container_statuses)
        ready = f"{ready_containers}/{total_containers}"
        
        # Get restart count (sum of all container restarts)
        restart_count = sum(container.restart_count for container in container_statuses)
        
        # Create comprehensive pod info dictionary
        pod_info = {
            # Basic pod information
            "name": pod.metadata.name,
            "ready": ready,
            "status": pod.status.phase,
            "restarts": restart_count,
            "age": pod.metadata.creation_timestamp.isoformat(),
            "ip": pod.status.pod_ip or "",
            "node": pod.spec.node_name or "",
            "project_id": project_id,
            "timestamp": datetime.datetime.utcnow().isoformat(),
            
            # Additional metadata
            "namespace": pod.metadata.namespace,
            "labels": dict(pod.metadata.labels) if pod.metadata.labels else {},
            "annotations": dict(pod.metadata.annotations) if pod.metadata.annotations else {},
            
            # Resource information
            "containers": [
                {
                    "name": container.name,
                    "image": container.image,
                    "ready": container.ready,
                    "restart_count": container.restart_count,
                    "state": self._get_container_state(container)
                }
                for container in container_statuses
            ],
            
            # Pod conditions
            "conditions": [
                {
                    "type": condition.type,
                    "status": condition.status,
                    "last_transition_time": condition.last_transition_time.isoformat() if condition.last_transition_time else None
                }
                for condition in (pod.status.conditions or [])
            ]
        }
        
        return pod_info
    
    def _get_container_state(self, container_status) -> Dict:
        """Extract container state information."""
        if container_status.state.running:
            return {
                "state": "running",
                "started_at": container_status.state.running.started_at.isoformat() if container_status.state.running.started_at else None
            }
        elif container_status.state.waiting:
            return {
                "state": "waiting",
                "reason": container_status.state.waiting.reason,
                "message": container_status.state.waiting.message
            }
        elif container_status.state.terminated:
            return {
                "state": "terminated",
                "reason": container_status.state.terminated.reason,
                "exit_code": container_status.state.terminated.exit_code,
                "finished_at": container_status.state.terminated.finished_at.isoformat() if container_status.state.terminated.finished_at else None
            }
        else:
            return {"state": "unknown"}
    
    def get_available_pods_by_project(self, project_id: str) -> List[Dict]:
        """
        Get available pods for a specific project.
        
        Args:
            project_id: The project ID to filter by
            
        Returns:
            List of available pod dictionaries for the project
        """
        all_pods, _ = self.get_available_codegen_pods()
        return [pod for pod in all_pods if pod.get('project_id') == project_id]
    
    def get_available_pods_summary(self) -> Dict:
        """
        Get a summary of available pods grouped by project.
        
        Returns:
            Dictionary with project summaries and total counts
        """
        pods, total_count = self.get_available_codegen_pods()
        
        # Group by project
        project_summary = {}
        for pod in pods:
            project_id = pod.get('project_id', 'unknown')
            
            if project_id not in project_summary:
                project_summary[project_id] = {
                    "project_id": project_id,
                    "pod_count": 0,
                    "pods": []
                }
            
            project_summary[project_id]["pod_count"] += 1
            project_summary[project_id]["pods"].append({
                "name": pod["name"],
                "status": pod["status"],
                "ready": pod["ready"],
                "node": pod["node"],
                "age": pod["age"]
            })
        
        return {
            "total_available_pods": total_count,
            "total_projects": len(project_summary),
            "projects": list(project_summary.values()),
            "timestamp": datetime.datetime.utcnow().isoformat()
        }
    
    def check_pod_health(self, pod_name: str) -> Dict:
        """
        Check the health status of a specific pod.
        
        Args:
            pod_name: Name of the pod to check
            
        Returns:
            Dictionary containing pod health information
        """
        try:
            pod = self.core_v1.read_namespaced_pod(
                name=pod_name,
                namespace=self.namespace
            )
            
            # Extract project ID
            match = re.match(r'([\w\d]+)-' + self.environment + r'-', pod_name)
            project_id = match.group(1) if match else 'unknown'
            
            # Check availability
            configmap_name = f"pod-status-{project_id}-{self.environment}"
            is_available = self._check_pod_availability(configmap_name, pod_name)
            is_ready = self._check_pod_readiness(pod)
            
            # Get detailed pod information
            pod_details = self._extract_pod_details(pod, project_id)
            
            return {
                "pod_name": pod_name,
                "is_available": is_available and is_ready,  # Both conditions must be true
                "is_configmap_available": is_available,
                "is_ready": is_ready,
                "health_status": "healthy" if pod.status.phase == "Running" and is_available and is_ready else "unhealthy",
                "details": pod_details
            }
            
        except ApiException as e:
            if e.status == 404:
                return {
                    "pod_name": pod_name,
                    "is_available": False,
                    "health_status": "not_found",
                    "error": "Pod not found"
                }
            else:
                return {
                    "pod_name": pod_name,
                    "is_available": False,
                    "health_status": "error",
                    "error": str(e)
                }


if os.getenv("BATCH_JOB_TRIGGER"):
    pass
else:
    environment, namespace = get_environment_and_namespace()
    print(f"Using environment: {environment}, namespace: {namespace}")
    if os.environ.get("LOCAL_DEBUG"):
        kubernetes_manager: KubernetesAvailablePodsManager = None
    else:
        kubernetes_manager: KubernetesAvailablePodsManager = KubernetesAvailablePodsManager(
            environment=environment, 
            namespace=namespace, 
            use_local_kubeconfig=settings.LOCAL_KUBERNETES_DEBUG
        )