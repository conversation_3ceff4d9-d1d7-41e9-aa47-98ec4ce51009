from code_generation_core_agent.agents.framework.cost_tracker import Cost<PERSON><PERSON><PERSON>, CircuitBreakerCallback
from app.core.websocket.client import WebSocketClient
from app.utils.datetime_utils import generate_timestamp
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent

class BackendCircuitBreakerCallback(CircuitBreakerCallback):
    """
    Implementation of CircuitBreakerCallback for the backend application.
    Handles different cost threshold events with appropriate logging.
    """

    def __init__(self, agent: TaskExecutionAgent, ws_client: WebSocketClient = None):
        """
        Initialize the callback with an optional logger.
        
        Args:
            logger: Optional logger instance. If not provided, will use standard logging.
        """
        self.agent = agent
        self.ws_client = ws_client
        pass
    
    def trigger_warning(self, limit: int):
        """
        Handle the warning threshold event.
        
        Args:
            limit: The warning cost limit that was reached
        """
        warning_message = f"⚠️ COST WARNING: Approaching cost limit of ${limit:.2f}"
        print(generate_timestamp(), warning_message)

        if self.ws_client:
            self.ws_client.send_message(
                "cost_warning",
                {
                    "message": warning_message,
                    "limit": limit,
                    "timestamp": generate_timestamp()
                }
            )

    
    def trigger_pause(self, limit: int):
        """
        Handle the pause threshold event.
        
        Args:
            limit: The pause cost limit that was reached
        """
        pause_message = f"⏸️ COST ALERT: Cost limit of ${limit:.2f} reached - operations will be throttled"
        print(generate_timestamp(), pause_message)
        
        if self.agent:
            try:
                self.agent.reporter.handle_merge_to_kavia_main_all_repos()
            except Exception as e:
                print(f"Error during merge to kavia main: {e}")

        if self.ws_client:
            self.ws_client.send_message(
                "cost_pause",
                {
                    "message": pause_message,
                    "limit": limit,
                    "timestamp": generate_timestamp()
                }
            )

    def trigger_error(self, limit: int):
        """
        Handle the error threshold event.
        
        Args:
            limit: The error cost limit that was reached
        """
        if self.agent:
            try:
                self.agent.reporter.handle_merge_to_kavia_main_all_repos()
            except Exception as e:
                print(f"Error during merge to kavia main: {e}")
        error_message = f"🛑 CRITICAL COST LIMIT: ${limit:.2f} exceeded - shutting down to prevent further costs"
        print(generate_timestamp(), error_message)
        print(generate_timestamp(), "System will terminate in 3 seconds to prevent additional costs")
        if self.ws_client:
            self.ws_client.send_message(
                "cost_error",
                {
                    "message": error_message,
                    "limit": limit,
                    "timestamp": generate_timestamp()
                }
            )
