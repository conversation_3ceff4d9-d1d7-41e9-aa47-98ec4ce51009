# app/core/code_generation.py

import asyncio
import json
import os
import time
import threading
from datetime import datetime
import yaml
import pkg_resources
import shutil
import requests
from code_generation_core_agent.agents.task_execution_agent import (
    TaskExecutionAgent,
    TaskExecutionControl
)
import yaml
from typing import Dict
from app.utils.aws.cognito_main import tenant_service
from app.utils.aws.cognito_user_manager import CognitoUserManager
from code_generation_core_agent.llm.llm_interface import LLMInterface
from code_generation_core_agent.telemetry.logger_factory import LoggerFactory
from app.core.Settings import settings
from app.core.constants import TASKS_COLLECTION_NAME, TaskStatus
from app.models.code_generation_model import Message, AgentParams
from app.utils.kg_build.sync_ebs_efs import do_sync, get_project_paths
from app.utils.respository_utils import _run_clone_in_background
from app.core.websocket.client import WebSocketClient
from queue import Queue, Empty
import asyncio
from app.connection.establish_db_connection import get_mongo_db
from app.core.git_tools import EnhancedGitTools
from app.utils.message_utils import send_agent_message_custom
import subprocess
from app.core.git_handlers import handle_branch_checkout
from app.core.git_controller import GitController
from app.connection.tenant_middleware import get_tenant_id
from app.utils.code_generation_utils import get_codegeneration_path, diffPath
from app.core.task_reporter import TaskReporter
from app.core.chat_controller import ChatInterfaceThread
from code_generation_core_agent.agents.micro.platform.platform_loader import PlatformConfigLoader
from app.utils.datetime_utils import generate_timestamp, utc_now
from code_generation_core_agent.llm.chat_interface import ChatInterface
from code_generation_core_agent.agents.utilities import ApplicationType, FrameworkType
from code_generation_core_agent.chat.chat_enabled_worker import ChatEnabledWorker
from app.models.scm import ACCESS_TOKEN_PATH
from app.routes.scm_route import scm_manager
from app.utils.hash import decrypt_string,decrypt_data
from app.classes.S3Handler import S3Handler
from app.models.user_model import LLMModel
from app.core.constants import CODE_GEN_ATTACHMENT_COLLECTION
from app.core.preview_controller import PreviewController
from app.utils.task_utils import get_codegen_url
import traceback
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.core.farget_deployment_controller import ECSDeploymentController
from app.core.deployment_controller import DeploymentController
from app.connection.establish_db_connection import get_node_db
import concurrent.futures
import nest_asyncio
from app.core.preview_controller import run_in_daemon_thread
from git import Repo
from app.utils.respository_utils import create_session_branches
from app.utils.time_utils import minutes_to_seconds

nest_asyncio.apply()



class AsyncSyncHelper:
    """Helper class to handle async operations in sync context"""
    
    @staticmethod
    def run_async_safe(coro):
        """Safely run async coroutine in sync context"""
        try:
            # Try to get existing event loop
            loop = asyncio.get_running_loop()
            # If we're in an event loop, use thread executor
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        except RuntimeError:
            # No event loop running, safe to use asyncio.run
            return asyncio.run(coro)

git_tool:EnhancedGitTools = None
input_arguments = os.environ.get("input_arguments", "{}")
input_arguments = json.loads(input_arguments)
db = None


DOCS_FOLDER = "kavia-docs"


AGENT_CONFIGS = {
    "ArchitectureCreation": {
        "config_file": "src/code_generation_core_agent/agents/micro/architecture_agent/architecture_agent.yaml",
        "prompts_path": "src/code_generation_core_agent/agents/micro/architecture_agent"
    },
    "CodeMaintenance": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    },
    "CodeGeneration": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    },
    "DocumentCreation": {
        "prompts_path": "src/code_generation_core_agent/agents/micro/prompts"
    }
}

# Create diff path if not exists
if not os.path.exists(diffPath):
    os.makedirs(diffPath)


def get_prompt_directory(agent_name=None):
    

    return pkg_resources.resource_filename(
        "code_generation_core_agent.agents.micro", 
        "prompts"
    )


def list_prompt_files():
    prompt_dir = get_prompt_directory()
    try:
        files = os.listdir(prompt_dir)
        print(generate_timestamp(),f"Files in {prompt_dir}:")
        for file in files:
            print(generate_timestamp(),f" - {file}")
    except Exception as e:
        print(generate_timestamp(),f"Error listing prompt directory: {e}")


def add_git_ignore_pattern(repo_path: str, pattern: str, use_gitignore: bool = False) -> None:
    """
    Add a pattern to git exclude or .gitignore file if not already present.

    Args:
        repo_path: Path to the git repository
        pattern: Pattern to ignore (e.g. 'logs/')
        use_gitignore: If True, adds to .gitignore, otherwise adds to git exclude
    """
    try:
        if use_gitignore:
            ignore_path = os.path.join(repo_path, '.gitignore')
        else:
            ignore_path = os.path.join(repo_path, '.git', 'info', 'exclude')
            os.makedirs(os.path.dirname(ignore_path), exist_ok=True)

        # Check if pattern already exists
        if os.path.exists(ignore_path):
            with open(ignore_path, 'r') as f:
                if pattern in f.read():
                    return

        # Add pattern to file
        with open(ignore_path, 'a') as f:
            f.write(f'\n{pattern}\n')
    except:
        pass


def has_real_files(directory: str) -> bool:
    """Check if directory contains any non-hidden files."""
    try:
        return any(
            not file.startswith('.')
            for file in os.listdir(directory)
            if os.path.isfile(os.path.join(directory, file))
        )
    except:
        return False

async def get_user_budget_usd(user_id: str, tenant_id: str) -> float:
    """
    Get user's remaining budget in USD based on their latest subscription
    
    Args:
        user_id (str): The user ID
        tenant_id (str): The tenant ID
        
    Returns:
        float: Remaining budget in USD
    """
    try:
        print(generate_timestamp(),f"💰 Getting budget for user: {user_id}, tenant: {tenant_id}")
        
        # Check if it's B2C tenant
        is_b2c = tenant_id == settings.KAVIA_B2C_CLIENT_ID
        print(generate_timestamp(),f"🏢 Is B2C tenant: {is_b2c}")
        
        if not is_b2c:
            print(generate_timestamp(),f"⚠️ Not a B2C tenant, returning unlimited budget")
            return float('inf')  # Unlimited budget for non-B2C
        
        # Get MongoDB connection for root database
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["active_subscriptions"]
        
        print(generate_timestamp(),f"🔍 Fetching latest subscription from database...")
        
        # Find the latest subscription for the user
        latest_subscription = collection.find_one(
            {"user_id": user_id},
            sort=[("created_at", -1)]
        )
        
        if not latest_subscription:
            print(generate_timestamp(),f"❌ No subscription found for user {user_id}, returning $0 budget")
            return 0.0
        
        print(generate_timestamp(),f"✅ Found subscription: {latest_subscription.get('_id')}")
        
        # Extract subscription details with None handling
        total_credits = latest_subscription.get("credits") or 0
        current_cost_str = latest_subscription.get("current_cost", "$0.00")
        
        print(generate_timestamp(),f"📊 Total credits: {total_credits}")
        print(generate_timestamp(),f"💸 Current cost string: {current_cost_str}")
        
        # Parse current cost (remove $ and convert to float)
        if current_cost_str and current_cost_str.startswith("$"):
            current_cost_usd = float(current_cost_str[1:])
        else:
            current_cost_usd = float(current_cost_str) if current_cost_str else 0.0
        
        print(generate_timestamp(),f"💵 Current cost (USD): {current_cost_usd}")
        
        # Calculate used credits (1 USD = 20,000 credits)
        CREDITS_PER_USD = 20000
        used_credits = current_cost_usd * CREDITS_PER_USD
        
        print(generate_timestamp(),f"🔢 Used credits: {current_cost_usd} * {CREDITS_PER_USD} = {used_credits}")
        
        # Calculate remaining credits
        remaining_credits = max(0, total_credits - used_credits)
        
        print(generate_timestamp(),f"🎯 Remaining credits: {total_credits} - {used_credits} = {remaining_credits}")
        
        # Convert remaining credits to USD budget
        budget_usd = remaining_credits / CREDITS_PER_USD
        
        print(generate_timestamp(),f"💰 Final budget: {remaining_credits} / {CREDITS_PER_USD} = ${budget_usd:.6f}")
        
        return round(budget_usd, 6)
        
    except Exception as e:
        print(generate_timestamp(),f"❌ Error calculating budget: {str(e)}")
        print(generate_timestamp(),f"🔍 Traceback: {traceback.format_exc()}")
        return 0.0  # Return 0 budget on error
    
####### --CONTROLLER-START-###########

class Controller(TaskExecutionControl):
    def __init__(self, task_id, agent: TaskExecutionAgent = None, ws_client: WebSocketClient = None):
        self.task_id = task_id
        self.agent = agent
        self._paused = False
        self._stopped = False
        self._rollback_requested = False
        self.ws_client = ws_client
        self._handler_running = False
        self._input_buffer = Queue()
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1  # Initial delay in seconds
        self._last_connection_check = utc_now()
        self._connection_check_interval = 20  # Check connection every 20 seconds
        self.git_controller = None
        self.deployment_controller = None
        self.container_deployment_controller = None
        self.preview_controller = None
        self.is_ready = False
        self.reporter = None
        self.model = LLMModel.bedrock_claude_3_5_sonnet.value
        self.docs_folder = self.get_docs_folder(task_id)
        self.docs_folder = f"deep_query_docs/{task_id}"
        
                # Add inactivity tracking
        self._last_activity = utc_now() # Initialize with current time
        self._inactivity_timeout = minutes_to_seconds(30)  
        self._inactivity_check_interval = minutes_to_seconds(1)
        self._last_inactivity_check = utc_now()
        self._inactivity_warning_threshold = minutes_to_seconds(15)
        self.s3_handler = None
        self.source_attachments_folder = self.get_attachments_folder()
        self.target_attachments_folder = get_codegeneration_path(gl_agent_params.agent_name, task_id) + "/attachments"
        self.chat_controller:ChatInterfaceThread = None
        
        self.initialize_s3()
        
    @run_in_daemon_thread
    def initialize_s3(self):
        try:
            self.s3_handler = S3Handler(tenant_id=get_tenant_id(), folder_name=self.docs_folder)
        except Exception as e:
            print(generate_timestamp(),f"Error initializing S3 handler: {e}")
            self.s3_handler = None
            
    @run_in_daemon_thread
    def _update_activity(self):
        """Update the last activity timestamp"""
        self._last_activity = utc_now()
        print(generate_timestamp(),f"Activity updated for task {self.task_id} at {(self._last_activity)}")

    def _check_inactivity(self):
        """Check if the session has been inactive for too long"""
        current_time = utc_now()
        inactive_duration = (current_time - self._last_activity).total_seconds()
        try:
            manifest_path = f"{self.agent.base_path}/.project_manifest.yaml"
            self.sync_manifest_to_project_node(manifest_path)
        except Exception as e:
            print(generate_timestamp(),f"Error syncing manifest to project node: {e}")
            pass
        
        if self._inactivity_warning_threshold <= inactive_duration:
            
            if self.ws_client and self.ws_client.connected:
                self.ws_client.send_message(
                    "timeout_warning", {"seconds_remaining":int(self._inactivity_timeout - inactive_duration),"terminated":False})

        if inactive_duration >= self._inactivity_timeout:
            if self.ws_client and self.ws_client.connected:
                self.ws_client.send_message(
                    "timeout_warning", {"seconds_remaining": 0,"terminated":True})
                self.ws_client.send_message(
                    "timeout_warning", {"seconds_remaining": 0,"terminated":True})
            print(generate_timestamp(),f"Time now ->", generate_timestamp(), "Last activity ->", self._last_activity, f"Session {self.task_id} has been inactive for {inactive_duration/3600:.2f} hours. Stopping session.")
            try:

                # Mark as stopped due to inactivity
                db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": self.task_id},
                    {"$set": {
                        "status": TaskStatus.STOPPED,
                        "execution_status": TaskStatus.STOPPED,
                        "stop_reason": f"Session stopped due to inactivity (no commands/interactions for {self._inactivity_timeout/3600:.1f} hours)"
                    }}
                )
                if gl_agent_params.agent_name != "DocumentCreation":
                    self.git_controller.handle_merge_to_kavia_main_all_repos(input_data={})
                    
                # Send WebSocket notification
                if self.ws_client and self.ws_client.connected:
                    self.ws_client.send_message(
                        "status_update", 
                        {
                            "status": TaskStatus.STOPPED,
                            "reason": "inactivity_timeout",
                            "message": f"Session automatically stopped after {self._inactivity_timeout/3600:.1f} hours of inactivity"
                        }
                    )
                
                # Kill the screen session containing this task
                self._kill_screen_session()
                
                # Stop the session - this will cause the process to exit
                # and the screen session will automatically terminate
                self._stopped = True
                
                # Raise an exception to terminate the process
                raise StopIteration(f"Session stopped due to inactivity after {self._inactivity_timeout/3600:.1f} hours")
                
            except StopIteration:
                # Re-raise StopIteration to properly terminate
                raise
            except Exception as e:
                print(generate_timestamp(),f"Error stopping inactive session {self.task_id}: {e}")
                # Still try to stop the process
                self._stopped = True
                raise StopIteration(f"Session stopped due to inactivity (with error: {e})")
                
        return False
    
    def _kill_screen_session(self):
        """Kill the screen session containing this task ID"""
        try:
            # Get list of screen sessions
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # Find sessions containing our task_id
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            killed_sessions = []
            
            for line in session_lines:
                # Check if this session contains our task_id
                if self.task_id in line and not line.endswith('(Dead)'):
                    # Extract the full session ID (PID.name)
                    parts = line.strip().split('\t')
                    session_id = parts[0].strip()
                    
                    try:
                        # Kill the screen session
                        subprocess.run(
                            ['screen', '-S', session_id, '-X', 'quit'],
                            check=True,
                            capture_output=True,
                            text=True
                        )
                        killed_sessions.append(session_id)
                        print(generate_timestamp(),f"Successfully killed screen session: {session_id}")
                    except subprocess.CalledProcessError:
                        # If quit doesn't work, try force kill
                        try:
                            subprocess.run(
                                ['screen', '-S', session_id, '-X', 'kill'],
                                check=True,
                                capture_output=True,
                                text=True
                            )
                            killed_sessions.append(session_id)
                            print(generate_timestamp(),f"Force killed screen session: {session_id}")
                        except subprocess.CalledProcessError as e:
                            print(generate_timestamp(),f"Failed to kill screen session {session_id}: {e}")
            
            if killed_sessions:
                print(generate_timestamp(),f"Killed {len(killed_sessions)} screen session(s) for task {self.task_id}")
            else:
                print(generate_timestamp(),f"No active screen sessions found for task {self.task_id}")
            
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                print(generate_timestamp(),"No screen sessions found")
            else:
                print(generate_timestamp(),f"Error checking screen sessions: {e.output}")
        except Exception as e:
            print(generate_timestamp(),f"Unexpected error killing screen sessions: {str(e)}")
    
    def get_docs_folder(self, task_id):
        if(task_id.startswith("deep-query")):
            return f"deep_query_docs/{task_id}"
        elif(task_id.startswith("cm")):
            return f"code_maintenance_docs/{task_id}"
        else:
            return f"code_generation_docs/{task_id}"
    
    def initialize_attachments_folder(self):
        try:
            if not os.path.exists(self.attachments_folder):
                os.makedirs(self.attachments_folder)
        except Exception as e:
            print(generate_timestamp(),f"Error initializing attachments folder: {e}")
            pass
        
    @run_in_daemon_thread
    def sync_attachments_folder(self):
        """Sync the attachments folder to the codegeneration workspace path using rsync."""
        try:
            source_path = self.get_attachments_folder()
            target_path = os.path.join(get_codegeneration_path(gl_agent_params.agent_name, self.task_id), "attachments")
            
            # Create the target directory if it doesn't exist
            try:
                if not os.path.exists(target_path):
                    os.makedirs(target_path, exist_ok=True)
            except OSError as e:
                print(generate_timestamp(),f"Failed to create target directory {target_path}: {str(e)}")
                return False
            
            # Use rsync to sync the files
            try:
                # Ensure source path ends with slash for rsync
                if not source_path.endswith('/'):
                    source_path += '/'
                
                # Run rsync with appropriate options
                # -a: archive mode (preserves permissions, etc.)
                # -v: verbose
                # -z: compression
                # --delete: delete files in target that are not in source
                rsync_command = ["rsync", "-avz", "--delete", source_path, target_path]
                
                process = subprocess.run(
                    rsync_command,
                    capture_output=True,
                    text=True
                )
                
                if process.returncode != 0:
                    # print(generate_timestamp(),f"Rsync error output: {process.stderr}")
                    return False
                
                print(generate_timestamp(),f"Successfully synced attachments folder from {source_path} to {target_path}")
                return True
                
            except Exception as e:
                # print(generate_timestamp(),f"Error during rsync operation: {str(e)}")
                return False
        
        except Exception as e:
            # print(generate_timestamp(),f"Unexpected error in sync_attachments_folder: {str(e)}")
            return False
    
    @run_in_daemon_thread
    def sync_figmafiles_folder(self):
        """Sync the attachments folder to the codegeneration workspace path using rsync."""
        try:
            source_path = self.get_figmafiles_folder()
            target_path = os.path.join(get_codegeneration_path(gl_agent_params.agent_name, self.task_id), "figmafiles")
            
            # Create the target directory if it doesn't exist
            try:
                if not os.path.exists(target_path):
                    os.makedirs(target_path, exist_ok=True)
            except OSError as e:
                print(generate_timestamp(),f"Failed to create target directory {target_path}: {str(e)}")
                return False
            
            # Use rsync to sync the files
            try:
                # Ensure source path ends with slash for rsync
                if not source_path.endswith('/'):
                    source_path += '/'
                
                # Run rsync with appropriate options
                # -a: archive mode (preserves permissions, etc.)
                # -v: verbose
                # -z: compression
                # --delete: delete files in target that are not in source
                rsync_command = ["rsync", "-avz", "--delete", source_path, target_path]
                
                process = subprocess.run(
                    rsync_command,
                    capture_output=True,
                    text=True
                )
                
                if process.returncode != 0:
                    # print(generate_timestamp(),f"Rsync error output: {process.stderr}")
                    return False
                
                print(generate_timestamp(),f"Successfully synced figmafiles folder from {source_path} to {target_path}")
                return True
                
            except Exception as e:
                # print(generate_timestamp(),f"Error during rsync operation: {str(e)}")
                return False
        
        except Exception as e:
            # print(generate_timestamp(),f"Unexpected error in sync_attachments_folder: {str(e)}")
            return False

    def get_attachments_folder(self):
        if os.environ.get("LOCAL_DEBUG"):
            return f"/tmp/{input_arguments.get('tenant_id')}/{input_arguments.get('project_id')}/workspace/attachments/"
        return f"/efs/{input_arguments.get('tenant_id')}/{input_arguments.get('project_id')}/workspace/attachments/"

    def get_figmafiles_folder(self):
        if os.environ.get("LOCAL_DEBUG"):
            return f"/tmp/{input_arguments.get('tenant_id')}/{input_arguments.get('project_id')}/workspace/figmafiles/"
        return f"/efs/{input_arguments.get('tenant_id')}/{input_arguments.get('project_id')}/workspace/figmafiles/"
    
    def get_attachments(self):
        self.sync_attachments_folder()
        query = {
            "project_id": input_arguments.get('project_id'),
            "isDeleted": {"$ne": True}
        }
        projection = {
            "_id": 0
        }
        attachments = db[CODE_GEN_ATTACHMENT_COLLECTION].find(query, projection)
        return list(attachments)
        
    def get_attachment_by_id(self, attachment_id):
        """
        Get a specific attachment by ID
        
        Args:
            attachment_id (str): The ID of the attachment to retrieve
            
        Returns:
            dict: The attachment details if found, or None
        """

        self.sync_attachments_folder()

        attachments = self.get_attachments()
        return next((attachment for attachment in attachments if attachment['attachment_id'] == attachment_id), None)
    
    def set_is_ready(self, is_ready):
        self.is_ready = is_ready

    def get_is_ready(self):
        return self.is_ready

    def set_reporter(self, reporter):
        self.reporter = reporter

    def set_chat_controller(self, chat_controller):
        self.chat_controller = chat_controller

    def set_git_controller(self, git_controller):
        self.git_controller = git_controller

    def set_deployment_controller(self, deployment_controller):
        print(generate_timestamp(),"Inside the set Deployment controller")
        self.deployment_controller = deployment_controller

    def set_container_deployment_controller(self, container_deployment_controller):
        print(generate_timestamp(),"Inside the set_container_deployment_controller ")
        self.container_deployment_controller = container_deployment_controller
        
    def set_preview_controller(self, preview_controller):
        print(generate_timestamp(),"Inside the set preview controller")
        self.preview_controller = preview_controller

    def initialize(self):
        if self._try_connect():
            self.ws_client.add_message_handler(self._handle_message)
            self.ws_client.start_message_handler()
            # # Start connection monitoring in a separate thread
            self._connection_monitor_thread = threading.Thread(
                target=self._monitor_connection, daemon=True)
            self._connection_monitor_thread.start()
            
            
    def pause(self):
        """Implementation of abstract pause method"""
        self._paused = True
        db[TASKS_COLLECTION_NAME].update_one(
            {"_id": self.task_id},
            {"$set": {"status": TaskStatus.PAUSED,
                      "execution_status": TaskStatus.PAUSED}}
        )
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message(
                "status_update", {"status": TaskStatus.PAUSED})

    def resume(self):
        """Implementation of abstract resume method"""
        self._paused = False
        db[TASKS_COLLECTION_NAME].update_one(
            {"_id": self.task_id},
            {"$set": {"status": TaskStatus.RUNNING,
                      "execution_status": TaskStatus.RUNNING}}
        )
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message(
                "status_update", {"status": TaskStatus.RUNNING})

    def stop_screen_session(self):
        try:
            print(generate_timestamp(),"Starting stop_screen_session method")
            print(generate_timestamp(),f"Loaded input arguments: {input_arguments}")
            
            project_id = input_arguments.get("project_id")
            task_id = self.task_id
            agent_name = input_arguments.get("agent_name")
            stage = input_arguments.get("stage")
            container_id = input_arguments.get("container_id")
            
            print(generate_timestamp(),f"Project ID: {project_id}, Task ID: {task_id}")
            print(generate_timestamp(),f"Agent name: {agent_name}, Stage: {stage}")
            print(generate_timestamp(),f"Container ID: {container_id}")
            
            # Validate required parameters
            if not stage:
                print("Warning: Stage parameter is missing, defaulting to 'dev'")
                stage = "dev"
            
            is_maintenance = False
            is_document_creation = False
            
            if self.task_id.startswith("cm"):
                is_maintenance = True
                print(generate_timestamp(),"Task identified as maintenance task")
            if self.task_id.startswith("deep-query"):
                is_document_creation = True
                print(generate_timestamp(),"Task identified as document creation task")
            
            tenant_id = get_tenant_id()
            print(generate_timestamp(),f"Tenant ID: {tenant_id}")
            base_url = get_codegen_url(stage=stage, pod_prefix=os.environ.get("PROJECT_ID", ""))
            print(generate_timestamp(),f"Base URL: {base_url}")
            stop_url = f'{base_url}/stop'

            if not is_maintenance and not is_document_creation:
                stop_url += f"?container_id={container_id}"
                print(generate_timestamp(),f"Using container_id parameter: {container_id}")
            else:
                stop_url += f"?project_id={project_id}&task_id={task_id}&agent_name={agent_name}"
                print(generate_timestamp(),f"Using project/task/agent parameters")
            
            print(generate_timestamp(),f"Calling stop URL: {stop_url}")
            result = requests.get(stop_url, timeout=10, verify=False)
            print(generate_timestamp(),f"Stop request status code: {result.status_code}")
            response = result.json()
            print(generate_timestamp(),f"Stop response: {response}")
            return response
        except Exception as e:
            print(generate_timestamp(),f"Error stopping screen session: {e}")
            print(generate_timestamp(),f"Error type: {type(e).__name__}")
            print(generate_timestamp(),f"Error traceback: {traceback.format_exc()}")
            print(f"Warning: Error stopping screen session: {e}")
            print(f"Error type: {type(e).__name__}")
            print(f"Note: This is a non-critical error - main stop functionality still works")
            # Don't print full traceback unless in debug mode
            if os.environ.get("DEBUG"):
                print(f"Error traceback: {traceback.format_exc()}")
            return None

    @run_in_daemon_thread
    def sync_manifest_to_project_node(self, manifest_path):
        if gl_agent_params.agent_name == "DocumentCreation":
            # For CodeMaintenance, we don't sync the manifest to the project node
            return
        
        with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_data = yaml.safe_load(f)
        node_db = get_node_db()
        project_id = input_arguments.get("project_id")
        loop = asyncio.get_event_loop()
    
   
        project = loop.run_until_complete(
        node_db.get_node_by_id(project_id, node_type="Project")
    )
       
        loop.run_until_complete(node_db.update_node_by_id(project_id, {'Manifest': json.dumps(manifest_data)}))

    def stop(self):
        
        try:
            result = asyncio.run(get_project_paths(project_id=input_arguments.get("project_id")))
            for path_info in result["paths"]:
                source_path = path_info["source_path"]
                build_path = path_info["destination_path"]
                do_sync(source_path,build_path )
        except:
            pass
        
        """Implementation of abstract stop method"""
        self._stopped = True
        self.agent.stop()
        try:
            mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
            mongo_db.db["session_tracking"].update_one(
                {"task_id": self.task_id},
                {
                    "$set": {
                        "status": TaskStatus.STOPPED
                    }
                }
            )
            
        except Exception as e:
            print(generate_timestamp(),f"Error ending session: {e}")
        manifest_path = f"{self.agent.base_path}/.project_manifest.yaml"
        self.sync_manifest_to_project_node(manifest_path)
        db[TASKS_COLLECTION_NAME].update_one(
            {"_id": self.task_id},
            {"$set": {
                "status": TaskStatus.STOPPED,
                "execution_status": TaskStatus.STOPPED,
                "stop_reason": "Code generation stopped by user.",
                "previous_request_context": {
                    "request_context": self.agent.request_context,
                    "messages": self.agent.messages,
                    "task_report": self.agent.task_report
                }
            }}
        )
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message(
                "status_update", {"status": TaskStatus.STOPPED})

        self.stop_screen_session();
        raise StopIteration("Code generation stopped by user.")

    def stop_connection_monitor(self):
        self._connection_monitor_thread.join()

    def rollback(self, commit_hash=None):
        self._rollback_requested = True
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message(
                "status_update", {"status": "rollback"})

    def reset(self):
        """Implementation of abstract reset method"""
        self._paused = False
        self._stopped = False
        db[TASKS_COLLECTION_NAME].update_one(
            {"_id": self.task_id},
            {"$set": {
                "status": TaskStatus.RUNNING,
                "execution_status": TaskStatus.RUNNING
            }}
        )
        if self.ws_client and self.ws_client.connected:
            self.ws_client.send_message(
                "status_update", {"status": TaskStatus.RUNNING})

    def _try_connect(self):
        """Attempt to connect with retry logic"""
        while self._reconnect_attempts < self._max_reconnect_attempts:
            try:
                if self.ws_client.connect():
                    self._reconnect_attempts = 0  # Reset counter on successful connection
                    self._reconnect_delay = 1  # Reset delay
                    return True

            except Exception as e:
                self._reconnect_attempts += 1
                # print(generate_timestamp(),f"Connection attempt {self._reconnect_attempts} failed: {e}")

                if self._reconnect_attempts < self._max_reconnect_attempts:
                    time.sleep(self._reconnect_delay)
                    # Exponential backoff, max 30 seconds
                    self._reconnect_delay = min(self._reconnect_delay * 2, 30)

        print(generate_timestamp(),"Failed to establish WebSocket connection after maximum attempts")
        return False
    

    def _monitor_connection(self):
        """Continuously monitor WebSocket connection"""
        while not self._stopped:
            current_time = utc_now()
            
            if (current_time - self._last_inactivity_check).total_seconds() >= self._inactivity_check_interval:
                self._last_inactivity_check = current_time
                
                try:
                    if self._check_inactivity():
                        print(generate_timestamp(),f"Session {self.task_id} terminated due to inactivity")
                        # Break out of the monitoring loop to terminate the process
                        break
                except Exception as e:
                    print(generate_timestamp(),f"Error checking inactivity: {e}")

            if (current_time - self._last_inactivity_check).total_seconds() >= self._inactivity_check_interval:
                self._last_connection_check = current_time

                if not self.ws_client.connected:
                    if self._reconnect_attempts < self._max_reconnect_attempts:
                        print(generate_timestamp(),"Attempting to reconnect...")
                        if self._try_connect():
                            print(generate_timestamp(),"Reconnected successfully")
                            self._reconnect_attempts = 0
                            self._reconnect_delay = 1
                            return
                        else:
                            print(generate_timestamp(),"Failed to reconnect, stopping connection monitor")
                            break
                       
                    db[TASKS_COLLECTION_NAME].update_one(
                        {"_id": self.task_id},
                        {"$set": {
                            "status": TaskStatus.FAILED,
                            "execution_status": TaskStatus.FAILED
                        }}
                    )

                    task = db[TASKS_COLLECTION_NAME].find_one(
                        {"_id": self.task_id}, {"start_time": 1, "retry_count": 1, "user_id": 1})
                    retry_count = task.get("retry_count", 0)
                    start_time = task.get("start_time")
                    current_time = generate_timestamp()
                    
                    try:
                        if not start_time:
                            print(generate_timestamp(),"No start time found, skipping retry")
                            return

                        # Convert ISO format strings to datetime objects for comparison
                        start_datetime = datetime.fromisoformat(start_time)
                        current_datetime = datetime.fromisoformat(current_time)

                        # Calculate time difference in minutes
                        time_diff_minutes = (
                            current_datetime - start_datetime).total_seconds() / 60

                        # Retry if task is less than 10 minutes old
                        if time_diff_minutes < 10 and not os.environ.get("LOCAL_DEBUG") and retry_count < 3:
                            retry_count += 1
                            current_env = os.environ.copy()

                            screen_cmd = ['python', 'app/batch_jobs/jobs.py']

                            try:
                                db[TASKS_COLLECTION_NAME].update_one(
                                    {"_id": self.task_id},
                                    {"$set": {
                                        "retry_count": retry_count,
                                        "status": TaskStatus.RETRY,
                                        "execution_status": TaskStatus.RETRY
                                    }}
                                )
                                # Run subprocess and wait for completion
                                subprocess.run(
                                    screen_cmd,
                                    check=True,
                                    env=current_env
                                )
                                print(generate_timestamp(),"Batch jobs completed")
                            except subprocess.CalledProcessError as e:
                                print(generate_timestamp(),f"Error running batch jobs: {e}")
                        else:
                            print(generate_timestamp(),
                                f"Task too old for retry ({time_diff_minutes:.1f} minutes)")

                            self.stop_connection_monitor()

                    except (ValueError, TypeError) as e:
                        print(generate_timestamp(),f"Error processing timestamps: {e}")
                        return
                    except Exception as e:
                        print(generate_timestamp(),f"Unexpected error during retry check: {e}")
                        return
            
            self.ws_client.start_message_handler()

            time.sleep(3)  # Prevent excessive CPU usage

    async def get_documents(self):
        print(generate_timestamp(),"self.docs_folder",self.docs_folder)
        documents= self.s3_handler.list_all_filenames(self.docs_folder)
        print(generate_timestamp(),"documents",documents)
        if documents:
            self.ws_client.send_message(message_type="existing_document_names", data=documents)
        else:
            self.ws_client.send_message(message_type="existing_document_names", data=[])
        return

    async def get_document_content(self, title):
        print(generate_timestamp(),"title",title)
        content = self.s3_handler.get_file(title)
        print(generate_timestamp(),"content",content)
        if content:
            content = content.decode('utf-8')
            self.ws_client.send_message(message_type="document_content", data={'title': title, 'content': content})
        else:
            self.ws_client.send_message(message_type="document_content", data={'title': title, 'content': "# No Content Found"})
        return
    
    @run_in_daemon_thread
    def _handle_chat_message(self, message):
       self.chat_controller._handle_chat_message(message) 

    async def _handle_message(self, message):
        """Handle incoming WebSocket messages"""
        try:
            self._handle_chat_message(message)
            self._update_activity()
            self.sync_attachments_folder()
            command = message.get('type')
            input_data = message.get('input_data')
            tenant_id = get_tenant_id()
            project_id = input_arguments.get("project_id")
            attachments = message.get('attachments', None)
            has_figma_json = False
            figma_file_keys = set()

            if attachments:
                for attachment in attachments:
                    if attachment.get('file_type') == 'figma_json':
                        has_figma_json = True
                        file_key = attachment.get('fileKey')
                        if file_key:
                            figma_file_keys.add(file_key)

            # Convert set to list if needed
            figma_ids = list(figma_file_keys)
            
            if has_figma_json and figma_ids:  # Also check if figma_ids is not empty
                base_path = "/efs"
                if os.environ.get("LOCAL_DEBUG"):
                    base_path = "/tmp"
                
                # dest_path = f"{code_gen_path}/attachments"
                dest_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figmafiles"
                
                # Create destination directory once
                try:
                    os.makedirs(dest_path, exist_ok=True)
                except Exception as e:
                    print(f"Error creating destination directory: {str(e)}")
                    return  # or handle appropriately
                
                # Process each figma_id
                for figma_id in figma_ids:
                    source_path = f"{base_path}/{tenant_id}/{project_id}/workspace/figma_json/{figma_id}"
                    
                    try:
                        if os.path.exists(source_path) and os.path.isdir(source_path):
                            json_files = [f for f in os.listdir(source_path) if f.endswith(".json")]
                            
                            if not json_files:
                                print(f"No JSON files found in {source_path}")
                                continue
                            
                            # Copy all JSON files
                            for file_name in json_files:
                                source_file = os.path.join(source_path, file_name)
                                dest_file = os.path.join(dest_path, file_name)
                                
                                try:
                                    shutil.copy2(source_file, dest_file)
                                    print(f"Copied: {file_name}")
                                except Exception as file_error:
                                    print(f"Error copying {file_name}: {str(file_error)}")
                            
                            print(f"Processed {len(json_files)} JSON files from {figma_id}")
                                    
                        else:
                            print(f"Source path does not exist or is not a directory: {source_path}")
                            
                    except Exception as e:
                        print(f"Error processing figma_id {figma_id}: {str(e)}")
            try:
                self.sync_figmafiles_folder()
            except Exception as e:
                print(f"Error in sync figma files {e}")

            if command == 'ready_check':
                if self.get_is_ready():
                    self.ws_client.send_message(
                        "ready_check", {"status": "ready"})
                    self.ws_client.send_message(
                        "current_model", {"model": self.agent.get_selected_model()})
                    self.ws_client.send_message("available_models", {"models": self.agent.get_available_models()})
                return
                    
            if command == 'set_model':
                try:
                    model = message.get('model')
                    self.agent.set_model(model)
                    self.ws_client.send_message(
                        "model_set", {"model": model})
                    print(generate_timestamp(),f"Model set to {model}")
                except Exception as e:
                    print(generate_timestamp(),f"Error setting model: {e}")
                    self.ws_client.send_message(
                        "model_set", {"error": f"Error setting model: {e}"})
                return

            if command == 'get_current_model':
                try:
                    current_model = self.agent.get_selected_model()
                    self.ws_client.send_message(
                        "current_model", {"model": current_model})
                    available_models = self.agent.get_available_models()
                    self.ws_client.send_message(
                        "available_models", {"models": available_models})
                except Exception as e:
                    print(generate_timestamp(),f"Error getting current model: {e}")
                    self.ws_client.send_message(
                        "current_model", {"error": f"Error getting current model: {e}"})
                return
                    
            if command == 'get_available_models':
                try:
                    available_models = self.agent.get_available_models()
                    self.ws_client.send_message(
                        "available_models", {"models": available_models})
                except Exception as e:
                    print(generate_timestamp(),f"Error getting available models: {e}")
                    self.ws_client.send_message(
                        "available_models", {"error": f"Error getting available models: {e}"})
                return
            
            if command == 'get_documents':
                await self.get_documents()
                return

            if command == 'get_document_content':
                title = message.get('title', '')
                if title:
                    await self.get_document_content(title)
                return
            
            if command == 'get_attachments':
                try:
                    print(generate_timestamp(),"Getting attachments")
                    attachments = self.get_attachments()
                    self.ws_client.send_message(message_type="attachments", data=attachments)
                except Exception as e:
                    print(generate_timestamp(),f"Error getting attachments: {e}")
                    self.ws_client.send_message(message_type="attachments", data={'error': f"Error getting attachments: {e}"})
                return

            if command == 'get_attachment_by_id':
                try:
                    attachment_id = message.get('attachment_id')
                    if attachment_id:
                        attachment = self.get_attachment_by_id(attachment_id)
                        if attachment:
                            self.ws_client.send_message(message_type="attachment_detail", data=attachment)
                        else:
                            self.ws_client.send_message(message_type="attachment_detail", 
                                                      data={'error': f"Attachment with ID {attachment_id} not found"})
                    else:
                        self.ws_client.send_message(message_type="attachment_detail", 
                                                  data={'error': "No attachment ID provided"})
                except Exception as e:
                    print(generate_timestamp(),f"Error getting attachment by ID: {e}")
                    self.ws_client.send_message(message_type="attachment_detail", 
                                              data={'error': f"Error getting attachment by ID: {e}"})
                return
            
            if command == 'stop_streaming':
                # Stop streaming
                try:
                    self.agent.stop_streaming()
                    self.ws_client.send_message(message_type="stop_streaming", data={'status': 'success'})
                except Exception as e:
                    print(generate_timestamp(),f"Error stopping streaming: {e}")
                    self.ws_client.send_message(message_type="stop_streaming", data={'error': f"Error stopping streaming: {e}"})
                return

            if command == 'user_input':
                user_input = message.get('input')
                if user_input:
                    # Process input immediately
                    print(generate_timestamp(),"HANDLING USER MESSAGE:", user_input)
                    self._handle_user_input(user_input, message.get("user_id"))
                return
            if command == "send_message":
                return

            # Handle control commands
            print(generate_timestamp(),f"🔍 Processing command: '{command}' with input_data: {input_data}")
            await self._handle_control_command(command, input_data=input_data)

        except Exception as e:
            print(generate_timestamp(),f"Error handling message in controller: {e}")

    def _handle_user_input(self, user_input, user_id="admin"):
        """Process user input immediately"""
        try:

            # Notify clients
            if self.ws_client and self.ws_client.connected:
                self.ws_client.send_message("input_received", {
                    "status": "received",
                    "user_input": user_input,
                    "user_id": user_id, 
                    "timestamp": generate_timestamp()
                })
            # Update task status
            self._update_task_status(user_input, user_id)

        except Exception as e:
            print(generate_timestamp(),f"Error processing user input: {e}")

    async def _handle_control_command(self, command, input_data=None):
        """Handle control commands with proper state management"""
        try:
            # Handle non-git commands
            if command == 'pause':
                self.pause()
            elif command == 'resume':
                self.resume()
            elif command == 'stop':
                self.stop()
            elif command == 'rollback':
                self.rollback()
            elif command == 'reset':
                self.reset()
            # Try to handle as git command
            elif self.git_controller and self.git_controller.handle_command(command, input_data):
                print(generate_timestamp(),f"✅ Git command '{command}' handled successfully")
                pass
            # Try to handle as deployment command
            elif self.container_deployment_controller and self.container_deployment_controller.handle_command(command, input_data):
                print(generate_timestamp(),f"✅ Conatiner Deployment command '{command}' handled successfully")
                pass

            elif self.deployment_controller and self.deployment_controller.handle_command(command, input_data):
                print(generate_timestamp(),f"✅ Deployment command '{command}' handled successfully")
                pass
            
            
            elif self.preview_controller and self.preview_controller.handle_command(command, input_data):
                print(generate_timestamp(),f"✅ Preview command '{command}' handled successfully")
                pass
            
            else:
                print(generate_timestamp(),f"❌ Command '{command}' not handled by any controller")
                print(generate_timestamp(),f"   - Git controller exists: {self.git_controller is not None}")
                print(generate_timestamp(),f"   - Deployment controller exists: {self.deployment_controller is not None}")
                print(generate_timestamp(),f"   - Conatiner Deployment controller exists: {self.container_deployment_controller is not None}")
                if self.git_controller:
                    print(generate_timestamp(),f"   - Available git commands: {list(self.git_controller._command_map.keys())}")
                if self.deployment_controller:
                    print(generate_timestamp(),f"   - Available deployment commands: {list(self.deployment_controller._command_map.keys())}")
                if self.container_deployment_controller:
                    print(generate_timestamp(),f"   - Available deployment commands: {list(self.container_deployment_controller._command_map.keys())}")

            # Notify clients of state change
            if self.ws_client and self.ws_client.connected:
                self.ws_client.send_message("status_update", {
                    "status": self._get_current_status(),
                    "command": command
                })

        except Exception as e:
            print(generate_timestamp(),f"Error handling control command: {e}")
    
    @run_in_daemon_thread
    def _update_task_status(self, user_input, user_id: str = "admin"):
        """Update task status in database"""
        try:
            message_obj = Message(content=user_input, sender="User", timestamp=generate_timestamp(), user_id = user_id)
            db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {"$push": {"messages": message_obj.to_dict()}}
            )
        except Exception as e:
            print(generate_timestamp(),f"Error updating task status: {e}")

    def _get_current_status(self):
        """Get current execution status"""
        if self._stopped:
            return TaskStatus.STOPPED
        elif self._paused:
            return TaskStatus.PAUSED
        else:
            return TaskStatus.RUNNING

    def check_status(self):
        """Enhanced status checking with connection monitoring"""
        try:
            # Check database status
            task = db[TASKS_COLLECTION_NAME].find_one(
                {"_id": self.task_id},
                {"status": 1, "execution_status": 1}
            )
            execution_status = task.get("execution_status", TaskStatus.RUNNING)

            # Process any pending inputs
            while not self._input_buffer.empty():
                try:
                    input_data = self._input_buffer.get_nowait()
                    if self.agent:
                        self.agent.set_user_input(input_data)
                except Empty:
                    break
            if self._rollback_requested:
                result = self.agent.git_tool.git_revert(
                    commit_hash=None, repository_path=self.agent.base_path)
                self.ws_client.send_message("status_update", {
                    "type": "rollback",
                    "description": result
                })

                task = db[TASKS_COLLECTION_NAME].find_one(
                    {"_id": self.task_id})
                if task and 'past_steps' in task and len(task['past_steps']) > 0:
                    # Update the last step
                    last_step_index = len(task['past_steps']) - 1
                    db[TASKS_COLLECTION_NAME].update_one(
                        {"_id": self.task_id},
                        {"$set": {f"past_steps.{last_step_index}.reverted": True}}
                    )

                print(generate_timestamp(),"Rollback successfull")
                self._rollback_requested = False
            # Handle execution status
            if execution_status == TaskStatus.PAUSED:
                self.pause()
            elif execution_status == TaskStatus.RUNNING:
                self.resume()
            elif execution_status == TaskStatus.STOPPED:
                self.stop()

            if self._paused:
                while self._paused and not self._stopped:
                    time.sleep(0.1)
                    # Continue checking for new inputs while paused
                    while not self._input_buffer.empty():
                        try:
                            input_data = self._input_buffer.get_nowait()
                            if self.agent:
                                self.agent.set_user_input(input_data)
                        except Empty:
                            break

            if self._stopped:
                raise StopIteration("Code generation cancelled.")

        except Exception as e:
            print(generate_timestamp(),f"Error in check_status: {e}")
            raise



####### --CONTROLLER-END-###########


class CodeGenerationAgentManager:
    _instance = None

    def __new__(cls, platform=None, micro_agents_config=None, chat_interface:ChatInterface=None):
        if cls._instance is None:
            cls._instance = super(CodeGenerationAgentManager, cls).__new__(cls)
            cls._instance._initialize(platform, micro_agents_config, chat_interface=chat_interface)
        
        cls._instance.platform = platform
        cls._instance.micro_agents_config = micro_agents_config
        
        return cls._instance

    def _initialize(self,platform=None, micro_agents_config=None,  project_id=None, chat_interface:ChatInterface=None):
        self.agents = {}
        self.task_metadata = {}
        self.project_id = project_id
        self.platform = platform
        self.micro_agents_config = micro_agents_config
        self.chat_interface:ChatInterface = chat_interface

    def set_chat_interface(self, chat_interface):
        self.chat_interface = chat_interface
    
    def set_project_id(self, project_id):
        self.project_id = project_id

    def copy_repository_from_build(self, source_path: str, destination_path: str, workspace_path: str = None,
                                   ws_client=None, task_id=None, db=None, repository_name: str = "unknown") -> None:
        """
        Copy repository from build path to workspace path, excluding dependency directories.
        """
        try:
            # If destination already exists, check for real files
            if os.path.exists(destination_path):
                if has_real_files(destination_path):
                    send_agent_message_custom(
                        ws_client,
                        task_id,
                        f"Skipping copy as repository already exists in destination",
                        db
                    )
                    return
                else:
                    shutil.rmtree(destination_path)

            # Determine which source path to use
            actual_source = workspace_path if workspace_path else source_path

            # Check if actual source path exists
            if not os.path.exists(actual_source):
                print(generate_timestamp(),f"{actual_source} not found. Using {source_path}")
                actual_source = source_path

            # Check if the source directory has any real files
            if not has_real_files(actual_source):
                actual_source = source_path  # Fallback to source_path if workspace is empty

            def ignore_patterns(directory, contents):
                patterns_to_ignore = {
                    'node_modules',
                    'package-lock.json',
                    'yarn.lock',
                    '.npm',
                    '.yarn',
                    '.next',
                    '__pycache__',
                    '.pytest_cache',
                    '.venv',
                    'venv',
                    'env'
                }
                return [item for item in contents if item in patterns_to_ignore]

            os.makedirs(os.path.dirname(destination_path), exist_ok=True)

            shutil.copytree(
                actual_source,
                destination_path,
                symlinks=True,
                ignore=ignore_patterns
            )

            send_agent_message_custom(
                ws_client,
                task_id,
                f"Successfully copied repository {repository_name} to workspace",
                db
            )

        except Exception as e:
            error_msg = f"Error copying repository from build path: {str(e)}"
            send_agent_message_custom(ws_client, task_id, error_msg, db)
            print(generate_timestamp(),error_msg)
            raise

    def clone_all_repositories(self, project_details: dict, ws_client=None, task_id=None, db=None, agent_name="CodeGeneration") -> str:
        """Clone all repositories for a project and return base workspace path"""
        try:
            workspace_path = get_codegeneration_path(
                agent_name, input_arguments.get("task_id"))

            project_id = int(input_arguments.get("project_id"))
            
            project_repositories_record = db["project_repositories"].find_one({
                "project_id": project_id
            })

            project_repositories = project_repositories_record.get(
                "repositories", [])
            print(generate_timestamp(),"repositories", project_repositories)
            selected_repo_ids = []
            select_repositories = os.getenv("repositories", "{}")

            if select_repositories:
                select_repositories = json.loads(select_repositories)
                if select_repositories:
                    if agent_name == "DocumentCreation":
                        selected_repo_ids = select_repositories.get("selected_repos", [])
                    else:
                        selected_repo_ids = select_repositories.get("repositories", [])

            cached_access_token_map = {}
            filtered_repositories = project_repositories
            if selected_repo_ids:
                filtered_repositories = [repo for repo in project_repositories if repo.get(
                    "repo_id") in selected_repo_ids]

            # For DocumentCreation (deep query), only use the first repository to avoid duplicates
            if agent_name == "DocumentCreation" and filtered_repositories:
                print(generate_timestamp(), f"DocumentCreation agent: Using only the first repository out of {len(filtered_repositories)} available")
                filtered_repositories = filtered_repositories[:1]

            for repository in filtered_repositories:
                repository_metadata = {}
                repository_metadata["service"] = repository.get("service")
                repository_metadata["repositoryId"] = repository.get("repo_id")
                
                # Handle repository name extraction safely
                repo_name = repository.get("repository_name", "")
                if "/" in repo_name:
                    repository_metadata["repositoryName"] = repo_name.split("/")[1]
                    repository_metadata["organization"] = repo_name.split("/")[0]
                else:
                    repository_metadata["repositoryName"] = repo_name
                    repository_metadata["organization"] = ""
                    
                repository_metadata["auth_type"] = "user"
                
                # Check which type of repository we're dealing with
                if repository.get("service") == "localFiles":
                    # Local file upload case - builds is directly on the repository
                    builds = repository.get("builds", {})
                    user_id = builds.get("user_id")
                    path_to_copy_from = builds.get("path", "")
                else:
                    # GitHub repository case - builds is nested in branches
                    branches = repository.get("branches", [])
                    if branches and branches[0].get("builds"):
                        builds = branches[0]["builds"]
                        user_id = builds.get("user_id")
                        build_id = builds.get("build_id")
                        path_to_copy_from = builds.get("path", "")
                    else:
                        # Skip repositories with no valid branch data
                        send_agent_message_custom(
                            ws_client=ws_client,
                            task_id=task_id,
                            message=f"Skipping repository {repository_metadata['repositoryName']} - missing branch data",
                            db=db
                        )
                        continue
                
                # Handle access token retrieval
                if repository.get("scm_id"):
                    try:
                        scm_id = repository.get("scm_id")
                        scm_config = db["scm_configurations"].find_one({"scm_id": scm_id})
                        repository_metadata["access_token"] = scm_config.get("credentials", {}).get("access_token") if scm_config else None
                    except Exception:
                        repository_metadata["access_token"] = None
                else:
                    if repository.get("access_token_path") == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
                        access_token = repository_metadata.get("access_token")
                        if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                            access_token = settings.GITHUB_ACCESS_TOKEN
                        repository_metadata["access_token"] = access_token
                    elif user_id:
                        user_github_entry = db["users_github"].find_one({"userId": user_id})
                        access_token = user_github_entry.get("access_token") if user_github_entry else None
                        if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                            access_token = settings.GITHUB_ACCESS_TOKEN
                        repository_metadata["access_token"] = access_token
                    else:
                        repository_metadata["access_token"] = None
                
                # Set clone URLs
                repository_metadata["cloneUrlHttp"] = repository.get("git_url")
                repository_metadata["cloneUrlSsh"] = repository.get("clone_url_ssh")

                repo_path = os.path.join(workspace_path, repository_metadata["repositoryName"])
                repository_metadata["current_path"] = repo_path

                if not os.environ.get("LOCAL_DEBUG"):
                    if path_to_copy_from and path_to_copy_from.startswith("/app/data/"):
                        path_to_copy_from = path_to_copy_from.replace(
                            "/app/data/", f"/efs/", 1)

                print(generate_timestamp(),"path_to_copy from:", path_to_copy_from)
                self.task_metadata.setdefault(task_id, {}).setdefault(
                    "repository_metadata", []).append(repository_metadata)
                self.task_metadata.setdefault(task_id, {}).setdefault(
                    "project_directories", []).append(repo_path)
                
                # Only add git links if they exist
                if repository_metadata["cloneUrlHttp"]:
                    self.task_metadata.setdefault(task_id, {}).setdefault(
                        "git_links", []).append(repository_metadata["cloneUrlHttp"])

                alternative_path = f"/efs/{get_tenant_id()}/{project_id}/workspace/{repository_metadata['repositoryName']}"
                if (os.path.exists(path_to_copy_from) or os.path.exists(alternative_path)) and not project_details.get("Manifest"):
                    self.copy_repository_from_build(
                        source_path=path_to_copy_from,
                        destination_path=repo_path,
                        workspace_path=None,
                        ws_client=ws_client,
                        task_id=task_id,
                        db=db,
                        repository_name=repository_metadata["repositoryName"]
                    )
                    
                    lock_file = os.path.join(repo_path, '.knowledge', '.vector_db', '.milvus.db.lock')

                    # Check if lock file exists and remove it
                    if os.path.exists(lock_file):
                        try:
                            os.remove(lock_file)
                            print(generate_timestamp(),f"Successfully removed {lock_file}")
                        except Exception as e:
                            print(generate_timestamp(),f"Error removing lock file: {e}")
                                
                    add_git_ignore_pattern(repo_path, 'logs/')
                    add_git_ignore_pattern(repo_path, 'logs/', True)
                else:
                    try:
                        print("KAVIA GENERATED PROJECT")
                        access_token = repository_metadata.get("access_token")
                        if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                            # Use default token if organization matches default organization
                            access_token = settings.GITHUB_ACCESS_TOKEN
                        repository_metadata["access_token"] = access_token
                        _run_clone_in_background(repository_metadata=repository_metadata, tenant_id=get_tenant_id(), task_id=task_id, repo_name=repository_metadata["repositoryName"])
                        send_agent_message_custom(
                            ws_client=ws_client,
                            task_id=task_id,
                            message=f'Build path not found. Cloned repository {repository_metadata["repositoryName"]} to workspace',
                            db=db
                        )
                    except Exception as e:
                        import traceback
                        traceback.print_exc()
                        print(generate_timestamp(),"Failed to clone with code maintenance")
                        print(generate_timestamp(),e)
                # SESSION MANAGEMENT
                # Handle session branch creation after cloning
                if task_id:
                    try:
                        repo = Repo(repo_path)
                        branch_result = create_session_branches(repo, task_id)
                        send_agent_message_custom(
                            ws_client=ws_client,    
                            task_id=task_id,
                            message=f'Session branches created for {repository_metadata["repositoryName"]}: {branch_result}',
                            db=db
                        )
                        print(generate_timestamp(),f"Branch operations completed for {repository_metadata['repositoryName']}: {branch_result}")
                    except Exception as branch_error:
                        print(generate_timestamp(),f"Error creating session branches for {repository_metadata['repositoryName']}: {branch_error}")
                        pass
            
            return workspace_path

        except Exception as e:
            error_msg = f"Error cloning repositories: {str(e)}"
            send_agent_message_custom(ws_client, task_id, error_msg, db)
            print(generate_timestamp(),error_msg)
            raise

    def get_session_dir(self, project_details: dict, work_item_details: dict,
                        agent_name: str = "CodeGeneration", ws_client=None,
                        task_id=None, db=None) -> str:
        """Get appropriate session directory based on agent type"""
        try:
           
            if agent_name == "CodeMaintenance" or  agent_name =="DocumentCreation":
                # For CodeMaintenance, clone all repos and return base workspace path
                return self.clone_all_repositories(project_details, ws_client, task_id, db, agent_name)
            else:
                # For other agents, clone ALL container repositories
                repositories = project_details.get("current_repositories")
                
                if repositories:
                    
                    for  repository in repositories:
                        
                        repo_name = repository.get("repositoryName")
                        container_id = repository.get("container_id")
                        
                        send_agent_message_custom(
                            ws_client,
                            task_id,
                            f"Cloning repository: {repo_name} (Container ID: {container_id})...",
                            db
                        )
                        
                        try:
                            repository_metadata = repository.copy()
                            
                            # Handle access token based on token path
                            if repository_metadata.get("access_token_path") == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
                                access_token = repository_metadata.get("access_token")
                                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                                    access_token = settings.GITHUB_ACCESS_TOKEN
                                repository_metadata["access_token"] = access_token

                            elif repository_metadata.get("encrypted_scm_id") or repository_metadata.get("scm_id"):
                                if repository_metadata.get("encrypted_scm_id"):
                                    scm_id = repository_metadata.get("encrypted_scm_id")
                                    decrypted_scm_id = decrypt_string(scm_id)
                                else:
                                    decrypted_scm_id = repository_metadata.get("scm_id")
                                config = scm_manager.get_configuration(get_tenant_id(), scm_id=decrypted_scm_id)
                                if not config:
                                    raise Exception(f"Failed to get SCM configuration for container {container_id}")
                                access_token = config.credentials.access_token
                                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                                    access_token = settings.GITHUB_ACCESS_TOKEN
                                repository_metadata["access_token"] = access_token
                            
                            
                            # Clone the repository
                            session_dir =  os.path.join(get_codegeneration_path(agent_name=agent_name, task_id=task_id), repository_metadata["repositoryName"])
                            
                            # Update repository metadata with current path
                            repository_metadata["current_path"] = session_dir
                            repository_metadata["container_id"] = container_id
                            
                            # Store in task metadata
                            self.task_metadata.setdefault(task_id, {}).setdefault(
                                "project_directories", []).append(session_dir)
                            self.task_metadata.setdefault(task_id, {}).setdefault(
                                "git_links", []).append(repository.get("cloneUrlHttp"))
                            self.task_metadata.setdefault(task_id, {}).setdefault(
                                "repository_metadata", []).append(repository_metadata)
                            
                            
                            send_agent_message_custom(
                                ws_client,
                                task_id,
                                f"Successfully initialized workspace for {repo_name} at: {session_dir}",
                                db
                            )
                            
                        except Exception as e:
                            send_agent_message_custom(
                                ws_client,
                                task_id,
                                f"Failed to clone repository {repo_name} (Container {container_id}): {str(e)}",
                                db
                            )
                            continue  # Continue with other repositories even if one fails
                    

                    return get_codegeneration_path()  # Return single path, not list

                
                   
                else:
                    error_msg = "No repository information found in project details"
                    send_agent_message_custom(
                        ws_client, task_id, error_msg, db)

        except Exception as e:
            error_msg = f"Error getting session directory: {str(e)}"
            send_agent_message_custom(ws_client, task_id, error_msg, db)
            print(generate_timestamp(),error_msg)
            raise
        
    def _update_manifest_workspaces_silently(self, repositories: Dict, manifest_path: str) -> None:
        """
        Efficiently update manifest file with workspace paths.
        Maps first repository to first container of matching type without database queries.
        
        Args:
            repositories: Dictionary of repository information
            session_dirs: List of session directory paths
            manifest_path: Path to the manifest YAML file
            db: Database connection (unused in this optimized version)
        """
        if not os.path.exists(manifest_path):
            return
        
        try:
            # Load manifest file
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_data = yaml.safe_load(f)
            
            if not manifest_data or 'containers' not in manifest_data:
                return
            
            # Get repository names in order
            repo_names = [repo_info.get("repositoryName", "") for repo_info in repositories.values()]
            repo_names = [name for name in repo_names if name]  # Filter out empty names
            
            if not repo_names:
                return
            
            updates_made = False
            
            # Update containers in manifest - map each repository to each container one-by-one
            containers = manifest_data.get('containers', [])
            
            for i, container in enumerate(containers):
                # Skip if no more repositories available
                if i >= len(repo_names):
                    break
                    
                container_type = container.get('container_type')
                
                # Skip if no container type
                if not container_type:
                    continue
                
                # Get the corresponding repository name for this container
                repo_name = repo_names[i]
                
                # Generate workspace path using the repository name
                workspace_path = f"{repo_name}"
                
                # Update workspace for this container
                container['workspace'] = workspace_path
                container['container_root'] = f"{workspace_path}/{workspace_path}"
                updates_made = True
            
            # Save updated manifest if changes were made
            if updates_made:
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    yaml.dump(
                        manifest_data, 
                        f, 
                        default_flow_style=False, 
                        allow_unicode=True, 
                        indent=2, 
                        sort_keys=False
                    )
        
        except (yaml.YAMLError, IOError, KeyError) as e:
            # Log error if needed, but continue silently as per original function name
            print(generate_timestamp(),f"Error updating manifest: {e}")
        except Exception:
            # Silent failure as per original function behavior
            pass
    def get_or_create_agent(self, task_id, project_details, work_item_details,
                            agent_params: AgentParams, ws_client=None, db=None):
        if task_id not in self.agents:
            self.agents[task_id] = self._create_new_agent(
                task_id, project_details, work_item_details, agent_params,
                ws_client, db
            )
        return self.agents[task_id]

    def _create_new_agent(self, task_id: str, project_details: dict,
                          work_item_details: dict, agent_params: AgentParams,
                          ws_client=None, db=None) -> TaskExecutionAgent:
        try:
            session_dir = self.get_session_dir(
                project_details, work_item_details,
                agent_name=agent_params.agent_name,
                ws_client=ws_client,
                task_id=task_id,
                db=db
            )
            
            
            subprocess.run(['chmod', '-R', '777', session_dir], check=False)

            
            send_agent_message_custom(
                ws_client,
                task_id,
                f"Setting Base path {session_dir}...",
                db
            )
            chat_enabled_worker = ChatEnabledWorker([], name="TaskExecutionAgent")
            self.chat_interface.add_chat_user(chat_enabled_worker)

            llm = LLMInterface(
                llm_api_key=settings.OPENAI_API_KEY,
                session_dir=session_dir,
                instance_name="TaskExecutionAgent",
                chat_worker=chat_enabled_worker
            )
            selected_platform = self.platform
            selected_framework = work_item_details.get("framework", "default")
            if self.platform == "mobile":
                selected_framework = work_item_details.get("overview", {}).get("mobile_framework", None)
    
            if agent_params.agent_name == "DocumentCreation":
                print(generate_timestamp(),"DocumentCreation AGENT")
                micro_agents_yaml_base_path = pkg_resources.resource_filename(
                        "code_generation_core_agent.agents.micro", 
                        ""
                    )
                selected_platform = ApplicationType.GENERIC.value
                selected_framework = FrameworkType.DEFAULT.value
                platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
                micro_agents_config = platform_config_loader.get_platform_config(
                    platform=ApplicationType.from_string(selected_platform),
                    framework=FrameworkType.from_string(selected_framework)
                )
                agent = TaskExecutionAgent(
                    llm,
                    execution_base_path=session_dir,
                    micro_agents_config=micro_agents_config,
                    model_name=agent_params.llm_model,
                    prompts_base_path=get_prompt_directory(agent_params.agent_name),
                    platform=selected_platform,
                    framework=selected_framework,
                )
            elif agent_params.agent_name == "CodeMaintenance":
                micro_agents_yaml_base_path = pkg_resources.resource_filename(
                        "code_generation_core_agent.agents.micro", 
                        ""
                    )
                platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
                micro_agents_config = platform_config_loader.get_platform_config(
                    platform=ApplicationType.from_string(selected_platform),
                    framework=FrameworkType.from_string(selected_framework)
                )
                
                agent = TaskExecutionAgent(
                    llm,
                    execution_base_path=session_dir,
                    micro_agents_config=self.micro_agents_config,
                    model_name=agent_params.llm_model,
                    prompts_base_path=get_prompt_directory(agent_params.agent_name)
                )
            else:
                if self.platform == "generic":
                    micro_agents_yaml_base_path = pkg_resources.resource_filename(
                        "code_generation_core_agent.agents.micro", 
                        ""
                    )
                    platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
                    micro_agents_config = platform_config_loader.get_platform_config(
                        platform=ApplicationType.from_string(selected_platform),
                        framework=FrameworkType.from_string(selected_framework)
                    )
                    agent = TaskExecutionAgent(
                        llm,
                        execution_base_path=get_codegeneration_path(),
                        micro_agents_config=self.micro_agents_config,
                        model_name=agent_params.llm_model,
                        prompts_base_path=get_prompt_directory(agent_params.agent_name),
                        platform=selected_platform,
                        framework=selected_framework,
                    )
                else:
                    micro_agents_yaml_base_path = pkg_resources.resource_filename(
                        "code_generation_core_agent.agents.micro", 
                        ""
                    )
                    platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
                    micro_agents_config = platform_config_loader.get_platform_config(
                        platform=ApplicationType.from_string(selected_platform),
                        framework=FrameworkType.from_string(selected_framework)
                    )

                    agent = TaskExecutionAgent(
                        llm,
                        execution_base_path=session_dir,
                        micro_agents_config=micro_agents_config,
                        model_name=agent_params.llm_model,
                        prompts_base_path=get_prompt_directory(agent_params.agent_name),
                        platform=selected_platform,
                        framework=selected_framework,
                    )

            send_agent_message_custom(
                ws_client,
                task_id,
                "Workspace environment is ready to use",
                db
            )

            return agent

        except Exception as e:
            error_msg = f"Error creating agent: {str(e)}"
            send_agent_message_custom(ws_client, task_id, error_msg, db)
            print(generate_timestamp(),error_msg)
            raise

def get_supabase_credentials_and_append(work_item_details: dict) -> dict:
    """
    Retrieve Supabase credentials from MongoDB and append to work_item_details.
    
    Args:
        work_item_details (dict): The work item details dictionary
        
    Returns:
        dict: Updated work_item_details with Supabase credentials in env
    """
    try:
        # Extract project_id from work_item_details
        project_id = str(work_item_details.get('project_id'))
        if not project_id:
            return work_item_details
        
        # Get MongoDB connection
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        collection = mongo_db.db["supabase_user_credentials"]
        
        # Query for Supabase credentials
        query = {
            "project_id": project_id,
            "is_connected": True  # Only get active connections
        }
        
        projection = {
            "api_url": 1,
            "db_url": 1,
            "anon_key": 1,
            "_id": 0
        }
        
        supabase_record = collection.find_one(query, projection)
        
        if not supabase_record:
            print(generate_timestamp(), f" No Supabase credentials found for project {project_id}")
            return work_item_details
        
        # Decrypt the encrypted fields
        try:
            decrypted_db_url = decrypt_data(supabase_record["db_url"])
            decrypted_anon_key = decrypt_data(supabase_record["anon_key"])
            api_url = supabase_record["api_url"]
            
            # Initialize env if it doesn't exist
            if "env" not in work_item_details:
                work_item_details["env"] = {}
            if "third_party_services" not in work_item_details:
                work_item_details["third_party_services"] = []
            # Add Supabase environment variables directly to env
            work_item_details["env"]["SUPABASE_URL"] = api_url
            work_item_details["env"]["SUPABASE_KEY"] = decrypted_anon_key
            # work_item_details["env"]["SUPABASE_DB_URL"] = decrypted_db_url  #
            work_item_details["third_party_services"] = 'Supabase'
            print(generate_timestamp(), f" Added Supabase credentials to env in work_item_details")
            
            return work_item_details
            
        except Exception as decrypt_error:
            print(generate_timestamp(), f" Error decrypting Supabase credentials: {str(decrypt_error)}")
            return work_item_details
            
    except Exception as e:
        print(generate_timestamp(), f"Error retrieving Supabase credentials: {str(e)}")
        return work_item_details


def execute_code_generation_agent(project_details, work_item_details=None, task_id=None, agent_params: AgentParams = None, retry=False, ws_client: WebSocketClient =None, mongo_db = None):
    global db
    global gl_agent_params
    user_id = os.environ.get("user_id")

    micro_agents_config = None
    print(generate_timestamp(),"USER ID", user_id," Tenant ID" ,get_tenant_id())
    if mongo_db:
        db = mongo_db
    else:
        if get_tenant_id() == settings.KAVIA_B2C_CLIENT_ID:
            db = get_mongo_db(user_id=user_id).db
        else:
            db = get_mongo_db().db
    gl_agent_params = agent_params
    assets = project_details.get("assets", {})
    source_directories = []
    
    if assets:
        assets:dict = json.loads(assets)
        assets_values = assets.values()
        for asset in assets_values:
            print(generate_timestamp(),"asset", asset)
            
            path = asset.get("extracted_assets_path")
            if path:
                if path.startswith("/app/data/"):
                    path = path.replace("/app/data/", f"/efs/", 1)
                source_directories.append(path)
    
    # get the prmompt and micro-configuration
    platform = input_arguments.get("platform") or "generic"
    # allocate diffent ports for differnt platform
    print(generate_timestamp(),f"Platform is {platform} and selected framework is : {work_item_details.get('framework')}")
    framework = work_item_details.get("framework", "default")
    
    if platform == "mobile":
        framework = work_item_details.get("overview",{}).get("mobile_framework", "default")
    
    
    micro_agents_yaml_base_path = pkg_resources.resource_filename(
        "code_generation_core_agent.agents.micro", 
        ""
    )
    platform_config_loader = PlatformConfigLoader(micro_agents_yaml_base_path)
    platform_type = ApplicationType.from_string(platform)
    framework_type = FrameworkType.from_string(framework)
    micro_agents_config = platform_config_loader.get_platform_config(
        platform=platform_type,
        framework=framework_type
    )

    tenant_cred = asyncio.run(
        tenant_service.get_tenant_cred(get_tenant_id()))
    
    user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
    chat_interface_thread = ChatInterfaceThread(task_id, ws_client, db)
    chat_interface_thread.set_user_manager(user_manager)
    chat_interface_thread.set_code_generation_path(get_codegeneration_path(agent_params.agent_name, task_id))
    chat_interface_thread.start()
    chat_interface = chat_interface_thread.get_chat_interface()
    


    agent_manager = CodeGenerationAgentManager(platform, micro_agents_config, chat_interface=chat_interface)
    try:
        global git_tool
        # Initialize agent

        # Get repository metadata based on agent type
        if agent_params.agent_name == "CodeGeneration":
            # For CodeGeneration, use the first repository from current_repositories array
            current_repositories = project_details.get("current_repositories", [])
            repository_metadata = current_repositories[0] if current_repositories else {}
            print(generate_timestamp(), f"DEBUG - CodeGeneration current_repositories: {current_repositories}")
            print(generate_timestamp(), f"DEBUG - Selected repository_metadata: {repository_metadata}")
        else:
            # For other agents (CodeMaintenance, DocumentCreation), use current_repository
            repository_metadata = project_details.get("current_repository", {})
            print(generate_timestamp(), f"DEBUG - Other agent current_repository: {repository_metadata}")

        if agent_params.agent_name == "CodeMaintenance":
            agent: TaskExecutionAgent = agent_manager.get_or_create_agent(
                task_id, project_details, work_item_details, agent_params, ws_client=ws_client, db=db)
        else:
            agent: TaskExecutionAgent = agent_manager.get_or_create_agent(
                task_id, project_details, work_item_details, agent_params, ws_client=ws_client, db=db)

        # Replace the inline config file creation with the function call
        framework = work_item_details.get("overview",{}).get("frontend_framework")
        repository_name = repository_metadata.get("repositoryName",'unknown')
        print(generate_timestamp(), f"DEBUG - Final repository_name: {repository_name}")
        print(generate_timestamp(), f"DEBUG - Available keys in repository_metadata: {list(repository_metadata.keys()) if repository_metadata else 'None'}")
        create_or_update_config_file(get_codegeneration_path(agent_params.agent_name, task_id), task_id, framework, repository_name)
        try:
            agent.request_context.setdefault("design_node", work_item_details.get("design_node", {}))
        except Exception as e:
            print(generate_timestamp(),f"Error adding design knowledge: {str(e)}")

        git_tool = EnhancedGitTools(
            callback_functions=None,
            base_path=agent.base_path,
            logger=None,
            repository_metadata=repository_metadata,
            tenant_id=get_tenant_id(),
            mongo_db=db
        )

        reporter = TaskReporter(task_id, agent=agent, db=db, ws_client=ws_client)
        reporter.set_chat_controller(chat_interface_thread)
        reporter.initialize()

        controller = Controller(task_id, agent=agent, ws_client=ws_client)
        chat_interface_thread.set_controller(controller)
        # Import DeploymentController and initialize it
        controller.set_chat_controller(chat_interface_thread)
        
        deployment_controller = DeploymentController(task_id, agent=agent, ws_client=ws_client, db=db)
        controller.set_deployment_controller(deployment_controller)

        container_deployment_controller = ECSDeploymentController(task_id, ws_client, db,agent)
        controller.set_container_deployment_controller(container_deployment_controller)

        preview_controller = PreviewController(agent=controller.agent, task_id=task_id,ws_client=ws_client,db=db)
        controller.set_preview_controller(preview_controller)
        
        if agent_params.agent_name != "DocumentCreation":
            git_controller = GitController(
                task_id=task_id,
                git_tool=git_tool,
                directories=agent_manager.task_metadata[task_id].get(
                    "project_directories", []),
                ws_client=ws_client,
                db=db,
                repository_metadata=agent_manager.task_metadata[task_id].get(
                    "repository_metadata", [])
            )

            if agent_params.agent_name == "CodeGeneration":
                git_controller.auto_push = True
                git_controller.set_auto_push(True)
            controller.set_git_controller(git_controller)
            chat_interface_thread.set_git_controller(git_controller)
        
            
        controller.initialize()

        reporter.set_controller(controller)
        controller.set_reporter(reporter)

        # Initialize handlers

        if agent_params.agent_name != "DocumentCreation":
            if work_item_details.get("branch"):
                asyncio.run(handle_branch_checkout(
                    git_tool=git_tool,
                    reporter=reporter,
                    ws_client=ws_client,
                    task_id=task_id,
                    work_item_details=work_item_details,
                    agent_base_path=agent.base_path,
                    db=db
                ))

        agent.executor.running = False
        ws_client.send_message("web_socket_connected", {
            "context": work_item_details,
            "project_details": project_details,
        })

        # Update task status
        try:
            to_update = {
                        "status": TaskStatus.RUNNING,
                        "execution_status": TaskStatus.RUNNING,
                        "start_time": generate_timestamp(),
                        "session_id": str(LoggerFactory.get_logger_session_id()),
                        "project_details": json.dumps(project_details),
                        "user_inputs": []
                    }
            if not retry:
                to_update["work_item_details"] = json.dumps(work_item_details)

            db[TASKS_COLLECTION_NAME].update_one(
                {"_id": task_id},
                {
                    "$set": to_update
                },
                upsert=True
            )
        except Exception as e:
            print(generate_timestamp(),f"Error updating task status: {str(e)}")

        # Process work item
        if work_item_details:
            previous_context = db[TASKS_COLLECTION_NAME].find_one({"_id": task_id},{"previous_request_context": 1})
            print(generate_timestamp(),"previous_context", previous_context)
            if previous_context:
                previous_context = previous_context.get("previous_request_context", {})
            elif retry:
                previous_work_item = {
                    **work_item_details,
                }

                previous_context = {
                    "work_item": previous_work_item,
                    "execution_base_path": agent.base_path,
                    "agent_name": agent_params.agent_name,
                    **work_item_details
                }
            else:
                previous_context = None
            print(generate_timestamp(),"previous_context", previous_context)
            

            print(generate_timestamp(),f"Work_ITem_details: Type: {type(work_item_details)}, details: {work_item_details} ")
            work_item_details = get_supabase_credentials_and_append(work_item_details)

            print(generate_timestamp(),"AGENT : ", agent_params.agent_name)
            print(generate_timestamp(),"PHASE TASK EXECUTION, START TIME: ", generate_timestamp())
            
            tenant_id = get_tenant_id()
            budget = AsyncSyncHelper.run_async_safe(get_user_budget_usd(user_id, tenant_id))
            print(generate_timestamp(),"===============================BUDGET===========================", budget)
            
            containers = work_item_details.get("containers", [])
            
            for container in containers:
                try:
                    container_name = container.get("container_name", "")
                    container_workspace = container.get("workspace", "")
                    
                    #Ensure the workspace exists
                    workspace_path = f"{agent.base_path}/{container_workspace}/{container_name}"
                    os.makedirs(workspace_path, exist_ok=True)
                    try:
                        subprocess.run(["chmod", "-R", "777", workspace_path], check=True, capture_output=True)
                        os.chmod(workspace_path, 0o777)
                        
                            
                        
                        #create a status_file to indicate the workspace is ready
                        status_file_path = f"{workspace_path}/.status"
                        with open(status_file_path, 'w') as f:
                            f.write("ready")
                    except Exception as e:
                        print(generate_timestamp(),f"Error setting permissions for {workspace_path}: {e}")
                  
                except Exception as e:
                    print(generate_timestamp(),f"Error creating workspace for {container_name}: {e}")
                    pass

            context = asyncio.run(agent.process_work_item(
                agent_name=agent_params.agent_name,
                work_item=work_item_details,
                use_retriever=False,
                control=controller,
                status_reporter=reporter,
                chat_interface=chat_interface,
                budget = budget ,
                previous_context=json.dumps(
                    previous_context) if retry else None
            ))
            # Store context in db
            db[TASKS_COLLECTION_NAME].update_one(
                {"_id": task_id},
                {"$set": {
                    "previous_request_context": context
                }}, 
                upsert=True
            )

        return task_id

    except StopIteration as e:
        # Handle user-initiated stop or inactivity timeout
        print(f"Code generation stopped: {str(e)}")
        # Status should already be set to STOPPED by the controller, so don't override it
        # Just log and continue to finally block
        pass
    except Exception as e:
        error_msg = f"""
    **⚠️ Code Generation Error**
    {str(e)}
    Please check the error details above.
    """
        error_traceback = traceback.format_exc()
        print(generate_timestamp(),"\nFull traceback:")
        print(generate_timestamp(),error_traceback)
        print(generate_timestamp(),f"Error in code generation: {str(e)}")
        send_agent_message_custom(
            ws_client=ws_client,
            task_id=task_id,
            message=error_msg,
            db=db
        )
        if task_id:
            try:
                db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": TaskStatus.FAILED,
                        "execution_status": TaskStatus.STOPPED,
                        "stop_reason": str(e)
                    }}
                )
            except Exception as db_error:
                print(generate_timestamp(),f"Error updating task failure status: {str(db_error)}")
        raise
    
    finally:
        
        if controller:
            controller._handler_running = False
        if ws_client:
            # Check current status to determine final_status
            current_task = db[TASKS_COLLECTION_NAME].find_one({"_id": task_id}, {"status": 1})
            current_status = current_task.get("status") if current_task else None
            
            if current_status == TaskStatus.STOPPED:
                final_status = TaskStatus.STOPPED
            elif 'error_msg' in locals():
                final_status = TaskStatus.FAILED
            else:
                final_status = TaskStatus.COMPLETE
                
            completion_message = f"""
    **{final_status}**
    Session has been terminated.
    """

            manifest_path = f"{controller.agent.base_path}/.project_manifest.yaml"
            controller.sync_manifest_to_project_node(manifest_path)
            send_agent_message_custom(
                ws_client=ws_client, task_id=task_id, message=completion_message, db=db)
            ws_client.disconnect()
        # Only set to COMPLETE if not already stopped/failed
        if 'error_msg' not in locals():
            # Check current status to avoid overriding STOPPED status
            current_task = db[TASKS_COLLECTION_NAME].find_one({"_id": task_id}, {"status": 1, "execution_status": 1})
            current_status = current_task.get("status") if current_task else None
            
            # Only update to COMPLETE if current status is not STOPPED
            if current_status != TaskStatus.STOPPED:
                db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": TaskStatus.COMPLETE,
                        "execution_status": TaskStatus.COMPLETE
                    }}
                )
                print(f"Task {task_id} completed successfully")
            else:
                print(f"Task {task_id} was stopped by user - preserving STOPPED status")

def create_or_update_config_file(base_path, task_id, framework, repository_name):
    """
    Create or update the .config.json file in the session directory with task information.
    
    Args:
        base_path (str): The base path where the config file should be stored
        task_id (str): The current task ID
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        config_path = os.path.join(base_path, ".config.json")
        if os.path.exists(config_path):
            with open(config_path, "r") as file:
                config = json.load(file)
        else:
            config = {}
        
        config["task_id"] = task_id
        config["start_time"] = generate_timestamp()
        config["websocket_url"] = settings.WEBSOCKET_URI
        config["framework"] = framework
        config["frontend_framework"] = framework
        config["repository_name"] = repository_name
        config["queue_path"] = "/tmp/kavia/workspace/.queue" if os.environ.get("LOCAL_DEBUG") else "/home/<USER>/workspace/.queue"
        
        with open(config_path, "w") as file:
            json.dump(config, file)
        return True
    except Exception as e:
        print(generate_timestamp(),f"Error creating/updating config file: {str(e)}")
        return False