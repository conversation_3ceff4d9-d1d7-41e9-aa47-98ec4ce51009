# --------------------------------------------------------------------------------
# Company Name: Kavia AI
# Author: <PERSON><PERSON><PERSON>
# Creation Date: Year (2024)
#
# Confidential Information of Kavia AI
# NOTICE: All information contained herein is, and remains the property of Kavia AI.
# The intellectual and technical concepts contained herein are proprietary to Kavia AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Kavia AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Kavia AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------

from datetime import datetime
from typing import Any, Dict, List
from textwrap import dedent
import litellm
from litellm import Message, ModelResponse,  ChatCompletionMessageToolCall
from dotenv import load_dotenv
import json
import asyncio
import json
import logging
import os
from openai import OpenAI
from openai.types.chat.chat_completion_message_tool_call import ChatCompletionMessageToolCall, Function
from llm_wrapper.models.function_context import FunctionContext
from llm_wrapper.utils.base_utils import CustomJSONEncoder, MessageFormatter

load_dotenv()

class LLMInterfaceTest:
    def __init__(self, session_dir, instance_name, token_limit=None):
        self.instance_name = instance_name
        
        # Create logs directory if it doesn't exist
        logs_dir = os.path.join(session_dir, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        self.llm_logger = self.setup_logger(f'llm_logger_{instance_name}', os.path.join(logs_dir, f'llm_{instance_name}.log'))
        self.token_limit = token_limit
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        
        if os.getenv('USE_KEYWORDS_AI') and os.environ['USE_KEYWORDS_AI'] == "true":
            base_url = os.environ['BASE_URL']
            api_key = os.environ['KEYWORDSAI_API_KEY']
            self.client = OpenAI(base_url=base_url, api_key=api_key)
        else:
            self.client = litellm
        
    def setup_logger(self, name, log_file, level=logging.INFO):
        """Function to set up a logger for logging to a file."""
        logger = logging.getLogger(name)

        if not logger.handlers:
            handler = logging.FileHandler(log_file)
            formatter = MessageFormatter('%(asctime)s %(levelname)s\n%(message)s\n')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        logger.setLevel(level)
        logger.propagate = False
        return logger
    
    async def llm_interaction_wrapper(self, messages, user_prompt, system_prompt, model, response_format, function_schemas=None, function_executor=None, 
                                      stream=False, max_retries=5, max_tool_calls=25, warning_threshold=5):

        """
        Asynchronously interacts with a language model, handling various aspects of the interaction.

        Args:
            messages (list): A list of message dictionaries representing the conversation history.
            user_prompt (str): The current user's input prompt.
            system_prompt (str): The system prompt to guide the model's behavior.
            model (str): The name of the language model to use.
            response_format (dict): Specifies the desired format of the model's response.
            function_schemas (list, optional): A list of function schemas for function calling. Defaults to None.
            function_executor (callable, optional): A function to execute called functions. Defaults to None.
            stream (bool, optional): Whether to stream the response. Defaults to False.
            max_retries (int, optional): Maximum number of retries for rate limit errors. Defaults to 5.

        Returns:
            dict: The model's response, including content and any function calls.

        Functionality:
        - Processes and sanitizes input messages
        - Prepares prompts and handles system messages
        - Configures model parameters based on the specified model
        - Supports function calling with provided schemas and executor
        - Implements error handling and retries for rate limiting
        - Manages API keys for different model providers
        - Tracks token usage and calculates costs
        - Handles JSON parsing and validation for JSON responses
        - Provides detailed logging of the interaction
        - Supports both normal and streaming response modes

        Raises:
            ValueError: If required API keys are missing.
            Exception: For various error conditions, including rate limit errors and JSON parsing failures.

        Note:
        This function is designed to be flexible and robust, handling many common scenarios in LLM interactions.
        It's particularly useful for applications requiring detailed control over LLM API calls.
        """
        
        self.llm_logger.info("+++++++++++++++++ BEGIN LLM SESSION ++++++++")
        self.llm_logger.info(f"Function schemas: {json.dumps(function_schemas, indent=2)}")
        self.model = model
        self.response_format = response_format
        self.function_schemas = function_schemas

        # Prepare the system message
        if response_format and response_format.get("type") == "json_object":
            json_system_message = "You are an AI assistant. When responding, you must always provide your entire response as a single, valid JSON object. There should not be any plain text in the response outside of the JSON object."
            if system_prompt:
                system_prompt = f"{system_prompt}\n\n{json_system_message}"
            else:
                system_prompt = json_system_message
        else:
            # Check if there's already a system message
            if system_prompt and not any(message["role"] == "system" for message in messages):
                # If no system message exists, add the new one
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })

        if user_prompt:
            messages.append({"role": "user", "created_at": datetime.now().isoformat(), "content": user_prompt})

        def blocking_call_wrapper():
            
            # Process messages to keep only the latest screenshot
            screenshot_found = False
            for message in reversed(messages):
                if isinstance(message, dict):
                    if 'function_call' in message:
                        try:
                            result = json.loads(message['function_call']['arguments'])
                            if 'image_for_llm' in result:
                                if screenshot_found:
                                    result['image_for_llm'] = "image removed to abbreviate"
                                    message['function_call']['arguments'] = json.dumps(result)
                                else:
                                    screenshot_found = True
                        except json.JSONDecodeError:
                            continue
                    elif 'tool_calls' in message:
                        for tool_call in message['tool_calls']:
                            if isinstance(tool_call, dict) and tool_call.get('type') == 'function':
                                try:
                                    result = json.loads(tool_call['function']['arguments'])
                                    if 'image_for_llm' in result:
                                        if screenshot_found:
                                            result['image_for_llm'] = "image removed to abbreviate"
                                            tool_call['function']['arguments'] = json.dumps(result)
                                        else:
                                            screenshot_found = True
                                except json.JSONDecodeError:
                                    continue
                            elif hasattr(tool_call, 'function') and tool_call.type == 'function':
                                try:
                                    result = json.loads(tool_call.function.arguments)
                                    if 'image_for_llm' in result:
                                        if screenshot_found:
                                            result['image_for_llm'] = "image removed to abbreviate"
                                            tool_call.function.arguments = json.dumps(result)
                                        else:
                                            screenshot_found = True
                                except json.JSONDecodeError:
                                    continue
                                
            self.llm_logger.info("++++++++ BEGIN LLM INPUT++++++++")
            self.llm_logger.info(f"model:{model};format:{response_format};stream:{stream}")
            for message in messages:
                self.llm_logger.info(json.dumps(message, indent=2, cls=CustomJSONEncoder))
                
            self.llm_logger.info("++++++++++ END LLM INPUT+++++++++")
            
            kwargs = {
                "messages": messages,
                "model": model,
                "response_format": response_format,
                "temperature": 0,
                "stream": stream
            }

            if stream:
                kwargs["stream_options"] = {"include_usage": True}
                
            if function_schemas is not None:
                kwargs["tools"] = function_schemas
                kwargs["tool_choice"] = "auto"
            
            if os.getenv('USE_KEYWORDS_AI') and os.environ['USE_KEYWORDS_AI'] == "true":
                kwargs["extra_body"] = {"customer_identifier": self.instance_name }
                return self.client.chat.completions.create(**kwargs)
            else:
                return self.client.completion(**kwargs)
        
        loop = asyncio.get_running_loop()

        def handle_rate_limit(attempt):
            # Reduced exponential backoff for faster recovery from rate limits
            wait = min(self.exponential_backoff(attempt), 5.0)  # Cap wait time at 5 seconds max
            self.llm_logger.warning(f"Rate limit hit, waiting {wait} seconds before retrying...")
            return asyncio.sleep(wait)

        async def invoke_llm():
            attempt = 0
            while attempt < max_retries:
                try:
                    return await loop.run_in_executor(None, blocking_call_wrapper)
                except Exception as e:
                    if "rate limit" in str(e).lower():
                        await handle_rate_limit(attempt)
                        attempt += 1
                    elif "rate_limit_exceeded" in str(e).lower():
                        await handle_rate_limit(attempt)
                        attempt += 1
                    else:
                        self.llm_logger.error(f"Error in LLM interaction: {e}")
                        raise e

            self.llm_logger.error("Max retries hit, failing with rate limit error.")
            raise Exception("Rate limit error: Max retries reached.")
        
        json_retry_count = 0
        max_json_retries = 3  # Maximum number of times to retry JSON generation
        tool_calls_count = 0
        called_functions = set()  # Set to track already called functions

        while True:
            tool_calls_count += 1
            remaining_calls = max_tool_calls - tool_calls_count
            if remaining_calls < 0:
                self.llm_logger.error("Error: Maximum number of tool calls reached. Exiting.")
                return {"error": "Maximum number of tool calls reached. Request could not be completed."}
                
            # Add warnings to messages if needed
            if remaining_calls <= warning_threshold:
                warning_message = f"Warning: Only {remaining_calls} tool call(s) remaining."
                messages.append({"role": "system", "content": warning_message})
            
            if remaining_calls == 1:
                messages.append({"role": "system", "content": "This is your last function call. Please complete the request after this."})
            
            if remaining_calls == 0:
                messages.append({"role": "system", "content": "No more function calls are allowed. Please complete the request."})
            
            completion = await invoke_llm()
            if self.token_limit is not None and self.total_tokens >= self.token_limit:
                self.llm_logger.info(f"Reached token limit of {self.token_limit}, stopping.\n")
                break
            
            if stream:
   
                return self.process_stream(completion, messages, called_functions, function_executor)
                
            else:
                result =  await self.process_non_stream(completion, model, messages, False, function_executor, response_format, max_json_retries)
                #TODO: need to change the approach
                if result == 1:
                    continue
                else:
                    return result
                
    async def process_stream(self,completion, messages, called_functions, function_executor):
        self.llm_logger.info("LLM streaming response")
                
        functions = {}
        tool_call_id = None
        last_content = ""  # Variable to store the last non-empty content

        for chunk in completion:
            
            # Log usage information when available
            if hasattr(chunk, 'usage') and chunk.usage:
                self.llm_logger.info(f"Usage: {chunk.usage}")
            
            if not chunk.choices or not chunk.choices[0].delta:
                continue

            delta = chunk.choices[0].delta

            if delta.tool_calls:
                tool_call = delta.tool_calls[0]

                if tool_call.id:
                    tool_call_id = tool_call.id

                if tool_call.function.arguments:
                    assert tool_call_id is not None
                    functions.setdefault(tool_call_id, {}).setdefault("function_args", "")
                    functions[tool_call_id]["function_args"] += tool_call.function.arguments

                if tool_call.function.name:
                    assert tool_call_id is not None
                    functions.setdefault(tool_call_id, {})["function_name"] = tool_call.function.name

            if delta.content:
                last_content += delta.content  # Update the last content
                yield delta.content
        
        # After the loop, last_content will contain the final non-empty content
        if last_content:
            self.llm_logger.info(f"Final content: {last_content}")
            return
        
        if functions:
            chain = []
            assistant_requested_tool_calls: List[ChatCompletionMessageToolCall] = []
            
            for tool_id, tool in functions.items():
                function_name = tool["function_name"]
                function_args = tool["function_args"]
                chain.append(FunctionContext(id=tool_id,
                                            function_name=function_name,
                                            function_arguments=json.loads(function_args)))
                
                assistant_requested_tool_calls.append(ChatCompletionMessageToolCall(
                    id = tool_id,
                    type="function",
                    function=Function(
                        name=function_name,
                        arguments=function_args
                    )
                ))
            
            messages.append(
                {
                    "role": "assistant",
                    "tool_calls": assistant_requested_tool_calls,
                }
            )
            
            for ctx in chain:
                yield 'Checking the data' # process indication
                if ctx.id not in called_functions:  # Check before executing
                    called_functions.add(id)  # Mark function as called
                    
                    res = await function_executor(ctx.function_name, ctx.function_arguments)
                    self.llm_logger.info(f"LLM OUTPUT function: {ctx.function_name}\nArgs: {json.dumps(ctx.function_arguments, indent=2, cls=CustomJSONEncoder)}\n Function Response: {str(res)}")
                    
                    if 'skip_last_call' in res and res['skip_last_call'] == True:
                        yield res
                        return
                    else:                    
                        messages.append(
                            {
                                "tool_call_id": ctx.id,
                                "role": "tool",
                                "name": ctx.function_name,
                                "content": str(res)
                            }
                        )
                    
                    yield 'Fetching the data' # process indication
                    
            # Call the completion function again
            async for response in await self.llm_interaction_wrapper(
                messages=messages,
                user_prompt=None,
                system_prompt=None,
                model=self.model,
                response_format=self.response_format,
                function_schemas=self.function_schemas,
                stream=True,
                function_executor=function_executor
            ):
                yield response
                      
    async def process_non_stream(self,completion, model, messages,skip_last_call, function_executor, response_format, max_json_retries):
        self.prompt_tokens += completion.usage.prompt_tokens
        self.completion_tokens += completion.usage.completion_tokens
        self.total_tokens += completion.usage.total_tokens
        model_pricing = self.get_model_pricing(model)
        if model_pricing:
            self.total_cost = self.prompt_tokens * model_pricing['input_token_cost'] + self.completion_tokens * model_pricing['output_token_cost']
        

        response_messages = completion.choices[0].message
        # tool_calls = response_messages.tool_calls
        # tool_calls = response_messages.get('tool_calls', None)
    
        tool_calls = response_messages.tool_calls if hasattr(response_messages, 'tool_calls') else None

        self.llm_logger.info(f"Usage: Prompt Tokens: {completion.usage.prompt_tokens}, Completion Tokens: {completion.usage.completion_tokens}, Tokens: {completion.usage.total_tokens}")
        self.llm_logger.info(f"Total input tokens {self.prompt_tokens}, Total output tokens {self.completion_tokens}, Total tokens {self.total_tokens} Total cost ${self.total_cost:.4f}")
        if tool_calls:
            messages.append(response_messages)
            
            if skip_last_call:
                result = []
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    res = await function_executor(function_name, function_args)
                    self.llm_logger.info(f"LLM OUTPUT function: {function_name}\nArgs: {json.dumps(function_args, indent=2, cls=CustomJSONEncoder)}\n Function Response: {str(res)}")

                    result.append(
                            {
                                "function_name": function_name,
                                "content": res
                            }
                        )
                
                return result
                 
            for tool_call in tool_calls:
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)

                # TODO: need a consistent API for this. The result should be well
                # known type and common across all functions/agents
                function_result = await function_executor(function_name, function_args)
                if isinstance(function_result, Dict):
                    function_result = json.dumps(function_result)
                assert( function_result is not None )
                self.llm_logger.info(f"LLM OUTPUT function: {function_name}\nArgs: {json.dumps(function_args, indent=2, cls=CustomJSONEncoder)}\n Function Response: {str(function_result)}")

                messages.append(
                    {
                        "tool_call_id": tool_call.id,
                        "role": "tool",
                        "name": function_name,
                        "content": function_result
                    }
                )                        

            return 1
        else:

            response_content = response_messages.content
            
            # Check if JSON output was requested
            if response_format and response_format.get("type") == "json_object":
            
                json_response = self.ensure_json_response(response_content)
                
                if "error" in json_response:
                    self.llm_logger.warning(f"Failed to parse LLM response as JSON: {json_response['original_response']}")
                    if json_retry_count < max_json_retries:
                        json_retry_count += 1
                        self.llm_logger.info(f"Retrying JSON generation (Attempt {json_retry_count}/{max_json_retries})")
                        
                        # Add an error message to the conversation
                        error_message = f"The previous response was not valid JSON. Please generate a valid JSON response. Error: {json_response['error']}"
                        messages.append({"role": "user", "content": error_message})
                        return {"error": "Failed to generate valid JSON after multiple attempts", "original_response": error_message}
                    
                    else:
                        self.llm_logger.error("Max JSON generation retries reached. Returning error response.")
                        return {"error": "Failed to generate valid JSON after multiple attempts", "original_response": response_content}
                        
                
                self.llm_logger.info(f"LLM Final Response (JSON):\n{json.dumps(json_response, indent=2, cls=CustomJSONEncoder)}")
                
                # Create a new completion object with the parsed JSON response
                parsed_completion = type(completion)(
                    id=completion.id,
                    choices=[type(completion.choices[0])(
                        index=completion.choices[0].index,
                        message=type(completion.choices[0].message)(
                            role=completion.choices[0].message.role,
                            content=json.dumps(json_response)
                        ),
                        finish_reason=completion.choices[0].finish_reason
                    )],
                    created=completion.created,
                    model=completion.model,
                    usage=completion.usage
                )
                self.llm_logger.info("+++++++++++++++++ END LLM SESSION++++++++")
                return json.loads(parsed_completion.choices[0].message.content)
            else:
                # If JSON wasn't requested, return the original response
                self.llm_logger.info(f"LLM Final Response:\n{response_content}")
                self.llm_logger.info("+++++++++++++++++ END LLM SESSION++++++++")
                return completion.choices[0].message.content
            
                    
    def ensure_json_response(self, response_content):
        try:
            # Try to parse the entire response as JSON
            return json.loads(response_content)
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from the response
            try:
                # Look for the first '{' and last '}'
                start = response_content.index('{')
                end = response_content.rindex('}') + 1
                json_str = response_content[start:end]
                return json.loads(json_str)
            except (ValueError, json.JSONDecodeError):
                # If all else fails, return an error JSON object
                return {"error": "Failed to parse response as JSON", "original_response": response_content}

    def exponential_backoff(self, retry):
        # Optimized exponential backoff formula: faster recovery for knowledge ingestion
        # Start with 0.5 seconds, max out at 5 seconds instead of growing indefinitely
        return min(0.5 * (2 ** retry), 5.0)

    def generate_embedding(self, properties):
        # Convert properties to a string representation for embedding generation
        properties_text = self.properties_to_text(properties)
        # Generate embedding using OpenAI's API
        response = self.client.embeddings.create(
            input=properties_text,
            model="text-embedding-ada-002"  # Choose an appropriate model for your use case
        )
        embedding = response.data[0].embedding
        return embedding

    def properties_to_text(self, properties):
        # Convert node properties to a text representation
        # This could be as simple as concatenating key-value pairs or more complex depending on your needs
        return ", ".join([f"{key}: {value}" for key, value in properties.items()])
    
    def log_token_stats(self):

        pass

    def get_total_cost(self):
        return self.total_cost

    def get_model_pricing(self, model):
        pricing_table = [
            {
                "model": "gpt-4o",
                "input_token_cost": 5.0/1000000,
                "output_token_cost": 15.0/1000000
            },
            {
                "model": "gpt-4-turbo",
                "input_token_cost": 10.0/1000000,
                "output_token_cost": 30.0/1000000
            },
            {
                "model": "gpt-3.5-turbo",
                "input_token_cost": 0.5/1000000,
                "output_token_cost": 1.5/1000000
            },
            {
                "model": "gpt-4o-mini",
                "input_token_cost": 0.15/1000000,
                "output_token_cost": 0.6/1000000
            },
            {
                "model": "claude-2",
                "input_token_cost": 11.02/1000000,
                "output_token_cost": 32.68/1000000
            },
            {
                "model": "claude-instant-1",
                "input_token_cost": 1.63/1000000,
                "output_token_cost": 5.51/1000000
            },
            {
                "model": "claude-3-5-sonnet-20240620",
                "input_token_cost": 3.0/1000000,
                "output_token_cost": 15.0/1000000
            },
            {
                "model": "claude-3-haiku-20240307",
                "input_token_cost": 0.25/1000000,
                "output_token_cost": 1.25/1000000
            },
        ]

        for pricing in pricing_table:
            if pricing["model"] == model:
                return pricing