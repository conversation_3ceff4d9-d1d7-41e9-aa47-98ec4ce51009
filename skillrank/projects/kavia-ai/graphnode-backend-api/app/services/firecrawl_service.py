"""
Firecrawl service for web scraping functionality.
Handles all Firecrawl API interactions with proper error handling.
"""

import os
import aiohttp
from typing import Dict, Any, List, Optional
from app.telemetry.logger_config import get_logger

logger = get_logger(__name__)


class FirecrawlService:
    """Service for handling Firecrawl API operations."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv("FIRECRAWL_API_KEY")
        if not self.api_key:
            raise ValueError("FIRECRAWL_API_KEY environment variable is required")
        
        self.base_url = "https://api.firecrawl.dev/v0"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.max_content_length = 8000

    async def scrape_url(
        self, 
        url: str, 
        include_html: bool = False, 
        wait_for: int = 1000
    ) -> Dict[str, Any]:
        """Scrape content from a single URL."""
        payload = {
            "url": url,
            "formats": ["markdown", "html"] if include_html else ["markdown"],
            "onlyMainContent": True,
            "waitFor": wait_for,
            "timeout": 30000
        }
        
        logger.info(f"Scraping URL: {url}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/scrape",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    result = await response.json()
                    
                    if response.status != 200 or not result.get("success"):
                        error_msg = result.get("error", f"HTTP {response.status}")
                        logger.error(f"Firecrawl scrape failed for {url}: {error_msg}")
                        return self._error_response(url, error_msg)
                    
                    return self._process_scrape_result(result, url, include_html)
                    
        except Exception as e:
            logger.error(f"Scraping error for {url}: {str(e)}")
            return self._error_response(url, str(e))

    async def crawl_website(
        self,
        url: str,
        max_pages: int = 5,
        include_paths: Optional[List[str]] = None,
        exclude_paths: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Crawl multiple pages from a website."""
        payload = {
            "url": url,
            "limit": max_pages,
            "scrapeOptions": {
                "formats": ["markdown"],
                "onlyMainContent": True
            }
        }
        
        if include_paths:
            payload["includePaths"] = include_paths
        if exclude_paths:
            payload["excludePaths"] = exclude_paths
        
        logger.info(f"Crawling website: {url} (max {max_pages} pages)")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/crawl",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    result = await response.json()
                    
                    if response.status != 200 or not result.get("success"):
                        error_msg = result.get("error", f"HTTP {response.status}")
                        logger.error(f"Firecrawl crawl failed for {url}: {error_msg}")
                        return {"success": False, "error": error_msg, "pages": []}
                    
                    return self._process_crawl_result(result, url)
                    
        except Exception as e:
            logger.error(f"Crawling error for {url}: {str(e)}")
            return {"success": False, "error": str(e), "pages": []}

    def _process_scrape_result(
        self, 
        result: Dict[str, Any], 
        url: str, 
        include_html: bool
    ) -> Dict[str, Any]:
        """Process and clean scrape result."""
        data = result.get("data", {})
        metadata = data.get("metadata", {})
        
        content = self._clean_content(data.get("markdown", ""))
        
        return {
            "success": True,
            "url": url,
            "content": content,
            "title": metadata.get("title", ""),
            "description": metadata.get("description", ""),
            "html": data.get("html") if include_html else None,
            "metadata": metadata
        }

    def _process_crawl_result(self, result: Dict[str, Any], base_url: str) -> Dict[str, Any]:
        """Process and clean crawl result."""
        pages_data = result.get("data", [])
        processed_pages = []
        
        for page in pages_data:
            content = self._clean_content(page.get("markdown", ""))
            metadata = page.get("metadata", {})
            
            processed_pages.append({
                "url": metadata.get("sourceURL", ""),
                "title": metadata.get("title", ""),
                "content": content,
                "description": metadata.get("description", "")
            })
        
        return {
            "success": True,
            "pages": processed_pages,
            "total_pages": len(processed_pages),
            "base_url": base_url
        }

    def _clean_content(self, content: str) -> str:
        """Clean and optimize content for LLM consumption."""
        if not content:
            return ""
        
        # Remove excessive whitespace and empty lines
        lines = [line.strip() for line in content.split('\n')]
        cleaned_lines = []
        
        for line in lines:
            if line or (cleaned_lines and cleaned_lines[-1]):
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        
        # Truncate if too long
        if len(cleaned_content) > self.max_content_length:
            cleaned_content = cleaned_content[:self.max_content_length] + "\n\n[Content truncated...]"
        
        return cleaned_content

    def _error_response(self, url: str, error: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "success": False,
            "url": url,
            "content": "",
            "error": error,
            "title": "",
            "description": "",
            "metadata": {}
        }