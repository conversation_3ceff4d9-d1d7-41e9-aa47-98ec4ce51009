"""
Tools service for handling LLM tool definitions and execution.
Manages web scraping tools and their execution logic.
"""

import json
from typing import Dict, Any, List
from .firecrawl_service import FirecrawlService
from app.telemetry.logger_config import get_logger

logger = get_logger(__name__)


class ToolsService:
    """Service for managing LLM tools and their execution."""
    
    def __init__(self, firecrawl_service: FirecrawlService):
        self.firecrawl = firecrawl_service
    
    @property
    def tool_definitions(self) -> List[Dict[str, Any]]:
        """Get available tool definitions for LLM."""
        return [
            {
                "type": "function",
                "function": {
                    "name": "scrape_url",
                    "description": "Scrape content from a Kavia AI documentation page or resource URL. Use this to get the latest information about Kavia AI features, APIs, guides, or troubleshooting steps.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "The Kavia AI documentation or resource URL to scrape content from"
                            },
                            "include_html": {
                                "type": "boolean",
                                "description": "Whether to include HTML content (useful for code examples)",
                                "default": False
                            },
                            "wait_for": {
                                "type": "integer", 
                                "description": "Page load wait time in milliseconds",
                                "default": 1000
                            }
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "crawl_website",
                    "description": "Crawl multiple pages from Kavia AI docs or related sites. Use this when you need to search across multiple documentation sections or find comprehensive information about a Kavia AI topic.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "The base Kavia AI documentation URL to start crawling from (e.g., https://kavia.ai/docs)"
                            },
                            "max_pages": {
                                "type": "integer",
                                "description": "Maximum number of pages to crawl (recommended: 3-10 for focused searches)",
                                "default": 5
                            },
                            "include_paths": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific Kavia AI doc paths to include (e.g., ['/docs/guides/auth/', '/docs/guides/database/', '/docs/reference/'])"
                            },  
                            "exclude_paths": {
                                "type": "array", 
                                "items": {"type": "string"},
                                "description": "Paths to exclude from crawling (e.g., ['/blog/', '/careers/'])"
                            }
                        },
                        "required": ["url"]
                    }
                }
            }
        ]
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool call and return standardized result."""
        try:
            if tool_name == "scrape_url":
                return await self._execute_scrape_url(arguments)
            elif tool_name == "crawl_website":
                return await self._execute_crawl_website(arguments)
            else:
                return self._tool_error(f"Unknown tool: {tool_name}")
                
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name}: {str(e)}")
            return self._tool_error(str(e))

    async def _execute_scrape_url(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Execute URL scraping tool."""
        url = args.get("url")
        include_html = args.get("include_html", False)
        wait_for = args.get("wait_for", 1000)
        
        logger.info(f"Executing scrape_url: {url}")
        result = await self.firecrawl.scrape_url(url, include_html, wait_for)
        
        logger.info(f"Scrape URL result -----------------------------------------============: {result}")
        
        return {
            "tool": "scrape_url",
            "success": result["success"],
            "url": result["url"],
            "content": result["content"],
            "title": result.get("title", ""),
            "metadata": result.get("metadata", {}),
            "error": result.get("error")
        }

    async def _execute_crawl_website(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Execute website crawling tool."""
        url = args.get("url")
        max_pages = args.get("max_pages", 5)
        include_paths = args.get("include_paths")
        exclude_paths = args.get("exclude_paths")
        
        logger.info(f"Executing crawl_website: {url}")
        result = await self.firecrawl.crawl_website(url, max_pages, include_paths, exclude_paths)
        
        # Combine content for LLM
        combined_content = self._combine_page_content(result.get("pages", []))
        
        return {
            "tool": "crawl_website",
            "success": result["success"],
            "base_url": url,
            "total_pages": result.get("total_pages", 0),
            "combined_content": combined_content,
            "individual_pages": result.get("pages", []),
            "error": result.get("error")
        }

    def _combine_page_content(self, pages: List[Dict[str, Any]]) -> str:
        """Combine content from multiple pages for LLM consumption."""
        if not pages:
            return ""
        
        combined = []
        for page in pages:
            title = page.get("title", "Untitled")
            url = page.get("url", "")
            content = page.get("content", "")
            
            if content:
                combined.append(f"--- {title} ({url}) ---\n{content}")
        
        return "\n\n".join(combined)

    def _tool_error(self, error_message: str) -> Dict[str, Any]:
        """Create standardized tool error response."""
        return {
            "success": False,
            "error": error_message
        }