from typing import Dict, Any, List
from .firecrawl_service import FirecrawlService
from app.telemetry.logger_config import get_logger

logger = get_logger(__name__)


class ToolsService:
    def __init__(self, firecrawl_service: FirecrawlService):
        self.firecrawl = firecrawl_service
    
    @property
    def tool_definitions(self) -> List[Dict[str, Any]]:
        return [
            {
                "type": "function",
                "function": {
                    "name": "scrape_url",
                    "description": "Scrape content from a specific Kavia AI documentation page",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "Kavia AI documentation URL (must be from kavia.ai/documentation/)"
                            }
                        },
                        "required": ["url"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "crawl_website",
                    "description": "Crawl multiple pages from Kavia AI documentation",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "Base Kavia AI documentation URL"
                            },
                            "max_pages": {
                                "type": "integer",
                                "description": "Maximum pages to crawl (1-10)",
                                "default": 5
                            }
                        },
                        "required": ["url"]
                    }
                }
            }
        ]
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Fixed URL validation - check for base documentation path
            if tool_name in ["scrape_url", "crawl_website"]:
                url = arguments.get("url")
                if not url or not isinstance(url, str) or not url.startswith("https://kavia.ai/documentation/"):
                    return {
                        "success": False,
                        "error": "Invalid or missing Kavia AI URL. Must start with https://kavia.ai/documentation/",
                        "tool": tool_name
                    }
            
            if tool_name == "scrape_url":
                return await self._scrape_url(arguments)
                
            elif tool_name == "crawl_website":
                return await self._crawl_website(arguments)
                
            else:
                return {
                    "success": False,
                    "error": f"Unknown tool: {tool_name}",
                    "tool": tool_name
                }
                
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name
            }

    async def _scrape_url(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        url = arguments["url"]
        result = await self.firecrawl.scrape_url(url)
        
        return {
            "tool": "scrape_url",
            "success": result["success"],
            "url": result["url"],
            "content": result["content"],
            "error": result.get("error")
        }

    async def _crawl_website(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        url = arguments["url"]
        max_pages = arguments.get("max_pages", 5)
        
        result = await self.firecrawl.crawl_website(url, max_pages)
        
        if not result["success"]:
            return {
                "tool": "crawl_website",
                "success": False,
                "url": url,
                "error": result.get("error"),
                "content": ""
            }
        
        # Combine content from all pages
        pages = result.get("pages", [])
        combined_content = self._combine_content(pages)
        
        return {
            "tool": "crawl_website",
            "success": True,
            "url": url,
            "content": combined_content,
            "pages_found": len(pages)
        }

    def _combine_content(self, pages: List[Dict[str, Any]]) -> str:
        if not pages:
            return ""
        
        combined = []
        for page in pages:
            url = page.get("url", "")
            content = page.get("content", "")
            if content:
                combined.append(f"=== {url} ===\n{content}")
        
        return "\n\n".join(combined)