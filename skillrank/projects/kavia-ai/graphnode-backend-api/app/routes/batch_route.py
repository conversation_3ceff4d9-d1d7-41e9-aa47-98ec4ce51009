import asyncio
import json
import random
import string
from fastapi import APIRouter, HTTPException,Depends, Query, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import  Optional, List
import requests
import time
import uuid
import os
import zipfile
import io
import subprocess
from app.core.task_framework import Task
from app.models.user_model import LLMModel
from app.classes.Ec2Handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.connection.establish_db_connection import get_node_db, NodeDB, get_mongo_db, MongoDBHandler, get_mongo_db_v1, TaskRepository
from app.repository.mongodb.client import get_db
from app.core.Settings import settings
from app.core.constants import TASKS_COLLECTION_NAME as tasks_collection_name, TaskStatus, REPOSITORIES_COLLECTION
from app.tasks import upstream
from app.utils.codegen_import_utils import import_codebase_for_project
from app.utils.kg_build.sync_ebs_efs import do_sync, get_project_paths
from app.utils.project_utils import get_stage, name_to_slug
from app.utils.auth_utils import get_current_user 
from app.utils.respository_utils import check_branch_exists
from app.utils.user_utils import get_module_configuration_for_user
from fastapi.responses import StreamingResponse
from app.models.code_generation_model import Message
from app.core.git_tools import EnhancedGitTools
from app.utils.code_generation_utils import get_codegeneration_path
from app.models.scm import ACCESS_TOKEN_PATH
from pydantic import BaseModel, Field
from typing import Optional
from app.connection.tenant_middleware import get_tenant_id
from fastapi import Body
from app.utils.datetime_utils import generate_timestamp
from app.utils.stream_utils import format_response_for_stream
import shutil
import tempfile
from app.utils.task_utils import generate_task_id, get_codegen_url
from app.classes.S3Handler import S3Handler
from app.services.session_tracker import get_session_tracker
from app.core.kubernetes_monitor import kubernetes_manager
from app.core.Settings import settings


# Global set to track active upstream tasks to prevent duplicates
_active_upstream_tasks = set()

async def trigger_automatic_upstream(project_id: int, build_id: str, user_id: str, repository_name: str):
    """
    Trigger automatic upstream synchronization for a repository with save_and_merge: 1.

    This function is called whenever a repository has save_and_merge: 1, indicating
    the user has performed a save operation and the repository should be synchronized.

    Args:
        project_id: Project ID
        build_id: Build ID for the repository
        user_id: User ID
        repository_name: Name of the repository
    """
    try:
        from app.knowledge.code_query import KnowledegeBuild
        from app.routes.code_query import generate_random_prefix

        # Create a unique key to prevent duplicate upstream tasks
        upstream_key = f"{project_id}:{build_id}:{repository_name}"

        # Check if upstream task is already running for this repository
        if upstream_key in _active_upstream_tasks:
            print(generate_timestamp(), f"⚠️ Upstream task already running for repository {repository_name} (build_id: {build_id})")
            return {
                "status": "skipped",
                "message": f"Upstream task already running for {repository_name}"
            }

        # Mark this upstream task as active
        _active_upstream_tasks.add(upstream_key)

        # Generate build session ID for upstream operation
        build_session_id = generate_random_prefix() + '-' + build_id

        print(generate_timestamp(), f"🚀 Starting automatic upstream synchronization for repository: {repository_name}")

        # Update KG status to indicate upstream operation is starting
        kg = KnowledegeBuild()
        await kg.update_kg_status(4, project_id, [build_id], build_session_id)  # Status 4 = upstream in progress
        await kg.update_build_times(project_id, [build_id], "start_time", True)

        # Schedule upstream task using the same pattern as kg_route.py
        task = Task.schedule_task(
            upstream,
            project_id=project_id,
            build_session_id=build_session_id,
            build_id=build_id,
            tenant_id=get_tenant_id(),
            current_user=user_id,
        )

        print(generate_timestamp(), f"✅ Scheduled automatic upstream task {task.task_id} for repository: {repository_name}")

        # Remove from active tasks after a delay (cleanup mechanism)
        import asyncio
        async def cleanup_task():
            await asyncio.sleep(300)  # 5 minutes cleanup delay
            _active_upstream_tasks.discard(upstream_key)

        asyncio.create_task(cleanup_task())

        return {
            "status": "success",
            "message": f"Automatic upstream synchronization started for {repository_name}",
            "task_id": task.task_id,
            "build_session_id": build_session_id
        }

    except Exception as e:
        # Remove from active tasks on error
        upstream_key = f"{project_id}:{build_id}:{repository_name}"
        _active_upstream_tasks.discard(upstream_key)

        print(generate_timestamp(), f"❌ Error triggering automatic upstream for repository {repository_name}: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to trigger automatic upstream: {str(e)}"
        }


class CreatePRRequest(BaseModel):
    destination_branch: str
    title: Optional[str] = Field(None, description="PR title. If not provided, will use default format")
    description: Optional[str] = Field(None, description="PR description. If not provided, will use default format")
    

_SHOW_NAME = "batch"
MAX_CODE_MAINTENANCE_SESSIONS = 3
class CodeGenerationResponse(BaseModel):
    job_id: str = ''
    task_id: str = ''
    iframe: str = ''
    message: str = ''
    wait: int = 0

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {'description': 'Not found'}},
)

VSCODE_CLOUD_URL = settings.VSCODE_CLOUD_URL

            
class UserInputRequest(BaseModel):
    user_input: str


    


def get_pod_url(stage, pod_prefix: str):

    """
    Generate the pod URL based on the tenant ID, project ID, and stage.
    """
    if stage == "dev":
        return f"https://vscode-internal-{pod_prefix}-dev.dev01.cloud.kavia.ai"
    elif stage == "qa":
        return f"https://vscode-internal-{pod_prefix}-qa.qa01.cloud.kavia.ai"
    elif stage == "pre_prod":
        return f"https://vscode-internal-{pod_prefix}-beta.beta01.cloud.kavia.ai"
    else:
        return f"https://vscode-internal-{pod_prefix}-{stage}.{stage}.cloud.kavia.ai"

@router.get("/get_active_task/{project_id}")
async def get_active_task(
    project_id: int,
    container_id=None, 
    architecture_id=None,
    custom_filter=None,
):
    mongo_db = get_mongo_db()
    find_query = {
        "project_id": project_id,
        "status": {"$regex": f"^({'submitted|started|pending|running|paused|in_progress' if custom_filter == None else custom_filter})$", "$options": "i"}
    }
    if container_id:
        find_query["container_id"] = container_id
    if architecture_id:
        find_query["architecture_id"] = architecture_id
    
    active_task = mongo_db.db[tasks_collection_name].find_one(
        find_query,
        {'_id':1,'job_id':1,'status':1,'iframe':1,'ip':1,'architecture_id':1}
    )
    
    previous_task = [i for i in mongo_db.db["tf_tasks"].find(
        {
            "project_id": project_id, 
            "status": {"$regex": "^(complete)$", "$options": "i"}
        }
    ).sort('start_time', -1).limit(1)]
    
    if previous_task:
        previous_task = previous_task[0]
    else:
        previous_task = None

    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            pass
    return {
        "message": "No active task found",
        "prev_task_id":str(previous_task.get("_id")) if previous_task != None else None
    }  

def cancel_existing_running_tasks(project_id: int, mongo_db: MongoDBHandler):
    # Query to find RUNNING tasks
    query = {
        'project_id': project_id,
        'status': 'RUNNING'
    }
    
    # Update to change status to STOPPED
    update = {
        '$set': {'status': TaskStatus.STOPPED.value}
    }
    
    # Update the matching documents
    result = mongo_db.db[tasks_collection_name].update_many(query, update)

    return result.modified_count

def run_local_instance(project_id,container_ids,job_name,stage,llm_model,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        tenant_id:str = "T0000",
        user_id:str = "U0000",
        platform:str = "common", 
        test_case: bool = False,
        encrypted_scm_id: str = None
        ):
        os.environ["BATCH_JOB_TRIGGER"] = "True"
        os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
        os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
        os.environ["BATCH_JOB_STAGE"]= stage
        os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL
        os.environ["PLATFORM"] = platform
        os.environ["TEST_CASE"] = str(test_case)
        # print"LOCAL_DEBUG")
        
        input_arguments = json.dumps({
            "project_id": project_id,
            "container_id": container_ids,
            "llm_model": str(llm_model),
            "task_id": job_name,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "platform": platform,
            "test_case": test_case,
            "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace",
            "encrypted_scm_id": encrypted_scm_id,
        })
        os.environ["input_arguments"] = input_arguments
        os.environ["task_id"] = job_name
        # use screen to run the job with log
        
        try:
            os.makedirs("/tmp/kavia/workspace", exist_ok=True)
            os.makedirs("/tmp/kavia/workspace/logs", exist_ok=True)
        except Exception as e:
            print(generate_timestamp(),f"Error creating directory: {e}")
            pass
        #################################
        
        input_args = json.loads(input_arguments) if isinstance(input_arguments, str) else input_arguments
        project_id_ = int(input_args.get("project_id"))
        task_id_ = input_args.get("task_id")
        subprocess.run([
            "screen", "-L", 
            "-Logfile", f"/tmp/kavia/workspace/logs/{job_name}.log",
            "-dmS", job_name, 
            "bash", "-c", 
            f"python app/batch_jobs/jobs.py --input_args '{input_arguments}' --stage {stage} | ts '[%Y-%m-%d %H:%M:%S]'"
        ], env=os.environ)
        # #run the job in background without screen for debugging
        # background_tasks.add_task(subprocess.run, ["python", "app/batch_jobs/jobs.py"], env=os.environ)
        
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": job_name},
            {"$set": {
                "job_id": job_name,
                "project_id": project_id,
                "container_id": container_ids,
                "container_ids": container_ids,
                "llm_model": str(llm_model),
                "start_time": generate_timestamp(),
                "status": "SUBMITTED",
                "user_id": user_id,
                "platform": platform,
                "test_case": test_case,
                "context": {},
                "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation/",
                "encrypted_scm_id": encrypted_scm_id
            }},
            upsert=True
        )
        
        return {
            "task_id": job_name,
            "llm_model": llm_model,
            "container_id": container_ids,
            "message": "Job submitted successfully"
        }

def run_local_deep_query(project_id,selected_repos,job_name,stage,llm_model,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        tenant_id:str = "T0000",
        user_id:str = "U0000",
        ):
    os.environ["BATCH_JOB_TRIGGER"] = "True"
    os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
    os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
    os.environ["BATCH_JOB_STAGE"]= stage
    os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL

    input_arguments = json.dumps({
            "project_id": project_id,
            "llm_model": str(llm_model),
            "task_id": job_name,
            "user_id": user_id,
            "agent_name": "DocumentCreation",
            "tenant_id": tenant_id,
        })
    
    os.environ["input_arguments"] = input_arguments
    os.environ["task_id"] = job_name
    # use screen to run the job with log
    print(generate_timestamp(),"Running screen", job_name)
    subprocess.run(["screen", "-L", "-Logfile", f"logs/{job_name}.log", "-dmS", job_name, "python", "app/batch_jobs/jobs.py", "--input_args", input_arguments, "--stage", stage], env=os.environ)

    mongo_db.db[tasks_collection_name].update_one(
            {"_id": job_name},
            {"$set": {
                "job_id": job_name,
                "project_id": project_id,
                "llm_model": str(llm_model),
                "agent_name": "DocumentCreation",
                "start_time": generate_timestamp(),
                "status": "SUBMITTED",
                "user_id": user_id,
                "context": {},
                "repositories": selected_repos,
                "iframe": "http://localhost:8080/?folder=/tmp/kavia/workspace",
                "pod_id": "local-debug",
                "pod_name": "local-debug",
                "pod_prefix": "local-debug",
                "stage": stage,
            }},
            upsert=True
        )
        
    return {
        "task_id": job_name,
        "llm_model": llm_model,
        "message": "Job submitted successfully"
    }


# Need to add health check for the instance (3/3) Minumum 2 instance
async def stream_start_workspace_status(
        project_id: int,
        container_ids: list,
        session_name:str,
        description:str,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        node_db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user),
        new_repo_creation: bool = True,
        resume: bool = False,
        resume_task_id: str = "",
        test_case: bool = False,
        encrypted_scm_id = None
        ):
    """
    Generator function to stream workspace status as Server-Sent Events (SSE)
    """
    try:
        db_execution_time = None
        start_time = time.time()
        tenant_id = get_tenant_id()
        job_name = generate_task_id("cg") if not resume else resume_task_id
        
        if resume:
            task = mongo_db.db[tasks_collection_name].find_one({"_id": job_name})
            print(generate_timestamp(),"Resume task container ids", task.get("container_ids", []))
            if task:
                container_ids = task.get("container_ids", [])
            else:
                yield format_response_for_stream({'error':'Task not found','end':True})
                return

                
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username') if isinstance(current_user, dict) else None
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        print(generate_timestamp(),"TESTCASEGENERATION", container_ids, project_id,)
        project = await node_db.get_node_by_label_id(project_id, "Project")
        container_id=container_ids[0]
        container = await node_db.get_node_by_label_id(container_id, "Container")
        
        if not container:
            yield format_response_for_stream({'error':'Container is not configured','end':True})
            return
            
        container_id = container.get("id")
        platform = container.get("properties", {}).get("platform", "common")

        if not new_repo_creation:
            project_repositories = json.loads(project.get("properties", {}).get("repositories", "{}"))
            container_repository = project_repositories.get(str(container_id))
            if not container_repository:
                yield format_response_for_stream({'error':'Container repository is not configured','end':True})
                return

        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                container_id=container_ids,
                service_type="code-generation",
                session_data={
                    "session_name": session_name,
                    "description": description,
                    "platform": platform,
                    "llm_model": llm_model,
                }
            )
            
            if session_result["success"]:
                print(generate_timestamp(),f"Session tracking initialized for task_id: {job_name}")
            else:
                print(generate_timestamp(),f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(generate_timestamp(),f"Session tracking initialization error: {session_error}") 
            
            
        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            print(generate_timestamp(),"Local debug true")
            response = run_local_instance(project_id,container_ids,job_name,stage,llm_model, background_tasks,mongo_db, tenant_id, user_id=user_id, platform=platform,
                                          test_case=test_case, encrypted_scm_id=encrypted_scm_id)
            response['end'] = True
            yield format_response_for_stream(response)
        else:
            stage_params = 'dev' if stage == 'develop' else stage
            
            db_start_time = time.time()
            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()


            # Always assing a new pod for each session

            # Use findOneAndDelete for atomic operation
            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id, project_id=str(project_id))
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            print(generate_timestamp(),"Selected Pod: ", selected_pod)
            pod_prefix = selected_pod.get("project_id", "")
            
            
        
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_prefix} assigned in {assign_time:.2f} seconds'})
            
            
            # Continue with the same workflow as in the else block
            print(generate_timestamp(),"Instance name : ", pod_prefix)
            

            pod_url = get_pod_url(stage_params, pod_prefix)
            vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/code-generation/"
            application_preivew_url = pod_url
      
            planned_job_id = job_name
            anticipate_message_dict = {
                "message": "install",
                "planned_job_id": planned_job_id,
                "end": False
            }
            
            yield format_response_for_stream(anticipate_message_dict)
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&platform={platform}&pod_id={pod_prefix}&user_id={user_id}'
            
            ip = '127.0.0.1'
            
            yield format_response_for_stream({'message': 'install|provision|config'})
            
            iframe_url = vs_code_complete_url
            ports = [{f'{port}': vs_code_complete_url} for port in [3000, 5000, 8000]]
            
            # Submit request before start codegeneration
            mongo_db.db[tasks_collection_name].update_one(
                {"_id": job_name},
                {
                    "$set": {
                        "job_id": job_name,
                        "project_id": project_id,
                        "container_id": container_ids,
                        "container_ids": container_ids,
                        "session_name": session_name,
                        "description": description,
                        "status": "SUBMITTED",
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        "context": {},
                        "llm_model": llm_model,
                        "user_id": user_id,
                        "platform": platform,
                        "params": params,
                        "stage": stage_params,
                        "new_repo_creation": new_repo_creation,
                        "start_time": generate_timestamp(),
                        "application_preivew_url": application_preivew_url,
                        "pod_id": pod_prefix,
                        "pod_name": pod_prefix,
                        "encrypted_scm_id": encrypted_scm_id,
                        "resume": resume,
                    }
                },
                upsert=True
            )
            
            # Maximum number of retries
            MAX_RETRIES = 15
            # Delay between retries in seconds
            RETRY_DELAY = 2
            
            start_maintenance_url = get_codegen_url(stage_params, pod_prefix=pod_prefix)
            start_maintenance_url = f'{start_maintenance_url}/start'+ params


            print(generate_timestamp(),"Calling", start_maintenance_url)
            db_execution_time = time.time() - db_start_time
    
            # Print results
            print(generate_timestamp(),f"Execution time: {db_execution_time:.4f} seconds")
                
            connected = False
            attempts = 0
            
            while not connected and attempts < MAX_RETRIES:
                try:
                    resp = requests.get(start_maintenance_url, verify=False, timeout=5)
                    
                    if resp.status_code == 200:
                        connected = True
                        print(generate_timestamp(),f"Connection successful after {attempts+1} attempts")
                    else:
                        attempts += 1
                        print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'debug_error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...'
                        })
                        time.sleep(RETRY_DELAY)
                except requests.RequestException as e:
                    attempts += 1
                    print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                    yield format_response_for_stream({
                        'message':'install|provision|config',
                        'debug_error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...'
                    })
                    time.sleep(RETRY_DELAY)

            if connected:
                success = True
                
                if success:
                    end_time = time.time()
                    workspace_elapsed_time = end_time - start_time

                    yield format_response_for_stream({
                        'message': 'install|provision|config|final',
                        'k8_job_creation': workspace_elapsed_time,
                        "task_id": job_name,
                        'db_execution_time': db_execution_time,
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        'end': True
                    })
                                         
                else:
                    yield format_response_for_stream({
                        'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                        'end': True
                    })
                        
            else:
                
                yield format_response_for_stream({
                    'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                    'end': True
                })
                
            return
         
        return # Terminate the existing Connection 
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(generate_timestamp(),f"Exception in stream_start_workspace_status: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        
@router.post("/start_code_generation/{project_id}/")
async def submit_code_generation_job_opt(
        project_id: int,
        container_ids: Optional[List[int]] = Body(default=None, description="List of container IDs (made optional for testcase)"),
        session_name: str = Body(default="Untitled"),
        description: str = Body(default="Untitled"),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user),
        resume: bool = Query(False, description="Resume flag"),
        resume_task_id: str = Query("", description="Resume task id"),
        test_case: bool = Query(False, description="Test case flag")
    ): 
    if test_case: 

        node_query = '''MATCH (n:Project)-[:HAS_CHILD]->(s:SystemContext)-[:HAS_CHILD]->(c:Container) 
        WHERE ID(n) = $node_id 
        AND c.ContainerType = 'internal' 
        AND (c.Repository_Name IS NOT NULL OR c.repository_name IS NOT NULL)
        RETURN ID(c) AS container_id'''

        node_result = await db.async_run(node_query, node_id=project_id)
        node_data = node_result.data()

        container_id = None
        if node_data:
            container_id = node_data[0].get('container_id')

        if not container_id:
            return {"error": "No valid container found for this project"}
        
        # For test case, override container_ids with the found container
        container_ids = [container_id]

    return StreamingResponse(
        stream_start_workspace_status(
            project_id, container_ids, session_name, description, background_tasks, mongo_db, db, current_user,
            resume=resume, resume_task_id=resume_task_id, test_case=test_case
        ),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )


@router.get("/past_maintenance_tasks/{project_id}")
async def get_past_maintenance_tasks(
    project_id: int,
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    current_user = Depends(get_current_user),
    agent_name = Query('CodeMaintenance', alias="agent"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Retrieve past code maintenance tasks for a given project_id.
    """
    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    query = {
        "project_id": int(project_id),
        "agent_name": agent_name
    }

    
    projection = {
        "_id": 1,
        "status": 1,
        "llm_model": 1,
        "agent_name": 1,
        "start_time": 1,
        "session_id": 1,
        "messages":1,
        "description": 1,
        "session_name": 1,
        "start_time":1
    }
    sort = [("start_time", -1)]

    tasks = await task_repository.find_many(query, db, projection, skip=skip, limit=limit, sort=sort)

    
    # Convert ObjectId to string in tasks
    serialized_tasks = []
    for task in tasks:
        task['_id'] = str(task['_id'])  # Convert ObjectId to string
        serialized_tasks.append(task)

    total_count = await task_repository.count_documents(query, db)

    # try:
        # ec2_handler = Ec2Handler()
        # print(generate_timestamp(),"Invoking ec2 instance")
        # tenant_id = current_user.get("custom:tenant_id")
        # stage = get_stage() 
        # instance_name =f"{tenant_id}-{project_id}-{stage}"
        # background_tasks.add_task(
        #     ec2_handler.wake_up_instance,
        #     instance_name=instance_name)
    # except Exception as e:
    #     print(generate_timestamp(),"Error tracking project usage: ", str(e))
    #     pass
    return {
        "tasks": serialized_tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }

@router.get("/active_maintenance_sessions/{project_id}")
async def get_active_maintenance_sessions(
    project_id: int,
):
    return await get_active_code_maintenance_sessions(project_id)

@router.delete("/past_maintenance_tasks/{task_id}")
async def delete_maintenance_task(
    task_id: str,
):
    """
    Delete a specific maintenance task by its ID.
    """
    # return(task_id)
    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # # Convert string ID to ObjectId for MongoDB query
    # from bson import ObjectId
    # try:
    #     object_id = ObjectId(task_id)
    # except:
    #     raise HTTPException(status_code=400, detail="Invalid task ID format")

    # Query to find and delete the specific task
    query = {
        "_id": task_id
        # "agent_name": "CodeMaintenance"
    }
    print(generate_timestamp(),task_id)
    # Delete the task
    result = await task_repository.delete_one(query, db)
    
    if result==False:
        raise HTTPException(status_code=404, detail="Task not found")

    
    return {"message": "Task deleted successfully"}


async def get_active_maintenance_task(project_id: int):
    """Get the last active code maintenance task for a project"""
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name=tasks_collection_name)
    tenant_id = get_tenant_id()
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # Query for active task
    query = {
        "project_id": project_id,
        "agent_name": "CodeMaintenance",
        "status": {
            "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
            "$options": "i"
        }
    }
    projection = {
        "_id": 1, 
        "job_id": 1, 
        "status": 1, 
        "iframe": 1, 
        "ip": 1
    }
    
    active_task = await task_repository.find_one(query, db, projection)
    
    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            return {}
            
    
    # Query for previous completed task
    prev_query = {
        "project_id": project_id,
        "agent_name": "CodeMaintenance",
        "status": "COMPLETE"
    }
    previous_task = await task_repository.find_one(
        prev_query, 
        db
    )
    
    return {
        "message": "No active maintenance task found",
        "prev_task_id": str(previous_task.get("_id")) if previous_task else None
    }

async def get_active_code_maintenance_sessions(project_id: int):
    try:
        db = get_mongo_db().db
        query = {
            "project_id": int(project_id),
            "agent_name": "CodeMaintenance",
            "status": {
                "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
                "$options": "i"
            }
        }
        projection = {
            "_id": 1,
            "job_id": 1,
            "status": 1,
            "session_name": 1,
            "start_time": 1,
            "ip": 1
        }
        active_sessions = db[tasks_collection_name].find(query, projection)
        active_sessions = list(active_sessions)
        print(generate_timestamp(),"active_sessions",active_sessions)
        print(generate_timestamp(),"len",len(active_sessions))
        return active_sessions
    except Exception as e:
        print(generate_timestamp(),"Error in get_active_code_maintenance_sessions: ", str(e))
        return []

def generate_codegen_hash():
    # Generate 4 random letters (a-z)
    random_chars = ''.join(random.choices(string.ascii_lowercase, k=4))
    return f"{random_chars}"
            
async def stream_code_maintanence(
    project_id: int,
    selected_repos: dict,
    session_name:str,
    description:str,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user),
    resume: bool = False,
    resume_task_id: str = ""
):
    try:
        tenant_id = get_tenant_id()
        job_name = generate_task_id("cm") if not resume else resume_task_id
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username')
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        project_repos = mongo_db.db[REPOSITORIES_COLLECTION].find_one({
            "project_id": project_id
        }
        )
        project_manifest = project_repos.get("project_manifest", "")
        print(generate_timestamp(),selected_repos)
        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                service_type="code-maintenance",
                session_data={
                    "session_name": session_name,
                    "description": description,
                    "llm_model": llm_model,
                    "selected_repos":selected_repos
                }
            )
            
            if session_result["success"]:
                print(generate_timestamp(),f"Session tracking initialized for task_id: {job_name}")
            else:
                print(generate_timestamp(),f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(generate_timestamp(),f"Session tracking initialization error: {session_error}")  

        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            os.environ["BATCH_JOB_TRIGGER"] = "True"
            os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
            os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
            os.environ["BATCH_JOB_STAGE"] = stage
            os.environ["CELERY_BROKER_URL"] = settings.CELERY_BROKER_URL
            yield format_response_for_stream({'message':'install|provision|config'})

            input_arguments = json.dumps({
                "project_id": project_id,
                "llm_model": str(llm_model),
                "task_id": job_name,
                "tenant_id": tenant_id,
                "agent_name": "CodeMaintenance",
                "user_id": user_id,
                "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation/",
                
            })
            os.environ["input_arguments"] = input_arguments
            os.environ["task_id"] = job_name
            # use screen to run the job with log
            # subprocess.run(["screen", "-L", "-Logfile", f"logs/{job_name}.log", "-dmS", job_name, "python", "app/batch_jobs/jobs.py"], env=os.environ)
            os.makedirs("/tmp/kavia/workspace/logs", exist_ok=True)
            screen_cmd = [
                'screen', '-L', 
                '-Logfile', f"/tmp/kavia/workspace/logs/{job_name}.log",
                '-dmS', job_name, 
                'python3', 'app/batch_jobs/jobs.py',
                '--input_args', input_arguments,  # Pass arguments directly
                '--stage', stage
            ]
            
            subprocess.run(
                screen_cmd,
                check=True,
                env=os.environ
            )
            
            mongo_db.db[tasks_collection_name].update_one(
                {"_id": job_name},
                {"$set": {
                    "job_id": job_name,
                    "project_id": project_id,
                    "session_name": session_name,
                    "description": description,
                    "llm_model": str(llm_model),
                    "agent_name": "CodeMaintenance",
                    "start_time": generate_timestamp(),
                    "status": "SUBMITTED",
                    "user_id": user_id,
                    "context": {},
                    "repositories": selected_repos,
                    "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation/",
                    "pod_id": "local-debug",
                    "pod_name": "local-debug", 
                    "pod_prefix": "local-debug",
                    "stage": stage,
                    "project_manifest": project_manifest,
                }},
                upsert=True
            )
            
            response = {
                "task_id": job_name,
                "llm_model": llm_model,
                "message": "Job submitted successfully",
                "iframe": f"http://localhost:8080/?folder=/tmp/kavia/workspace/code-generation/",
                "end": True
            }
            print(generate_timestamp(),"STREAMING RESPONSE ", response)
            yield format_response_for_stream(response)
            return
        else:
            
            stage_params = 'dev' if stage == 'develop' else stage

            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()
            # Always assign a new pod for each session
            # Use findOneAndDelete for atomic operation
            
            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id, project_id=str(project_id))
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            
            pod_name = selected_pod.get("project_id", "")
            
            

            
            pod_prefix = pod_name
            
            
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_name} assigned in {assign_time:.2f} seconds'})
            
            
            # Continue with the same workflow as in the else block
            print(generate_timestamp(),"Instance name : ", pod_name)
            
            

            pod_url = get_pod_url(stage_params, pod_prefix)
            vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/code-generation/"
            application_preivew_url = pod_url

            
            planned_job_id = job_name
            anticipate_message_dict ={
                "message":"install",
                "planned_job_id":planned_job_id,
                "end":False
            }   
            yield format_response_for_stream(anticipate_message_dict)
            # for stage params and variables
            active_sessions = await get_active_code_maintenance_sessions(project_id)
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&agent_name=CodeMaintenance&platform=common&user_id={user_id}'
            
            if len(active_sessions) >= MAX_CODE_MAINTENANCE_SESSIONS:
                error_message = f"Maximum number of active code maintenance sessions reached. Code maintenance sessions are limited to {MAX_CODE_MAINTENANCE_SESSIONS} at a time. ({len(active_sessions)}/{MAX_CODE_MAINTENANCE_SESSIONS}). Please close some sessions and try again."
                active_sessions = json.dumps(active_sessions)
                yield format_response_for_stream({'error':error_message, 'active_sessions':active_sessions,'end':True})
            else:
                ip = '127.0.0.1'
                yield format_response_for_stream({'message':'install|provision|config'})
                iframe_url = vs_code_complete_url
                ports = [{f'{port}':vs_code_complete_url} for port in [3000,5000,8000]]
                
                mongo_db.db[tasks_collection_name].update_one(
                    {"_id": job_name},
                    {"$set": {
                        "job_id": job_name,
                        "stage": stage_params,
                        "project_id": project_id,
                        "session_name": session_name,
                        "description": description,
                        "llm_model": str(llm_model),
                        "agent_name": "CodeMaintenance",
                        "status": "SUBMITTED",
                        "user_id": user_id,
                        "start_time": generate_timestamp(),
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        "context": {},
                        "params":params,
                        "repositories": selected_repos,
                        "application_preivew_url": application_preivew_url,
                        "pod_id": pod_prefix,
                        "pod_name": pod_prefix,
                        "pod_prefix": pod_prefix,
                        "project_manifest": project_manifest,
                    }},
                    upsert=True
                )
                
                # Maximum number of retries
                MAX_RETRIES = 15  
                # Delay between retries in seconds
                RETRY_DELAY = 2   
                    
                start_maintenance_url = get_codegen_url(stage_params, pod_prefix=pod_prefix)
                start_maintenance_url = f'{start_maintenance_url}/start'+ params
                    
                print(generate_timestamp(),"Calling", start_maintenance_url)

                connected = False
                attempts = 0
                while not connected and attempts < MAX_RETRIES:
                    try:
                        resp = requests.get(start_maintenance_url, verify=False, timeout=5)
                        
                        if resp.status_code == 200:
                            connected = True
                            print(generate_timestamp(),f"Connection successful after {attempts+1} attempts")
                        else:
                            attempts += 1
                            print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                            yield format_response_for_stream({
                                'message':'install|provision|config',
                                'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...'
                            })
                            time.sleep(RETRY_DELAY)
                    except requests.RequestException as e:
                        attempts += 1
                        print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...'
                        })
                        time.sleep(RETRY_DELAY)

                if connected:
                    retry_count = 0
                    max_retries = 15
                    success = False
                    
                    while retry_count < max_retries and not success:
                        try:
                            resp = requests.get(iframe_url, verify=False, timeout=5)
                            if resp.status_code == 200:
                                success = True
                                print(generate_timestamp(),f"Debug: Connection successful on attempt {retry_count+1}")
                            else:
                                error = f"Debug: Got status code {resp.status_code} on attempt {retry_count+1}"
                                retry_count += 1
                                time.sleep(1)  # Add delay between retries
                                
                                yield format_response_for_stream({
                                    "debug_error": error
                                })
                                
                        except Exception as e:
                            print(generate_timestamp(),f"Debug_error: Connection failed on attempt {retry_count+1} with error: {str(e)}")
                            retry_count += 1
                            time.sleep(1)  # Add delay between retries
                    
                    if success:
                        yield format_response_for_stream({
                            'message': 'install|provision|config|final',
                            "task_id": job_name,
                            "ip": ip,
                            "iframe": iframe_url,
                            "ports": ports,
                            'end': True
                        })
                        
                    else:
                        yield format_response_for_stream({
                            'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                            'end': True
                        })
                else:
                    yield format_response_for_stream({
                    'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                    'end': True
                    })
                    
                    
                return
       
    except Exception as e:
        print(generate_timestamp(),f"Error in stream_code_maintanence: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        return

@router.post("/start_code_maintenance/{project_id}/")
async def submit_code_maintanence_job_opt(
        project_id: int,
        selectedrepos: dict = Body(...),
        session_name: str = Body(default="Untitled"),
        description: str = Body(default="Untitled"),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user),
        resume: bool = Query(False, description="Resume flag"),
        resume_task_id: str = Query("", description="Resume task id")
):
        print(generate_timestamp(),"steaming ...")
        return StreamingResponse(
        stream_code_maintanence(project_id,selectedrepos,session_name,description,background_tasks,mongo_db,db,current_user,resume=resume,resume_task_id=resume_task_id),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )


async def get_active_deep_query_task(project_id: int):
    """Get the last active code maintenance task for a project"""
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name=tasks_collection_name)
    tenant_id = get_tenant_id()
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    
    # Query for active task
    query = {
        "project_id": project_id,
        "agent_name": "DocumentCreation",
        "status": {
            "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
            "$options": "i"
        }
    }
    projection = {
        "_id": 1, 
        "job_id": 1, 
        "status": 1, 
        "iframe": 1, 
        "ip": 1
    }
    
    active_task = await task_repository.find_one(query, db, projection)
    
    if active_task:
        try:
            job_id = active_task.get("_id")
            active_task['task_id'] = str(job_id)
            return active_task
        except:
            return {}
            
    
    # Query for previous completed task
    prev_query = {
        "project_id": project_id,
        "agent_name": "DocumentCreation",
        "status": "COMPLETE"
    }
    previous_task = await task_repository.find_one(
        prev_query, 
        db
    )
    
    return {
        "message": "No active maintenance task found",
        "prev_task_id": str(previous_task.get("_id")) if previous_task else None
    }
    
async def stream_deep_query(
        project_id: int,
        selected_repos: dict,
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        node_db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user)
        ):
    """
    Generator function to stream workspace status as Server-Sent Events (SSE)
    """
    try:
        tenant_id = get_tenant_id()
        job_name = f"deep-query-job-{uuid.uuid4().hex[:8]}"
        print(generate_timestamp(),selected_repos)
        
        # Copy the file to tmp folder        
        # temp_workspace = setup_workspace(selected_repos, project_id, tenant_id, job_name)
        
        # print(generate_timestamp(),"Temp workspace : ", temp_workspace)
        
        stage = get_stage(settings)
        user_id = current_user.get('cognito:username')
        model_config = await get_module_configuration_for_user(module_name='code_generation', user_id=user_id)
        # if model_config:
        #     llm_model = model_config.get('llm_model', LLMModel.gpt_4o_mini.value )
        # else:
        #     llm_model = LLMModel.gpt_4o_mini.value
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        try:
            # Get session tracker
            tracker = get_session_tracker()
            
            # Initialize session tracking
            session_result = await tracker.initialize_session(
                task_id=job_name,  # This is your task_id
                tenant_id=tenant_id,
                user_id=user_id,
                project_id=project_id,
                service_type="deep-query",
                session_data={
                    "llm_model": llm_model,
                }
            )
            
            if session_result["success"]:
                print(generate_timestamp(),f"Session tracking initialized for task_id: {job_name}")
            else:
                print(generate_timestamp(),f"Failed to initialize session tracking: {session_result['error']}")
                    
        except Exception as session_error:
            print(generate_timestamp(),f"Session tracking initialization error: {session_error}")
            
        # For local debug
        if os.environ.get("LOCAL_DEBUG"):
            response = run_local_deep_query(project_id,selected_repos,job_name,stage,llm_model, background_tasks,mongo_db, tenant_id, user_id=user_id)
            response['end'] = True
            yield format_response_for_stream(response)
        else:
            ip = '127.0.0.1'
            # for stage params and variables
            stage_params = 'dev' if stage == 'develop' else stage
            params = f'?project_id={project_id}&task_id={job_name}&stage={stage_params}&llm_model={llm_model}&tenant_id={tenant_id}&agent_name=DocumentCreation&platform=common&user_id={user_id}'
            
            yield format_response_for_stream({'message':'install|provision|config', 'end': False} )
            
            stage_params = 'dev' if stage == 'develop' else stage
            
            db_start_time = time.time()
            unique_id = tenant_id + str(project_id)
            # Assign a pod to this project
            start_assign_time = time.time()
            # Always assign a new pod for each session
            # Use findOneAndDelete for atomic operation
            selected_pod = kubernetes_manager.get_one_available_pod_and_mark_as_used(job_name, tenant_id, project_id=str(project_id))
            
            if not selected_pod:
                yield format_response_for_stream({'error': 'No available pods found', 'end': True})
                return
            
            pod_name = selected_pod.get("project_id", "")
            
            
            
            pod_prefix = pod_name
            
            assign_time = time.time() - start_assign_time
            yield format_response_for_stream({'message': f'Pod {pod_name} assigned in {assign_time:.2f} seconds'})
            


            pod_url = get_pod_url(stage_params,pod_prefix=pod_prefix)
            vs_code_complete_url = f"{pod_url}/?folder=/home/<USER>/workspace/{job_name}"
            application_preivew_url = pod_url
                

            iframe_url = vs_code_complete_url

            ports = [{f'{port}':vs_code_complete_url} for port in [3000,5000,8000]]
            mongo_db.db[tasks_collection_name].update_one({"_id": job_name},{
                "$set":{
                "job_id": job_name,
                "project_id": project_id,
                "agent_name": "DocumentCreation",
                "status": "SUBMITTED",
                "ip":ip,
                "iframe":iframe_url,
                "ports":ports,
                "context": {},
                "stage": stage_params,
                "llm_model":llm_model,
                "user_id": user_id,
                "start_time": generate_timestamp(),
                "repositories": selected_repos,
                "params": params,
                "application_preivew_url": application_preivew_url,
                "pod_id": pod_prefix,
                "pod_name": pod_prefix,
                "pod_prefix": pod_prefix,
            }},upsert=True)
                            # Maximum number of retries
            MAX_RETRIES = 15  
            # Delay between retries in seconds
            RETRY_DELAY = 2   
                
            start_query_url = get_codegen_url( stage_params, pod_prefix=pod_prefix)
            start_query_url = f'{start_query_url}/start'+ params

            print(generate_timestamp(),"Calling", start_query_url)

            connected = False
            attempts = 0
            while not connected and attempts < MAX_RETRIES:
                try:
                    resp = requests.get(start_query_url, verify=False, timeout=5)
                    
                    if resp.status_code == 200:
                        connected = True
                        print(generate_timestamp(),f"Connection successful after {attempts+1} attempts")
                    else:
                        attempts += 1
                        print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection failed with status code {resp.status_code}")
                        yield format_response_for_stream({
                            'message':'install|provision|config',
                            'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Status code: {resp.status_code}. Retrying...',
                            'end': False
                        })
                        time.sleep(RETRY_DELAY)
                except requests.RequestException as e:
                    attempts += 1
                    print(generate_timestamp(),f"Attempt {attempts}/{MAX_RETRIES}: Connection error: {str(e)}")
                    yield format_response_for_stream({
                        'message':'install|provision|config',
                        'error': f'Connection attempt {attempts}/{MAX_RETRIES} failed. Error: {str(e)}. Retrying...',
                        'end': False
                    })
                    time.sleep(RETRY_DELAY)

            if connected:
                retry_count = 0
                max_retries = 15
                success = False
                
                while retry_count < max_retries and not success:
                    try:
                        resp = requests.get(iframe_url, verify=False, timeout=5)
                        if resp.status_code == 200:
                            success = True
                            print(generate_timestamp(),f"Debug: Connection successful on attempt {retry_count+1}")
                        else:
                            error = f"Debug: Got status code {resp.status_code} on attempt {retry_count+1}"
                            retry_count += 1
                            time.sleep(1)  # Add delay between retries
                            
                            yield format_response_for_stream({
                                "debug_error": error
                            })
                            
                    except Exception as e:
                        print(generate_timestamp(),f"Debug_error: Connection failed on attempt {retry_count+1} with error: {str(e)}")
                        retry_count += 1
                        time.sleep(1)  # Add delay between retries
                
                if success:
                    yield format_response_for_stream({
                        'message': 'install|provision|config|final',
                        "task_id": job_name,
                        "ip": ip,
                        "iframe": iframe_url,
                        "ports": ports,
                        'end': True
                    })
                    
                else:
                    yield format_response_for_stream({
                        'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                        'end': True
                    })
            else:
                    yield format_response_for_stream({
                    'error': 'Failed to connect to the service after multiple attempts. Please check the service status.',
                    'end': True
                })
            
                    
            return
        
                
    except Exception as e:
        print(generate_timestamp(),f"Error in stream_start_workspace_status: {str(e)}")
        error_data = json.dumps({"error": str(e), "end": True})
        yield f"data: {error_data}\n\n"
        
@router.post("/start_deep_query/{project_id}/")
async def submit_deep_query_job_opt(
        project_id: int,
        selected_repos: dict = Body(...),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        mongo_db: MongoDBHandler = Depends(get_mongo_db),
        db: NodeDB = Depends(get_node_db),
        current_user = Depends(get_current_user)
    ): 
    return StreamingResponse(
        stream_deep_query(project_id,selected_repos,background_tasks,mongo_db,db,current_user),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )
    
@router.patch("/update_task/{task_id}")
async def update_task(
    task_id: str,
    update_data: dict = Body(...),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """
    Update specific fields of a task by its ID.
    """
    try:
        # Remove any attempt to update _id field as it's immutable
        if '_id' in update_data:
            del update_data['_id']

        result = mongo_db.db[tasks_collection_name].update_one(
            {'_id': task_id},
            {'$set': update_data}
        )

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Task not found")

        if result.modified_count == 0:
            return {
                "message": "No changes were made to the task",
                "task_id": task_id
            }

        return {
            "message": "Task updated successfully",
            "task_id": task_id,
            "modified_count": result.modified_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error updating task: {str(e)}"
        )

 
@router.post("/retry_code_generation/{task_id}")
async def retry_code_gen(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user = Depends(get_current_user) 
):
    try:
        tenant_id = get_tenant_id()
        stage = get_stage(settings)
        os.environ["BATCH_JOB_TRIGGER"] = "True"
        os.environ["FEATURE_FLAG_GIT_TOOL"] = "True"
        os.environ["FEATURE_FLAG_USE_DOCKER"] = "True"  
        os.environ["BATCH_JOB_STAGE"] = stage
        user_id = current_user.get("cognito:username")
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        model_config = await get_module_configuration_for_user(module_name='code_generation', user_id=user_id)
        print(generate_timestamp(),"Model", model_config)
        if model_config:
            llm_model = model_config.get('llm_model', LLMModel.gpt_4o_mini.value )
        else:
            llm_model = LLMModel.gpt_4o_mini.value
            
        llm_model = LLMModel.bedrock_claude_3_7_sonnet.value
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        if os.environ.get("LOCAL_DEBUG"):
            input_arguments = json.dumps({
                "task_id": task_id,
                "tenant_id": tenant_id,
                "project_id": task.get("project_id"),
                "retry": True
            })
            mongo_db.db[tasks_collection_name].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": "SUBMITTED",
                        "tenant_id": tenant_id,
                        "project_id": task.get("project_id"),
                        "stop_reason": "",
                        "llm_model":llm_model
                    }},
                    upsert=True
                )
            os.environ["input_arguments"] = input_arguments
            os.environ["task_id"] = task_id
            # use screen to run the job with log
            subprocess.run(["screen", "-L", "-Logfile", f"logs/{task_id}.log", "-dmS", task_id, "python", "app/batch_jobs/jobs.py"], env=os.environ)
        else:
            handler = Ec2Handler()
            # Accumulate all responses from get_project
            accumulated_responses = []
            ip_address = None
            
            async for response in handler.get_project(
                project_id=str(task.get("project_id")), 
                stage=get_stage(settings)
            ):
                accumulated_responses.append(response)
                if 'ip' in response:
                    ip_address = response['ip']
                if response.get('end', False):
                    break
            
            if ip_address:
                # Update mongo with the instance information
                iframe_url = f'https://workspace.develop.kavia.ai/?ip={ip_address}&port=8080&folder=/home/<USER>/workspace/'
                ports = [{f'{port}':f'https://workspace.develop.kavia.ai/?ip={ip_address}&port={port}'} for port in [3000,5000,8000]]
                params = f'?project_id={task.get("project_id")}&architecture_id={task.get("architecture_id")}&container_id={task.get("container_id")}&task_id={task_id}&stage={get_stage(settings=settings)}&llm_model={llm_model}&tenant_id={tenant_id}'
                mongo_db.db[tasks_collection_name].update_one(
                    {"_id": task_id},
                    {"$set": {
                        "status": "SUBMITTED",
                        "stop_reason": "",
                        "llm_model":llm_model,
                        "ip": ip_address,
                        "iframe": iframe_url,
                        "ports": ports,
                        "last_retry_time": generate_timestamp(),
                        "retry": True,
                        "tenant_id": tenant_id
                    }},
                    upsert=True
                )
                start_code_generation_url = f'https://8765_{ip_address.replace(".","_")}.workspace.develop.kavia.ai/retry'+ params
                print(generate_timestamp(),"Calling", start_code_generation_url)
                resp = requests.get(start_code_generation_url, verify=False)
                
                return {
                    "task_id": task_id,
                    "message": "Retry Initiated",
                    "status": "success",
                    "ip": ip_address,
                    "iframe": iframe_url,
                    "ports": ports,
                    "responses": accumulated_responses
                }
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to get instance IP address"
                )
                
        return {
            "task_id": task_id,
            "message": "Retry Initiated",
            "status": "submitted"
        }

    except TimeoutError:
        raise HTTPException(
            status_code=504, 
            detail="Timeout waiting for instance to be ready"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during retry: {str(e)}"
        )

@router.post("/retry/{task_id}")
async def retry_task(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    #TODO: Need to clean this endpoint.
    return {
            "message": "Task retried successfully",
            "status": "success"
        }
    
    task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
    retry_count = 0
    MAX_RETRIES = 3
    if task: 
        while retry_count < MAX_RETRIES:
            ip = task.get('ip')
            params = task.get('params')  # Get the stored params
            
            if ip and params:
                
                start_url = f'{CODEGEN_URL}/api/code_gen/start'+ params
                print(generate_timestamp(),f"Retrying start URL (attempt {retry_count + 1}): {start_url}")
                response = requests.get(start_url, verify=False, timeout=10)
                if response.status_code == 200:
                    # Mark the old messages that needs_response as false
                    mongo_db.db[tasks_collection_name].update_one(
                        {"_id": task_id},
                        {"$set": {"status": "SUBMITTED",
                                  "stop_reason": "",
                                  "retry": True,
                                  "last_retry_time": generate_timestamp(),
                                  
                                  }}
                    )
                    messages = task.get('messages', [])
                    for message in messages:
                        try:
                            if message.get("status", "") == "needs_response" or message.get("status", "") == "pending":
                                message["status"] = "completed"
                        except:
                            pass
                    
                    mongo_db.db[tasks_collection_name].update_one(
                        {"_id": task_id},
                        {"$set": {"messages": messages}}
                    )
                    return {
                        "message": "Task retried successfully",
                        "status": "success"
                    }
                else:
                    return {
                        "message": "Failed to retry task",
                        "status": "failed"
                    }
            
    return task

@router.get("/task_status/{task_id}")
async def task_status(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    try:
        projection = {
            '_id': 1,
            'job_id': 1,
            'status': 1,
            'iframe': 1,
            'ip': 1,
            'architecture_id': 1,
            'session_name': 1,
            'preview_url': 1,
            'preview_url_status': 1,
            'stop_reason': 1,
        }
        response = mongo_db.db[tasks_collection_name].find_one({'_id':task_id}, projection)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")

@router.patch("/user_input/{task_id}")
async def update_user_input(task_id: str, user_input_request: UserInputRequest, mongo_db:MongoDBHandler =Depends(get_mongo_db)):
    """Streams updates for a configuration task."""
    
    user_input_dict = user_input_request.model_dump_json()
    user_input_dict = json.loads(user_input_dict)
    user_message = Message(content=user_input_dict.get("user_input"), sender="User")
    task = mongo_db.db[tasks_collection_name].find_one_and_update(
        {
            "_id": task_id
        },
        {
            "$set": {
                "user_input": user_input_dict.get("user_input"),
                "waiting_for_user": False
            },
            "$push": {"messages": user_message.to_dict()}
                   
        }
    )
    return {
        "message": "User input updated successfully"
    }


@router.patch("/control/{task_id}")
async def control_task(
    task_id: str, 
    control: str, 
    project_id: int, 
    commit_hash: str="", 
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
    is_maintenance = task.get("agent_name") == "CodeMaintenance"
    is_document_creation = task_id.startswith("deep-query-job")
    
    if control.casefold() == "pause":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {"status": "PAUSED"}}
        )
        return {"message": "Task paused successfully"}
        
    elif control.casefold() == "resume":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {"status": "RUNNING"}}
        )
        return {"message": "Task resumed successfully"}
        
    elif control.casefold() == "stop":
        
        # Update query based on task type
        update_query = {
            "project_id": project_id,
            "status": {
                "$regex": "^(submitted|started|pending|running|paused|in_progress)$",
                "$options": "i"
            }
        }
        if is_maintenance:
            update_query["_id"] = task_id
            update_query["agent_name"] = "CodeMaintenance"
        elif is_document_creation:
            update_query["agent_name"] = "DocumentCreation"
        else:
            update_query["container_id"] = task.get("container_id")
            
        mongo_db.db[tasks_collection_name].update_many(
            update_query,
            {"$set": {"status": TaskStatus.STOPPED.value}}
        )
        
        try:
            _ip = task.get('ip')
            
            if _ip:
                # stop_url = f"https://8765_{_ip.replace('.','_')}.workspace.develop.kavia.ai/stop"
                base_url =  get_codegen_url( stage=task.get("stage"),pod_prefix=task.get("pod_prefix"))
                stop_url = f'{base_url}/stop'

                if not is_maintenance and not is_document_creation:
                    stop_url += f"?container_id={task.get('container_id')}"
                else:
                    stop_url += f"?project_id={project_id}&task_id={task_id}&agent_name={task.get('agent_name')}"
                print(generate_timestamp(),"Calling", stop_url)
                result = requests.get(stop_url, timeout=10, verify=False)
                response = result.json()
                print(generate_timestamp(),"Stop response:", response)
                return response
            else:
                return 
        except:
            return 

    elif control.casefold() == "rollback":
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {"$set": {
                "status": "ROLLBACK",
                "commit_hash": commit_hash
            }}
        )
        return {"message": "Rollback initiated successfully"}
 
@router.post("/merge/{task_id}")
async def merge(
    task_id: str,
    pr_request: CreatePRRequest,
    db: NodeDB = Depends(get_node_db),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """Create a pull request for merging code changes"""
    try:
        # Get task details
        task = mongo_db.db[tasks_collection_name].find_one({'_id': task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Get repository details from project
        project_id = task.get('project_id')
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties", {})
        
        try:
            project_repositories = json.loads(project_details.get("repositories", "{}"))
        except:
            project_repositories = {}
            
        container_id = task.get('container_id')
        repository_metadata = project_repositories.get(str(container_id))

        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")

        # Get work item details
        work_item_details = task.get('work_item_details')
        if isinstance(work_item_details, str):
            work_item_details = json.loads(work_item_details)
        elif not work_item_details:
            work_item_details = {}

        component_name = work_item_details.get('component_name', '')
        if not component_name:
            raise HTTPException(status_code=400, detail="Component name not found in work item details")
            
        source_branch = f"feature/{name_to_slug(component_name)}"

        # Initialize enhanced GitTools
        git_tool = EnhancedGitTools(
            callback_functions=None,
            base_path=get_codegeneration_path(),
            logger=None,
            access_token=settings.GITHUB_ACCESS_TOKEN,
            aws_credentials={
                'region': settings.AWS_REGION,
                'access_key_id': settings.AWS_ACCESS_KEY_ID,
                'secret_access_key': settings.AWS_SECRET_ACCESS_KEY
            }
        )

        # Create PR title and description
        default_title = f"Merge {source_branch} into {pr_request.destination_branch}"
        default_description = (
            f"Automated PR created for task {task_id}\n\n"
            f"Component: {component_name}\n"
            f"Source Branch: {source_branch}\n"
            f"Target Branch: {pr_request.destination_branch}\n"
            f"Created At: {generate_timestamp()}"
        )

        pr_result = git_tool.create_pull_request(
            source_branch=source_branch,
            target_branch=pr_request.destination_branch,
            title=pr_request.title or default_title,
            description=pr_request.description or default_description,
            repository_path=get_codegeneration_path(),
            repository_metadata=repository_metadata
        )
        
        if isinstance(pr_result, str) and "Error" in pr_result:
            raise Exception(pr_result)
        
        # Update task with PR details
        pr_details = {
            "pr_number": pr_result.get("number"),
            "pr_url": pr_result.get("url"),
            "title": pr_request.title or default_title,
            "description": pr_request.description or default_description,
            "source_branch": source_branch,
            "target_branch": pr_request.destination_branch,
            "status": "created",
            "created_at": generate_timestamp()
        }

        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {
                "$set": {
                    "pr_details": pr_details
                }
            }
        )
        
        return {
            "message": "Pull request created successfully",
            "pr_details": pr_details
        }

    except Exception as e:
        error_message = f"Failed to create PR: {str(e)}"
        print(generate_timestamp(),f"Error details: {error_message}")
        
        # Update task with error details
        mongo_db.db[tasks_collection_name].update_one(
            {"_id": task_id},
            {
                "$set": {
                    "pr_error": {
                        "error": error_message,
                        "timestamp": generate_timestamp()
                    }
                }
            }
        )
        raise HTTPException(status_code=500, detail=error_message)

     
@router.get("/past_code_generation_tasks/{project_id}")
async def get_past_code_generation_tasks(
    project_id: int, 
    container_id: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """
    Retrieve past code generation tasks for a given project_id.
    Optionally filter by architecture_id.
    """
    query = {
        "project_id": project_id
    }
    
    if container_id is not None:
        query["container_id"] = container_id
    
    tasks = list(mongo_db.db[tasks_collection_name].find(
        query,
        {
            "_id": 1,
            "job_id": 1,
            "status": 1,
            "project_id":1,
            "start_time":1,
            "container_id": 1,
            "session_id":1,
            "description":1,
            "messages":1,
            
        }
    ).sort("start_time", -1).skip(skip).limit(limit))

    total_count = mongo_db.db[tasks_collection_name].count_documents(query)

    return {
        "tasks": tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }
       


@router.get("/callback/{callback_type}/{task_id}")
async def get_callback_output(
    callback_type: str,
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    print(generate_timestamp(),"Callback:", callback_type)
    if callback_type == "function_calls":
        return StreamingResponse(
            function_calls_stream(task_id, mongo_db),
            media_type="text/event-stream",
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'
            }
        )
    elif callback_type == "browser":
        return StreamingResponse(browser_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "document":
        return StreamingResponse(get_documents(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "messages":
        return StreamingResponse(messages_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "terminal":
        return StreamingResponse(terminal_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "past_steps":
        return StreamingResponse(past_steps_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })

        
    elif callback_type == "status":
        return StreamingResponse(status_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
        
    elif callback_type == "task_plan":
        return StreamingResponse(task_plan_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    elif callback_type == "code_server":
        task = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        },
        {
            'status': 1,
            'iframe': 1,
        })
        
        return {
            "iframe": task.get('iframe', "")
        }
        
    elif callback_type == "llm_cost":
        return StreamingResponse(llm_cost_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    
    elif callback_type == "steps":
        return StreamingResponse(steps_stream(task_id, mongo_db), media_type="text/event-stream" , headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        })
    
async def get_documents(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    while continue_stream:
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        base_path = f"/tmp/kavia/workspace/{task_id}/docs"

        documents = {}
        total_count = 0
        
        for root, dirs, files in os.walk(base_path):
            
            # Get relative path from base_path
            rel_path = os.path.relpath(root, base_path)
            # Use '.' for base directory
            rel_path = base_path if rel_path == '.' else os.path.join(base_path, rel_path)

            # Filter .md and .mmd files
            md_files = [f for f in files if f.endswith(('.md', '.mmd'))]

            if md_files:
                documents[rel_path] = md_files
                total_count += len(md_files)

        print(generate_timestamp(),documents)
        yield f"data: {json.dumps({'documents': documents})}"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'documents': {}, 'base_path': base_path, 'total_count': total_count})}\n\n"            
            return
        await asyncio.sleep(3)

def get_docs_folder( task_id):
    if(task_id.startswith("deep-query")):
        return f"deep_query_docs/{task_id}"
    elif(task_id.startswith("cg")):
        return f"code_maintenance_docs/{task_id}"
    else:
        return f"code_generation_docs/{task_id}"

@router.get("/list-document-from-s3")
async def get_document_from_s3(task_id:str):
    try:
        tenant_id = get_tenant_id()
        docs_folder = get_docs_folder(task_id)
        s3_handler = S3Handler(tenant_id, folder_name= docs_folder)
        documents = s3_handler.list_all_filenames(docs_folder)
        if documents:
            return documents
        else:
            return []
    except Exception as e :
        raise HTTPException(status_code=500, detail=f"Failed to retrieve documents: {str(e)}")
@router.get('/retrive-document-content-from-s3')
async def get_document_content_from_s3(task_id :str,title:str):
    try:
        tenant_id = get_tenant_id()
        docs_folder = get_docs_folder(task_id)
        s3_handler = S3Handler(tenant_id, folder_name= docs_folder)
        content = s3_handler.get_file(title)
        if content:
            content = content.decode('utf-8')
            return content
        else:
            return []
    except Exception as e :
        raise HTTPException(status_code=500, detail=f"Failed to retrieve file content from s3: {str(e)}")
    


@router.get("/get_deep_query_documents/{task_id}")
async def get_deep_query_docs(task_id: str):
    base_path = f"/tmp/kavia/workspace/{task_id}/docs"

    documents = {}
    total_count = 0

    for root, dirs, files in os.walk(base_path):
        # Get relative path from base_path
        rel_path = os.path.relpath(root, base_path)
        # Use '.' for base directory
        rel_path = base_path if rel_path == '.' else os.path.join(base_path, rel_path)

        # Filter .md and .mmd files
        md_files = [f for f in files if f.endswith(('.md', '.mmd'))]

        if md_files:
            documents[rel_path] = md_files
            total_count += len(md_files)

    return {'documents': documents, "base_path": base_path, "total_count": total_count}


async def messages_stream(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    
    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        messages = tasks.get('messages', [])
        waiting_for_user = tasks.get('waiting_for_user', False)
        
        messages_dict = {
            "messages": messages,
            "waiting_for_user": waiting_for_user
        }
        
        if previous_output != messages_dict:
            previous_output = messages_dict
            yield f"data: {json.dumps({'messages': messages_dict['messages'], 'waiting_for_user': messages_dict['waiting_for_user']})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'messages': messages_dict['messages'], 'waiting_for_user': messages_dict['waiting_for_user']})}\n\n"
            return


async def browser_stream(task_id: str, mongo_db: MongoDBHandler):
    # With heartbeat
    continue_stream = True
    previous_output = []
    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({
            '_id': task_id
        },{
            'status': 1,
            'task_status': 1,
            'browser_output': 1,
        })
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        browser_output = tasks.get('browser_output', [])
        browser_output = browser_output[-1] if browser_output else ""
        if previous_output != browser_output:
            previous_output = browser_output
            yield f"data: {json.dumps({'browser_output': browser_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'browser_output': browser_output})}\n\n"
            return
  

async def function_calls_stream(task_id: str, mongo_db: MongoDBHandler):
    MAX_FUNCTION_CALLS = 25
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        # Updated MongoDB query to fetch only the last 25 function calls
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'function_calls': 1,
                
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        function_calls = tasks.get('function_calls', [])
        if len(function_calls) > MAX_FUNCTION_CALLS:
            function_calls = function_calls[-MAX_FUNCTION_CALLS:]

        if previous_output != function_calls:
            previous_output = function_calls
            yield f"data: {json.dumps({'function_calls': function_calls})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'function_calls': function_calls})}\n\n"
            return

        
async def terminal_stream(task_id: str, mongo_db: MongoDBHandler):
    MAX_TERMINAL_OUTPUT = 50
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'terminal_output': 1
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        terminal_output = tasks.get('terminal_output', [])
        if len(terminal_output) > MAX_TERMINAL_OUTPUT:
            terminal_output = terminal_output[-MAX_TERMINAL_OUTPUT:]
        
        if previous_output != terminal_output:
            previous_output = terminal_output
            yield f"data: {json.dumps({'terminal_output': terminal_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'terminal_output': terminal_output})}\n\n"
            return


async def past_steps_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = True
        tasks = mongo_db.db[tasks_collection_name].find_one({'_id': task_id},{
            "status": 1,
            "task_status": 1,
            "past_steps": 1
        })

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        
        past_steps_output = tasks.get('past_steps', [])
        if previous_output != past_steps_output:
            previous_output = past_steps_output
            yield f"data: {json.dumps({'past_steps': past_steps_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'past_steps': past_steps_output})}\n\n"
            return

async def steps_stream(task_id:str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one({'_id': task_id},{
            "status": 1,
            "total_tasks": 1,
            "past_steps": 1
        })

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return
        
        total_tasks = tasks.get('total_tasks', [])
        past_steps = tasks.get('past_steps', [])
        total_tasks = json.loads(total_tasks) if isinstance(total_tasks, str) else total_tasks

        steps = []
        for step in total_tasks:
            steps.append({
                "title": step.get("step", ""),
                "description": step.get("details", ""),
                "status": step.get("status", "to-do")
            })
        for step in past_steps:
            steps.append({
                "title": step.get("title", ""),
                "description": step.get("action", ""),
                "status": "completed"
            })
        
        if previous_output != steps:
            previous_output = steps
            yield f"data: {json.dumps({'steps': steps})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'steps': steps})}\n\n"
            return
        


async def status_stream(task_id:str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []
    MAX_LOOP = 100
    count = 0
    while continue_stream:
        count = count + 1
        if count>= MAX_LOOP:
            continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},{
                "status": 1,
                "task_status": 1,
                "_status": 1,
                "description": 1,
                "past_statuses": 1,
                "session_id":1,
                "llm_model":1,
                "stop_reason":1,
                "branch_name":1,
                "ip": 1,
                "params": 1  # Get stored params
            }
        )
        
        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        
        status_output = {
            "past_statuses": tasks.get('past_statuses', []),
            "status": tasks.get('status', ""),
            "task_status": tasks.get('task_status', ""),
            "description": tasks.get('description', ""),
            "_status": tasks.get('_status', ""),
            "session_id":tasks.get('session_id',""),
            "llm_model":tasks.get('llm_model', ""),
            "stop_reason":tasks.get('stop_reason', ""),
            "branch_name":tasks.get('branch_name', "")
        }
        
        if previous_output != status_output:
            previous_output = status_output
            yield f"data: {json.dumps({'status_output': status_output})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status','').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.lower(), TaskStatus.COMPLETE.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'status_output': status_output})}\n\n"
            return
        await asyncio.sleep(3)
        
async def task_plan_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = []

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'total_tasks': 1,
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        task_plan = tasks.get('total_tasks', [])
        if previous_output != task_plan:
            previous_output = task_plan
            yield f"data: {json.dumps({'task_plan': task_plan})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'task_plan': task_plan})}\n\n"
            return


async def llm_cost_stream(task_id: str, mongo_db: MongoDBHandler):
    continue_stream = True
    previous_output = {}

    while continue_stream:
        continue_stream = False
        tasks = mongo_db.db[tasks_collection_name].find_one(
            {'_id': task_id},
            {
                'status': 1,
                'agents_cost': 1,
                'total_cost': 1,
            }
        )

        if not tasks:
            yield f"data: {json.dumps({'error': 'Task not found!!'})}\n\n"
            return

        llm_cost = {
            'agents_cost': tasks.get('agents_cost', {}),
            'total_cost': tasks.get('total_cost', 0)
        }
        
        if previous_output != llm_cost:
            previous_output = llm_cost
            yield f"data: {json.dumps({'llm_cost': llm_cost})}\n\n"
        else:
            yield f"data: {json.dumps({'message': 'Waiting for updates...'})}\n\n"
        
        if tasks.get('status', '').casefold() in [TaskStatus.FAILED.casefold(), TaskStatus.CANCELLED.casefold(), TaskStatus.COMPLETE.casefold(), TaskStatus.PAUSED.casefold()]:
            continue_stream = False
            yield f"data: {json.dumps({'llm_cost': llm_cost})}\n\n"
            return


@router.get("/callback_state/{callback_type}/{task_id}")
async def get_callback_state(callback_type: str, task_id: str, db: NodeDB = Depends(get_node_db)):
    """Non-streaming version of callbacks that returns current state"""
    
    # First verify task exists
    mongo_db = get_mongo_db()
    task = mongo_db.db[tasks_collection_name].find_one({'_id': task_id})
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if callback_type == "function_calls":
        # Get last 25 function calls
        function_calls = task.get('function_calls', [])[-25:]
        return {"function_calls": function_calls}

    elif callback_type == "browser":
        browser_output = task.get('browser_output', [])
        browser_output = browser_output[-1] if browser_output else ""
        return {"browser_output": browser_output}

    elif callback_type == "messages":
        messages = task.get('messages', [])
        messages_to_send = []
        cached_user_details = {}
        
        # Create a dictionary to track messages by ID
        message_map = {}
        
        # Status priority map (lower number = higher priority)
        status_priority = {
            "completed": 1,
            "resolved": 2,
            "pending": 3,
            "streaming": 4,
            "needs_response": 5,
            "failed": 6
        }
        
        for message in messages:
            # Handle both regular messages and file updates
            message_id = message.get("id", "") or message.get("message_id", "")
            
            if not message_id:
                continue
                
            # For file updates, just add them directly to the messages_to_send list
            if message.get("msg_type") == "file_update":
                messages_to_send.append(message)
                continue
                
            # If we've seen this ID before, merge the messages
            if message_id in message_map:
                existing_msg = message_map[message_id]
                
                # Compare status priorities
                existing_status = existing_msg.get("status", "").lower()
                new_status = message.get("status", "").lower()
                
                if (status_priority.get(new_status, 999) < 
                    status_priority.get(existing_status, 999)):
                    existing_msg["status"] = message.get("status")
                
                # Compare requires_resolution flags
                existing_requires = existing_msg.get("requires_resolution", True)
                new_requires = message.get("requires_resolution", True)
                if not existing_requires or not new_requires:
                    existing_msg["requires_resolution"] = False
                
                # Update resolution_id if present
                if message.get("resolution_id"):
                    existing_msg["resolution_id"] = message.get("resolution_id")
                    
                # Merge metadata if present
                if message.get("metadata"):
                    if not existing_msg.get("metadata"):
                        existing_msg["metadata"] = {}
                    existing_msg["metadata"].update(message.get("metadata", {}))
                    
                continue
                
            # Process user details
            metadata = message.get("metadata", {})
            user_id = ""
            if metadata:
                user_id = metadata.get("user_id", "")
            
            if user_id:
                if user_id not in cached_user_details:
                    user_details = await db.get_user_by_id(user_id)
                    node_properties = user_details.get("properties", {})
                    cached_user_details[user_id] = {
                        "name": node_properties.get("Name", ""),
                        "email": node_properties.get("Email", ""),
                        "designation": node_properties.get("Designation", ""),
                        "department": node_properties.get("Department", "")
                    }
                message["user_details"] = cached_user_details[user_id]
            
            # Store message in map
            message_map[message_id] = message

        # Add regular messages to the messages_to_send list
        messages_to_send.extend(list(message_map.values()))
        
        # Sort all messages by timestamp if available
        messages_to_send.sort(
            key=lambda x: x.get("timestamp", "") or x.get("created_at", ""),
            reverse=False
        )
        
        waiting_for_user = task.get('waiting_for_user', False)
        return {
            "messages": messages_to_send,
            "waiting_for_user": waiting_for_user
        }

    elif callback_type == "terminal":
        # Get last 50 terminal outputs
        terminal_output = task.get('terminal_output', [])[-50:]
        return {"terminal_output": terminal_output}

    elif callback_type == "past_steps":
        past_steps = task.get('past_steps', [])
        return {"past_steps": past_steps}
    
    elif callback_type == "steps":
        steps = []
        total_tasks = task.get('total_tasks', [])
        past_steps = task.get('past_steps', [])
        
        # Process total_tasks if it's a string (JSON)
        if isinstance(total_tasks, str):
            try:
                total_tasks = json.loads(total_tasks)
            except:
                total_tasks = []
        
        # Create a combined array of steps with title, description and status
        for step in total_tasks:
            steps.append({
                "title": step.get("step", ""),
                "description": step.get("details", ""),
                "status": step.get("status", "to-do")
            })
            
        # Add completed steps from past_steps
        for step in past_steps:
            steps.append({
                "title": step.get("title", ""),
                "description": step.get("action", ""),
                "status": "completed"
            })
            
        return {"steps": steps}

    elif callback_type == "status":
        return {
            "status_output": {
                "past_statuses": task.get('past_statuses', []),
                "status": task.get('status', ""),
                "task_status": task.get('task_status', ""),
                "description": task.get('description', ""),
                "_status": task.get('_status', ""),
                "session_id": task.get('session_id', ""),
                "branch_name": task.get('branch_name',"")
            }
        }

    elif callback_type == "task_plan":
        task_plan = task.get('total_tasks', [])
        return {"task_plan": task_plan}

    elif callback_type == "llm_cost":
        return {
            "llm_cost": {
                "agents_cost": task.get('agents_cost', {}),
                "total_cost": task.get('total_cost', 0)
            }
        }
        
    elif callback_type == "code_server":
        return {
            "iframe": task.get('iframe', "")
        }
    
    elif callback_type == "document":
        return get_documents(task_id, mongo_db)

    else:
        raise HTTPException(status_code=400, detail=f"Unknown callback type: {callback_type}")
    
    
@router.get("/check_live_status/")
async def check_live_status( project_id: str ,ip_address : str,architecture_id: int, current_user=Depends(get_current_user), 
    db: NodeDB = Depends(get_node_db),) -> StreamingResponse:
    mongo_db : MongoDBHandler = get_mongo_db()
    
    return True

@router.post("/check-live-status-code-maintenance/")
async def check_live_status_code_maintenance(project_id: str, ip_address: str, selectedrepos: dict = Body(...),
    session_name:str = Body(...),
    description:str = Body(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user)) -> StreamingResponse:
    
    return True

    if is_live(ip_address):
            
            async def streamResponse():
                yield format_response_for_stream({"status": "live"})
            return StreamingResponse(
            streamResponse(),
            media_type="application/json",)
    else:
        return StreamingResponse(
        stream_code_maintanence(project_id,selectedrepos,session_name,description,background_tasks,mongo_db,db,current_user),
        media_type="application/json",)
    
@router.post("/download-repository/{task_id}")
async def download_repository(
    task_id: str,
    project_id: str = Query(..., description="Project ID"),
    repo_name: str = Query(..., description="Repository name"),
    branch: str = Query(None, description="Branch to download"),
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db)
):
    """
    Download repository by pulling from git source, compressing in memory, and streaming the response.
    """
    import git
    from io import BytesIO
    
    try:
        tenant_id = get_tenant_id()
        
        # Get task information
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Get project_details from task
        project_details = task.get('project_details', {})
        
        # Handle case where project_details is a JSON string
        if isinstance(project_details, str):
            try:
                project_details = json.loads(project_details)
                
            except json.JSONDecodeError as e:
                print(generate_timestamp(),f"Error parsing project_details JSON: {e}")
                raise HTTPException(status_code=400, detail="Invalid project_details format")
        
        print(generate_timestamp(),f"Debug: project_details type: {type(project_details)}")
        print(generate_timestamp(),f"Debug: current_repositories type: {type(project_details.get('current_repositories', {}))}")

        # Find the repository metadata
        repository_metadata = None
        container_id = task.get('container_id')

        print(generate_timestamp(),f"Debug: Looking for repo_name: {repo_name}, container_id: {container_id}")
        
        # Look for repository in project_details.repositories first
        repositories = project_details.get('current_repositories', {})

        print(repositories)
        print(type(repositories))

        # Handle case where repositories might be a string
        if isinstance(repositories, str):
            try:
                repositories = json.loads(repositories)
            except json.JSONDecodeError:
                repositories = {}

        # Handle case where repositories is a list (like in your case)
        if isinstance(repositories, list):
            # Convert list to dict for easier processing
            repositories_dict = {}
            for i, repo_data in enumerate(repositories):
                if isinstance(repo_data, dict):
                    # Use repositoryName as key if available, otherwise use index
                    key = repo_data.get('repositoryName', str(i))
                    repositories_dict[key] = repo_data
            repositories = repositories_dict

        if container_id and str(container_id) in repositories:
            repo_data = repositories[str(container_id)]
            # Handle case where repo_data might be a string
            if isinstance(repo_data, str):
                try:
                    repository_metadata = json.loads(repo_data)
                except json.JSONDecodeError:
                    repository_metadata = None
            else:
                repository_metadata = repo_data

        # If not found by container_id, search by repo_name in repositories
        if not repository_metadata and repositories and isinstance(repositories, dict):
            for key, repo_data in repositories.items():
                # Skip if repo_data is not a dict and can't be parsed
                if isinstance(repo_data, str):
                    try:
                        repo_data = json.loads(repo_data)
                    except json.JSONDecodeError:
                        continue

                if isinstance(repo_data, dict) and repo_data.get('repositoryName') == repo_name:
                    repository_metadata = repo_data
                    break

        # If not found in repositories, check current_repository
        if not repository_metadata:
            current_repo = project_details.get('current_repositories', {})

            print("Current repository : ")
            print(current_repo)
            print(type(current_repo))

            # Handle case where current_repository might be a string
            if isinstance(current_repo, str):
                try:
                    current_repo = json.loads(current_repo)
                except json.JSONDecodeError:
                    current_repo = {}

            # Handle case where current_repo is a list
            if isinstance(current_repo, list):
                # Find the repository by name in the list
                for repo_item in current_repo:
                    if isinstance(repo_item, dict) and (repo_item.get('repositoryName') == repo_name or not repo_name):
                        repository_metadata = repo_item
                        break
            elif current_repo and isinstance(current_repo, dict):
                if current_repo.get('repositoryName') == repo_name or not repo_name:
                    repository_metadata = current_repo

        print(generate_timestamp(),f"Debug: Final repository_metadata found: {repository_metadata is not None}")
        if repository_metadata:
            print(generate_timestamp(),f"Debug: Repository metadata keys: {list(repository_metadata.keys())}")

        if not repository_metadata:
            # Additional debugging for troubleshooting
            print(generate_timestamp(),f"Debug: Available repositories in current_repositories:")
            if isinstance(repositories, dict):
                for key, value in repositories.items():
                    if isinstance(value, dict):
                        print(generate_timestamp(),f"  - Key: {key}, Repository Name: {value.get('repositoryName', 'N/A')}")
            elif isinstance(repositories, list):
                for i, repo in enumerate(repositories):
                    if isinstance(repo, dict):
                        print(generate_timestamp(),f"  - Index: {i}, Repository Name: {repo.get('repositoryName', 'N/A')}")

            raise HTTPException(status_code=404, detail=f"Repository '{repo_name}' not found in project")
        
        # Extract repository details using the correct field names
        clone_url = repository_metadata.get('cloneUrlHttp') or repository_metadata.get('cloneUrlSsh')

        print(generate_timestamp(),f"Debug: Branch parameter provided: {branch}")
        print(generate_timestamp(),f"Debug: Task ID: {task_id}")
        print(generate_timestamp(),f"Debug: Task ID starts with 'cg': {task_id.startswith('cg')}")

        # If task_id looks like a branch name (starts with 'cg') and no explicit branch provided, use cga-{task_id} as branch
        if task_id.startswith('cg') and not branch:
            # The actual branch name is typically cga-{task_id}
            target_branch = f"cga-{task_id}"
            print(generate_timestamp(),f"Debug: Using cga-prefixed branch name: {target_branch}")
        else:
            # Use the branch parameter, fallback to repository default, then to 'kavia-main'
            target_branch = branch or repository_metadata.get('default_branch', 'kavia-main')
            print(generate_timestamp(),f"Debug: Using {'explicit branch' if branch else 'default branch'}: {target_branch}")

        if not clone_url:
            raise HTTPException(status_code=400, detail="Repository clone URL not found")
        
        # Get organization from repository metadata
        organization = repository_metadata.get('organization', '')
        
        # Fetch access token from scm_configurations collection
        access_token = None
        if organization:
            scm_config = mongo_db.db["scm_configurations"].find_one({
                "tenant_id": tenant_id,
                "credentials.organization": organization
            })
            if scm_config:
                access_token = scm_config.get('credentials', {}).get('access_token')
        
        # Fallback to repository metadata access_token if no specific token found
        if not access_token:
            access_token = repository_metadata.get('access_token')

        # Final fallback to settings if no token found
        if not access_token:
            access_token = settings.GITHUB_ACCESS_TOKEN

        if not access_token:
            raise HTTPException(status_code=400, detail="GitHub access token not found")

        print(generate_timestamp(),f"Debug: Using access token from: {'repository metadata' if repository_metadata.get('access_token') else 'scm_configurations' if organization else 'settings'}")
        print(generate_timestamp(),f"Debug: Clone URL: {clone_url}")
        print(generate_timestamp(),f"Debug: Target branch: {target_branch}")
        
        # Create temporary directory for cloning
        temp_clone_dir = tempfile.mkdtemp()
        
        try:
            # Clone repository to temporary directory
            print(generate_timestamp(),f"Cloning repository from {clone_url}")
            print(generate_timestamp(),f"Target branch: {target_branch}")

            # Prepare authenticated clone URL for HTTPS
            if clone_url.startswith('https://'):
                # Insert token into HTTPS URL
                auth_clone_url = clone_url.replace('https://', f'https://{access_token}@')
            else:
                # Use original URL for SSH (assumes SSH key is configured)
                auth_clone_url = clone_url

            repo_path = os.path.join(temp_clone_dir, repo_name)

            # Try to clone the specific branch first
            try:
                print(generate_timestamp(),f"Attempting to clone branch: {target_branch}")
                repo = git.Repo.clone_from(
                    auth_clone_url,
                    repo_path,
                    branch=target_branch,
                    env={
                        'GIT_TERMINAL_PROMPT': '0',
                        'GIT_ASKPASS': 'echo'
                    }
                )
                print(generate_timestamp(),f"Successfully cloned branch: {target_branch}")
            except git.exc.GitCommandError as branch_error:
                print(generate_timestamp(),f"Failed to clone branch {target_branch}: {str(branch_error)}")
                print(generate_timestamp(),f"Attempting to clone default branch and then checkout {target_branch}")

                # Clone without specifying branch (gets default branch)
                repo = git.Repo.clone_from(
                    auth_clone_url,
                    repo_path,
                    env={
                        'GIT_TERMINAL_PROMPT': '0',
                        'GIT_ASKPASS': 'echo'
                    }
                )

                # Try to checkout the target branch
                try:
                    # Fetch all branches
                    repo.remotes.origin.fetch()

                    # List available branches
                    available_branches = [ref.name for ref in repo.remotes.origin.refs]
                    print(generate_timestamp(),f"Available remote branches: {available_branches}")

                    # Try to checkout the target branch
                    if f"origin/{target_branch}" in available_branches:
                        repo.git.checkout('-b', target_branch, f'origin/{target_branch}')
                        print(generate_timestamp(),f"Successfully checked out branch: {target_branch}")
                    else:
                        # If the target branch wasn't found, try some variations for task_id based branches
                        branch_found = False
                        if task_id.startswith('cg'):
                            # Try different branch name patterns
                            possible_branches = [
                                f"cga-{task_id}",  # cga-cgaf8e74f6
                                task_id,           # cgaf8e74f6
                                f"cg-{task_id[2:]}" if len(task_id) > 2 else task_id,  # cg-af8e74f6
                            ]

                            for possible_branch in possible_branches:
                                if f"origin/{possible_branch}" in available_branches:
                                    repo.git.checkout('-b', possible_branch, f'origin/{possible_branch}')
                                    print(generate_timestamp(),f"Successfully checked out branch: {possible_branch}")
                                    branch_found = True
                                    break

                        if not branch_found:
                            print(generate_timestamp(),f"Branch {target_branch} not found. Using default branch.")
                            # Get current branch name
                            current_branch = repo.active_branch.name
                            print(generate_timestamp(),f"Using branch: {current_branch}")

                except Exception as checkout_error:
                    print(generate_timestamp(),f"Failed to checkout branch {target_branch}: {str(checkout_error)}")
                    print(generate_timestamp(),f"Continuing with default branch")

            # Verify we have files
            files_count = sum([len(files) for r, d, files in os.walk(repo_path)])
            print(generate_timestamp(),f"Repository contains {files_count} files")
            
            # Create zip file in memory
            zip_buffer = BytesIO()
            files_added = 0

            print(generate_timestamp(),f"Creating zip file from: {repo_path}")

            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # Walk through all files in the repository
                for root, dirs, files in os.walk(repo_path):
                    # Skip .git directory but keep everything else
                    if '.git' in dirs:
                        dirs.remove('.git')

                    # Skip __pycache__ and other common build directories
                    dirs[:] = [d for d in dirs if d not in ['__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv']]

                    print(generate_timestamp(),f"Processing directory: {root} with {len(files)} files")

                    for file in files:
                        file_path = os.path.join(root, file)
                        # Get relative path from repo root
                        arcname = os.path.relpath(file_path, repo_path)

                        # Skip hidden files except important ones
                        if file.startswith('.') and file not in ['.gitignore', '.env.example', '.dockerignore']:
                            continue

                        try:
                            # Check if file exists and is readable
                            if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
                                zip_file.write(file_path, arcname)
                                files_added += 1
                                if files_added <= 10:  # Log first 10 files for debugging
                                    print(generate_timestamp(),f"Added to zip: {arcname}")
                            else:
                                print(generate_timestamp(),f"Skipping unreadable file: {file_path}")
                        except Exception as e:
                            print(generate_timestamp(),f"Warning: Could not add file {file_path} to zip: {str(e)}")
                            continue

            print(generate_timestamp(),f"Zip file created with {files_added} files")

            # Check if we have any files in the zip
            if files_added == 0:
                shutil.rmtree(temp_clone_dir, ignore_errors=True)
                raise HTTPException(status_code=404, detail=f"No files found in repository branch '{target_branch}'. The branch may be empty or not exist.")

            zip_buffer.seek(0)
            zip_size = len(zip_buffer.getvalue())
            print(generate_timestamp(),f"Zip file size: {zip_size} bytes")

            # Create a streaming response
            def generate_zip():
                yield zip_buffer.getvalue()

            # Clean up temporary directory
            shutil.rmtree(temp_clone_dir, ignore_errors=True)

            return StreamingResponse(
                io.BytesIO(zip_buffer.getvalue()),
                media_type="application/zip",
                headers={"Content-Disposition": f"attachment; filename={repo_name}-{target_branch}.zip"}
            )
            
        except git.exc.GitCommandError as e:
            shutil.rmtree(temp_clone_dir, ignore_errors=True)
            raise HTTPException(status_code=500, detail=f"Git operation failed: {str(e)}")
        except Exception as e:
            shutil.rmtree(temp_clone_dir, ignore_errors=True)
            raise HTTPException(status_code=500, detail=f"Error processing repository: {str(e)}")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@router.get("/health")
async def health_check(url: str = Query(..., description="URL to check health status")):
    """Health check endpoint to verify if a URL returns a 200 status code."""
    try:
        response = requests.get(url, timeout=10,verify=False)
        return {
            "status": "healthy" if response.status_code == 200 else "unhealthy",
            "url": url,
            "status_code": response.status_code,
            "healthy":True if response.status_code == 200 else False
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "url": url,
            "error": str(e)
        }
    
@router.get("/past_code_tasks/{project_id}")
async def get_past_code_tasks(
   project_id: int,
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    agent_name: Optional[str] = Query(None, description="Filter by agent name"),
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """
    Retrieve past code maintenance tasks for a given project_id.
    Includes duration calculation between start_time and last message timestamp.
    """
    from datetime import datetime

    def calculate_duration(start_time_str, end_time_str):
        try:
            start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            
            # Calculate difference in seconds
            diff_seconds = (end_time - start_time).total_seconds()
            
            # Convert to various units
            minutes = int(diff_seconds // 60)
            hours = int(minutes // 60)
            days = int(hours // 24)
            weeks = int(days // 7)
            years = int(days // 365.25)
            
            # Calculate remaining values
            remaining_weeks = int((days % 365.25) // 7)
            remaining_days = int(days % 7)
            remaining_hours = int(hours % 24)
            remaining_minutes = int(minutes % 60)
            remaining_seconds = int(diff_seconds % 60)
            
            # Return appropriate format based on duration
            if years >= 1:
                return f"{years}y {remaining_weeks}w"
            elif weeks >= 1:
                return f"{weeks}w {remaining_days}d"
            elif days >= 1:
                return f"{days}d {remaining_hours}h"
            elif hours >= 1:
                return f"{hours}h {remaining_minutes}m"
            elif minutes >= 1:
                return f"{minutes}m {remaining_seconds}s"
            else:
                return "<1m"
                
        except Exception as e:
            print(generate_timestamp(),f"Error calculating duration: {str(e)}")
            return "0"

    tenant_id = get_tenant_id()
    task_repository: TaskRepository = await get_mongo_db_v1("task", table_name="code_gen_tasks")
    db = await get_db(f"{settings.MONGO_DB_NAME}_{tenant_id}")
    query = {
        "project_id": int(project_id)
    }
    if agent_name:
        if agent_name.lower() == "codegeneration":
            query["agent_name"] = {"$exists": False}
        elif agent_name.lower() == "codemaintenance":
            query["agent_name"] = "CodeMaintenance"
    else:
        query = {
        "$and": [
            {"project_id": int(project_id)},
            {"$or": [
                {"agent_name": "CodeMaintenance"},
                {"agent_name": {"$exists": False}}
            ]}
        ]
    }   
    projection = {
        "_id": 1,
        "status": 1,
        "llm_model": 1,
        "agent_name": 1,
        "start_time": 1,
        "session_id": 1,
        "description": 1,
        "session_name": 1,
        "messages": 1,
        "container_id": 1,
        "merge_status": 1,
        "merge_summary": 1,
        "stop_reason": 1,
    }
    sort = [("start_time", -1)]

    tasks = await task_repository.find_many(query, db, projection, skip=skip, limit=limit, sort=sort)

    # Convert ObjectId to string in tasks and calculate duration
    serialized_tasks = []
    for task in tasks:
        task['_id'] = str(task['_id'])
        
        # Get start time
        start_time = task.get('start_time')
        
        # Get last message timestamp
        messages = task.get('messages', [])
        end_time = start_time  # Default to start_time if no messages
        
        if messages:
            last_message = messages[-1]
            if isinstance(last_message, dict):
                end_time = last_message.get('timestamp', start_time)
        
        # Calculate and add duration
        if start_time and end_time:
            task['duration'] = calculate_duration(start_time, end_time)
        else:
            task['duration'] = "<1m"
        
        # Ensure container_id is included in the serialized task
        if 'container_id' not in task:
            task['container_id'] = None
            
        serialized_tasks.append(task)

    total_count = await task_repository.count_documents(query, db)

    return {
        "tasks": serialized_tasks,
        "total_count": total_count,
        "limit": limit,
        "skip": skip
    }

async def get_repository_details_from_mongodb(project_id: int, mongo_db: MongoDBHandler):
    """
    Get repository details from MongoDB for a given project_id.
    Adopts the same structure as nodedb format for compatibility.

    Args:
        project_id: The project ID to get repository details for
        mongo_db: MongoDBHandler instance

    Returns:
        dict: Repository details formatted similar to nodedb structure
    """
    try:
        # Query MongoDB project_repositories collection
        project_repo_doc = mongo_db.db["project_repositories"].find_one({
            "project_id": project_id
        })

        if not project_repo_doc:
            print(generate_timestamp(), f"No repository document found for project {project_id} in MongoDB")
            return {
                "project_id": project_id,
                "project_name": "Unknown",
                "repositories": [],
                "total_repositories": 0
            }

        repositories = project_repo_doc.get("repositories", [])
        if not repositories:
            print(generate_timestamp(), f"No repositories found in document for project {project_id}")
            return {
                "project_id": project_id,
                "project_name": "Unknown",
                "repositories": [],
                "total_repositories": 0
            }

        repository_list = []

        # Process each repository from MongoDB document
        for repo in repositories:
            try:
                # Extract repository information from MongoDB format
                repo_name = repo.get("repository_name", "")
                service = repo.get("service", "github")

                if not repo_name or "/" not in repo_name:
                    print(generate_timestamp(), f"Skipping repository with invalid name: {repo_name}")
                    continue

                # Get selected branch info
                selected_branch = repo.get("selected_branch", "main")
                branches = repo.get("branches", [])

                # Find the selected branch data
                branch_data = None
                for branch in branches:
                    if branch.get("name") == selected_branch:
                        branch_data = branch
                        break

                if not branch_data and branches:
                    branch_data = branches[0]  # Fallback to first branch
                    selected_branch = branch_data.get("name", "main")

                # Extract SCM information
                scm_id = repo.get("scm_id", "")
                encrypted_scm_id = None
                if scm_id:
                    # For SCM repositories, we might need to get encrypted_scm_id
                    # For now, use scm_id as encrypted_scm_id if available
                    encrypted_scm_id = scm_id

                repo_request_data = {
                    "repo_name": repo_name,
                    "branch_name": selected_branch,
                    "repo_type": repo.get("repo_type", "private"),
                    "repo_id": str(repo.get("repo_id", "")),
                    "associated": repo.get("associated", True),
                    "encrypted_scm_id": encrypted_scm_id,
                    "container_id": None,  # MongoDB format doesn't have container mapping
                    "container_name": "Unknown",
                    "repository_url": repo.get("git_url", ""),
                    "ssh_url": repo.get("clone_url_ssh", ""),
                    "access_token_path": "",  # Will be determined during merge process
                    "scm_id": scm_id,
                    "service": service
                }

                repository_list.append(repo_request_data)

            except Exception as e:
                print(generate_timestamp(), f"Error processing repository {repo.get('repository_name', 'unknown')}: {str(e)}")
                continue

        result = {
            "project_id": project_id,
            "project_name": f"Project {project_id}",  # MongoDB doesn't store project name in this collection
            "project_description": "",
            "repositories": repository_list,
            "total_repositories": len(repository_list),
            "source": "mongodb"  # Indicate this came from MongoDB
        }

        print(generate_timestamp(), f"Found {len(repository_list)} repositories for project {project_id} from MongoDB")
        return result

    except Exception as e:
        print(generate_timestamp(), f"Error getting repository details from MongoDB for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get repository details from MongoDB: {str(e)}")


@router.post("/merge_to_kavia_main/{task_id}")
async def merge_repos(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db),
    db: NodeDB = Depends(get_node_db)
):
    """
    Merge all repositories for a given task_id by calling the code_gen service.
    """
    try:
        # Get task details to determine project_id and stage
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        project_id = task.get("project_id")
        # Extract user_id from task for later use
        task_user_id = task.get("user_id", "U0000")

        source_branch = f"cga-{task_id}"
        target_branch = "kavia-main"


        if not project_id:
            raise HTTPException(status_code=400, detail="Project ID not found in task")

        # Try to get repository details from node database first, fallback to MongoDB
        from app.utils.batch_utils import get_repository_details_from_nodedb

        repo_details = None
        try:
            repo_details = await get_repository_details_from_nodedb(project_id, db)
        except Exception as e:
            print(generate_timestamp(), f"Error getting repository details from node DB: {str(e)}")
            print(generate_timestamp(), f"Falling back to MongoDB for project {project_id}")

        # If nodedb fails, try MongoDB
        if not repo_details or not repo_details.get("repositories"):
            try:
                repo_details = await get_repository_details_from_mongodb(project_id, mongo_db)
            except Exception as e:
                print(generate_timestamp(), f"Error getting repository details from MongoDB: {str(e)}")
                raise HTTPException(status_code=404, detail=f"No repositories found for this project: {str(e)}")

        if not repo_details or not repo_details.get("repositories"):
            raise HTTPException(status_code=404, detail="No repositories configured for this project")

        project_repositories = repo_details.get("repositories", [])

        merge_results = []
        merge_status = []  # New array to track merge status for database
        import_codebase = None  # Initialize import_codebase variable

        # Process each repository
        for repository in project_repositories:
            try:
                # Extract repository information from node DB format
                repo_name = repository.get("repo_name", "")

                if not repo_name or "/" not in repo_name:
                    print(generate_timestamp(), f"Skipping repository with invalid name: {repo_name}")
                    merge_status.append({
                        "repo_name": repo_name or "unknown",
                        "merge_time": generate_timestamp(),
                        "merge_status": "failed",
                        "error": "Invalid repository name",
                        "sha": None
                    })
                    continue

                owner, repo = repo_name.split("/", 1)
                
                # Get access token
                access_token = None

                # Handle different access token scenarios for both SCM and non-SCM repositories
                encrypted_scm_id = repository.get("encrypted_scm_id")
                scm_id = repository.get("scm_id")

                # Check if this is an SCM repository
                if encrypted_scm_id or scm_id:
                    try:
                        # Use scm_id if available, otherwise use encrypted_scm_id
                        lookup_scm_id = scm_id if scm_id else encrypted_scm_id
                        scm_config = mongo_db.db["scm_configurations"].find_one({"scm_id": lookup_scm_id})
                        if owner == settings.GITHUB_DEFAULT_ORGANIZATION:
                            access_token = settings.GITHUB_ACCESS_TOKEN
                        else:
                            access_token = scm_config.get("credentials", {}).get("access_token") if scm_config else None
                    except Exception as e:
                        print(generate_timestamp(), f"Error getting SCM config for {repo_name}: {e}")

                # If no access token from SCM, try other methods
                if not access_token:
                    access_token_path = repository.get("access_token_path")
                    if access_token_path == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
                        # For Kavia managed repositories, use the stored access token or default
                        access_token = repository.get("access_token")
                        if not access_token or owner == settings.GITHUB_DEFAULT_ORGANIZATION:
                            access_token = settings.GITHUB_ACCESS_TOKEN
                    else:
                        # For user repositories, try to get from user's GitHub credentials
                        # Since we don't have branches in the new format, use task_user_id
                        if task_user_id and task_user_id != "U0000":
                            user_github_entry = mongo_db.db["users_github"].find_one({"userId": task_user_id})
                            access_token = user_github_entry.get("access_token") if user_github_entry else None

                        # Fallback to default token
                        if not access_token or owner == settings.GITHUB_DEFAULT_ORGANIZATION:
                            access_token = settings.GITHUB_ACCESS_TOKEN
                
                
                if not access_token:
                    print(generate_timestamp(), f"No access token found for repository: {repo_name}")
                    merge_results.append({
                        "repository": repo_name,
                        "success": False,
                        "error": "No access token available"
                    })
                    
                    # Add to merge_status for database update
                    merge_status.append({
                        "repo_name": repo_name,
                        "merge_time": generate_timestamp(),
                        "merge_status": "failed",
                        "error": "No access token available",
                        "sha": None
                    })
                    continue
                
                branch_check = check_branch_exists(owner, repo, source_branch, access_token)
                if not branch_check.get("exists", False):
                    print(generate_timestamp(), f"Branch {source_branch} does not exist in {repo_name}")
                    continue
                
                # Perform the merge using our simple merge_branches function
                from app.utils.respository_utils import merge_branches
                commit_message = f"Merge {source_branch} into {target_branch} for task {task_id}"
                
                print(generate_timestamp(), f"Merging {source_branch} -> {target_branch} for {repo_name}")
                
                merge_result = merge_branches(
                    owner=owner,
                    repo=repo,
                    source_branch=source_branch,
                    target_branch=target_branch,
                    access_token=access_token,
                    commit_message=commit_message
                )
                
                merge_results.append({
                    "repository": repo_name,
                    "success": merge_result.get("success", False),
                    "merged": merge_result.get("merged", False),
                    "sha": merge_result.get("sha"),
                    "error": merge_result.get("error")
                })
                
                # Add to merge_status for database update
                merge_status.append({
                    "repo_name": repo_name,
                    "merge_time": generate_timestamp(),
                    "merge_status": "success" if merge_result.get("success", False) else "failed",
                    "error": merge_result.get("error") if not merge_result.get("success", False) else None,
                    "sha": merge_result.get("sha")
                })
                
                if merge_result.get("success"):
                    print(generate_timestamp(), f"Successfully merged {repo_name}: {merge_result.get('sha', 'N/A')}")
                else:
                    print(generate_timestamp(), f"Failed to merge {repo_name}: {merge_result.get('error', 'Unknown error')}")
                    
            except Exception as repo_error:
                error_msg = f"Error processing repository {repository.get('repo_name', 'unknown')}: {str(repo_error)}"
                print(generate_timestamp(), error_msg)
                merge_results.append({
                    "repository": repository.get("repo_name", "unknown"),
                    "success": False,
                    "error": error_msg
                })

                # Add to merge_status for database update
                merge_status.append({
                    "repo_name": repository.get("repo_name", "unknown"),
                    "merge_time": generate_timestamp(),
                    "merge_status": "failed",
                    "error": error_msg,
                    "sha": None
                })
        
        # Summary of results
        successful_merges = [r for r in merge_results if r.get("success")]
        failed_merges = [r for r in merge_results if not r.get("success")]

        # Call import_codebase_for_project if there were successful merges
        if successful_merges:
            try:
                print(generate_timestamp(), f"🚀 Calling import_codebase_for_project for project {project_id}")
                import_codebase = await import_codebase_for_project(project_id)
                print(generate_timestamp(), f"✅ Import codebase completed: {import_codebase.get('status', 'unknown')}")
            except Exception as import_error:
                print(generate_timestamp(), f"❌ Error calling import_codebase_for_project: {str(import_error)}")
                import_codebase = {
                    "status": "error",
                    "message": f"Failed to import codebase: {str(import_error)}",
                    "error": str(import_error)
                }
        
        # Update task with merge status information
        try:
            mongo_db.db[tasks_collection_name].update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "merge_status": merge_status,
                        "merge_completed_at": generate_timestamp(),
                        "merge_summary": {
                            "total_repositories": len(merge_status),
                            "successful_merges": len([m for m in merge_status if m["merge_status"] == "success"]),
                            "failed_merges": len([m for m in merge_status if m["merge_status"] == "failed"]),
                            "source_branch": source_branch,
                            "target_branch": target_branch
                        }
                    }
                }
            )
            print(generate_timestamp(), f"Updated task {task_id} with merge status information")
        except Exception as db_error:
            print(generate_timestamp(), f"Error updating task with merge status: {str(db_error)}")
        
    
        return {
            "task_id": task_id,
            "source_branch": source_branch,
            "target_branch": target_branch,
            "total_repositories": len(merge_results),
            "successful_merges": len(successful_merges),
            "failed_merges": len(failed_merges),
            "results": merge_results,
            "merge_status": merge_status,
            "import_codebase": import_codebase
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(generate_timestamp(),f"Error in merge operation for task_id {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing merge operation: {str(e)}"
        )

@router.get("/session_pod_status/{task_id}")
async def check_session_pod_status(
    task_id: str,
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
):
    """Check if the session pod is running by health checking the pod URL."""
    try:
        # Get task details
        task = mongo_db.db[tasks_collection_name].find_one({"_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Determine task type
        agent_name = task.get("agent_name")
        is_code_maintenance = agent_name == "CodeMaintenance"
        is_document_creation = agent_name == "DocumentCreation"
        
        # Get the pod URL to check
        stage = task.get("stage", get_stage(settings))
        pod_prefix = task.get("pod_prefix") or task.get("pod_id")
        
        if not pod_prefix:
            return {"is_running": False, "error": "Pod prefix not found in task"}
        
        # Get the appropriate URL based on task type
        if is_code_maintenance or is_document_creation:
            # For code maintenance and document creation, use the codegen URL
            check_url = get_codegen_url(stage, pod_prefix=pod_prefix)
        else:
            # For code generation, use the pod URL  
            stage_params = 'dev' if stage == 'develop' else stage
            check_url = get_pod_url(stage_params, pod_prefix)
        
        if not check_url:
            return {"is_running": False, "error": "Could not determine pod URL"}
        
        # Health check the URL
        try:
            response = requests.get(check_url, timeout=10, verify=False)
            is_running = response.status_code == 200
            
            return {
                "is_running": is_running,
                "url": check_url,
                "status_code": response.status_code,
                "task_type": "code_maintenance" if is_code_maintenance else "document_creation" if is_document_creation else "code_generation"
            }
            
        except requests.RequestException as e:
            return {
                "is_running": False,
                "url": check_url,
                "error": f"Connection error: {str(e)}",
                "task_type": "code_maintenance" if is_code_maintenance else "document_creation" if is_document_creation else "code_generation"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error checking pod status: {str(e)}"
        )


@router.post("/test_tic_tac_toe_creation")
async def test_tic_tac_toe_creation():
    """
    Test function to create a tic-tac-toe project using start_project_init endpoint.
    This function calls the start_project_init function directly to test project creation.
    """
    try:
        print(generate_timestamp(), "🚀 Starting Tic-Tac-Toe Project Creation Test")

        # Import required modules
        from app.connection.establish_db_connection import get_node_db
        from app.connection.tenant_middleware import get_user_id
        from app.models.project_model import StartProjectInit
        from app.routes.node_route import start_project_init
        from app.utils.codegen_import_utils import import_codebase_for_project

        # Create the request object with tic-tac-toe project data
        request_data = {
            "usercomment": "create a tic tac toe",
            "frontend_frameworks": "React JS",
            "backend_frameworks": "",
            "platform": ["web"],
            "type": "direct_code_gen"
        }

        print(generate_timestamp(), f"📦 Creating project with data: {request_data}")

        # Create StartProjectInit object
        start_project_request = StartProjectInit(**request_data)

        # Get dependencies
        node_db = get_node_db()
        current_user = {"cognito:username": get_user_id() or "test_user"}

        print(generate_timestamp(), "📞 Calling start_project_init function")

        # Call the start_project_init function directly
        project_result = await start_project_init(
            request=start_project_request,
            current_user=current_user,
            node_db=node_db
        )

        print(generate_timestamp(), f"✅ Project creation completed: {project_result}")

        # Extract project_id from the nested structure
        project_id = None
        if project_result.get('projectNodeInfo'):
            project_id = project_result['projectNodeInfo'].get('id')

        import_result = None

        if project_id:
            print(generate_timestamp(), f"🔄 Testing import_codebase_for_project with project_id: {project_id}")
            try:
                import_result = await import_codebase_for_project(project_id)
                print(generate_timestamp(), f"✅ Import codebase result: {import_result.get('status')} - {import_result.get('message')}")
            except Exception as import_error:
                print(generate_timestamp(), f"❌ Import codebase error: {str(import_error)}")
                import_result = {
                    "status": "error",
                    "message": f"Import failed: {str(import_error)}",
                    "error": str(import_error)
                }

        # Return comprehensive test results
        return {
            "test_status": "success",
            "message": "Tic-tac-toe project creation test completed",
            "timestamp": generate_timestamp(),
            "project_creation": {
                "status": "success",
                "request_data": request_data,
                "result": project_result
            },
            "import_codebase": import_result,
            "summary": {
                "project_id": project_id,
                "project_name": project_result.get('projectTitle') or (project_result.get('projectNodeInfo', {}).get('properties', {}).get('Title')),
                "project_status": project_result.get('projectNodeInfo', {}).get('properties', {}).get('status'),
                "import_status": import_result.get('status') if import_result else "not_attempted"
            }
        }

    except Exception as e:
        error_msg = f"Error in tic-tac-toe project creation test: {str(e)}"
        print(generate_timestamp(), f"❌ {error_msg}")
        import traceback
        traceback.print_exc()

        return {
            "test_status": "error",
            "message": error_msg,
            "timestamp": generate_timestamp(),
            "error": str(e),
            "project_creation": None,
            "import_codebase": None
        }

