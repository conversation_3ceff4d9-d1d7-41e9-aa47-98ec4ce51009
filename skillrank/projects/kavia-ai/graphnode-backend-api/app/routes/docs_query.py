from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks, Request, File, UploadFile
import yaml
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, List, Optional
import os
import uuid
import asyncio
import logging
from datetime import datetime
import json
from dataclasses import dataclass
from app.core.Settings import settings
from app.utils.prodefn.projdefn_helper import Proj<PERSON><PERSON>n_Helper,Reporter
from app.utils.auth_utils import get_current_user
from app.connection.establish_db_connection import get_mongo_db
from app.utils.prodefn.projdefn import ProjDefnDocSpecifier


# Models
class DocIngestionRequest(BaseModel):
    user_id: str
    tenant_id: str
    project_id: int
    document_ids: List[str]

class DocIngestionResponse(BaseModel):
    status: str
    session_id: str
    project_id: int
    document_paths: List[dict]

class FileUploadResponse(BaseModel):
    status: str
    session_id: str
    file_info: dict

class SessionStatusResponse(BaseModel):
    session_id: str
    is_ready: bool
    progress: float
    files_processed: int
    total_files: int

class DocumentPath(BaseModel):
    path: str
    name: str

class DocIngestionSession(BaseModel):
    document_paths: List[DocumentPath]

# Instance Management
def get_file_type(filename: str) -> str:
    """Determine file type based on extension"""
    extension = filename.split('.')[-1].lower()
    type_mapping = {
        'pdf': 'application/pdf',
        'txt': 'text/plain',
        'csv': 'text/csv',
        'json': 'application/json',
        'yaml': 'application/yaml',
        'yml': 'application/yaml',
        'xml': 'application/xml',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'xls': 'application/vnd.ms-excel'
    }
    return type_mapping.get(extension, 'application/octet-stream')

def extract_text_from_pdf(content: bytes) -> str:
    """Extract text from PDF content"""
    try:
        import fitz
        with fitz.open(stream=content, filetype="pdf") as doc:
            text = ""
            for page in doc:
                text += page.get_text()
            return text
    except Exception as e:
        logging.error(f"Error extracting text from PDF: {str(e)}")
        return None

def extract_text_from_excel(content: bytes) -> str:
    """Extract text from Excel content"""
    import pandas as pd
    import io
    try:
        df = pd.read_excel(io.BytesIO(content))
        return df.to_string()
    except Exception as e:
        logging.error(f"Error extracting text from Excel: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process Excel file: {str(e)}")

def extract_text_from_text_file(content: bytes) -> str:
    """Extract text from text file content"""
    try:
        return content.decode('utf-8')
    except UnicodeDecodeError:
        try:
            return content.decode('latin-1')
        except Exception as e:
            logging.error(f"Error decoding text file: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to decode text file")

# Pydantic models for requests and responses
class DocProcessingStatus(BaseModel):
    is_initialized: bool = False
    files_processed: int = 0
    total_files: int = 0
    initialization_started: bool = False

class DocInstance(BaseModel):
    session_id: str
    status: DocProcessingStatus
    base_path: str

class DocIngestionInstance:
    def __init__(self, session_id: str = None):
        self.reporter = Reporter()  # Similar to your existing Reporter class
        self.doc_helper = None
        self.is_initialized = False
        self.files_processed = 0
        self.total_files = 0
        self.initialization_started = False
        self.session_id = session_id
        self.base_path = os.path.join("./app/utils/prodefn", f'.projdefn_{session_id}' if session_id else 'default')
        os.makedirs(self.base_path, exist_ok=True)
    
    def to_model(self) -> DocInstance:
        return DocInstance(
            session_id=self.session_id,
            status=DocProcessingStatus(
                is_initialized=self.is_initialized,
                files_processed=self.files_processed,
                total_files=self.total_files,
                initialization_started=self.initialization_started
            ),
            base_path=self.base_path
        )

class DocManagerResponse(BaseModel):
    session_id: str
    instance_status: Optional[DocProcessingStatus] = None
    error: Optional[str] = None

class DocIngestionManager:
    def __init__(self):
        self._instances: Dict[str, DocIngestionInstance] = {}
        self._lock = asyncio.Lock()

    async def get_or_create_instance(self, session_id: str) -> DocManagerResponse:
        async with self._lock:
            if session_id not in self._instances:
                instance = DocIngestionInstance(session_id)
                self._instances[session_id] = instance
            instance = self._instances[session_id]
            return DocManagerResponse(
                session_id=session_id,
                instance_status=DocProcessingStatus(
                    is_initialized=instance.is_initialized,
                    files_processed=instance.files_processed,
                    total_files=instance.total_files,
                    initialization_started=instance.initialization_started
                )
            )

    def get_instance_by_session(self, session_id: str) -> Optional[DocIngestionInstance]:
        return self._instances.get(session_id)

    async def initialize_instance(self, document_paths: List[DocumentPath], session_id: str, instance: DocIngestionInstance) -> DocManagerResponse:
        try:
            if instance.initialization_started:
                return DocManagerResponse(
                    session_id=session_id,
                    error="Instance already initialized"
                )

            instance.initialization_started = True
            
            # Create session-specific path
            base_path = os.path.join("./app/utils/prodefn", f'.projdefn_{session_id}')
            os.makedirs(base_path, exist_ok=True)

            # Initialize document helper with session-specific path
            instance.doc_helper = ProjDefn_Helper(
                id=session_id,
                reporter=instance.reporter,
                base_path=base_path,
                documents=document_paths
            )

            # Initialize total_files count
            instance.total_files = len(document_paths)
            
            # Start document processing
            instance.doc_helper.knowledge.start()

            return DocManagerResponse(
                session_id=session_id,
                instance_status=DocProcessingStatus(
                    is_initialized=instance.is_initialized,
                    files_processed=instance.files_processed,
                    total_files=instance.total_files,
                    initialization_started=instance.initialization_started
                )
            )
        except Exception as e:
            return DocManagerResponse(
                session_id=session_id,
                error=str(e)
            )

    async def remove_instance(self, session_id: str):
        async with self._lock:
            if session_id in self._instances:
                del self._instances[session_id]

# Session Management
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, DocIngestionSession] = {}
        self._lock = asyncio.Lock()

    async def get_session(self, session_id: str) -> Optional[DocIngestionSession]:
        async with self._lock:
            return self.sessions.get(session_id)

# Initialize managers
doc_ingestion_manager = DocIngestionManager()
session_manager = SessionManager()

@dataclass
class ProcessedFile:
    file_path: str
    file_type: str
    extracted_content: str
    processing_complete: bool
    file_uuid: str

# Router setup
router = APIRouter(
    prefix="/doc_ingestion",
    tags=["doc_ingestion"],
    responses={404: {"description": "Not found"}}
)

async def wait_for_initialization(instance: DocIngestionInstance) -> bool:
    """Wait for initialization to complete with timeout"""
    max_wait_time = 600  # 10 minutes timeout
    wait_interval = 2  # seconds
    elapsed_time = 0

    while elapsed_time < max_wait_time:
        if instance.reporter.is_ready():
            instance.is_initialized = True
            if instance.doc_helper and instance.doc_helper.knowledge:
                instance.files_processed = len(instance.doc_helper.knowledge.processed_files)
            return True

        # Update progress while waiting
        if instance.doc_helper and instance.doc_helper.knowledge:
            instance.files_processed = len(instance.doc_helper.knowledge.processed_files)

        await asyncio.sleep(wait_interval)
        elapsed_time += wait_interval

    return False

async def process_uploaded_file(
    file: UploadFile,
    session_id: str,
    instance: DocIngestionInstance
) -> dict:
    """Process an uploaded file and prepare it for ingestion"""
    content = await file.read()
    file_uuid = str(uuid.uuid4())
    file_type = get_file_type(file.filename)
    file_extension = file.filename.split('.')[-1].lower()

    # Create safe filename and path
    safe_filename = f"{file_uuid}_{file.filename.replace(' ', '_')}"
    temp_file_path = os.path.abspath(os.path.join(instance.base_path, safe_filename))

    # Save file
    with open(temp_file_path, "wb") as temp_file:
        temp_file.write(content)

    # Extract text based on file type
    if file_extension == 'pdf':
        text = extract_text_from_pdf(content)
        if text is None:
            raise HTTPException(status_code=500, detail="Failed to extract text from PDF")
    elif file_extension in ['xlsx', 'xls']:
        text = extract_text_from_excel(content)
    elif file_type.startswith('text/') or file_extension in ['txt', 'csv', 'log', 'py', 'js']:
        text = extract_text_from_text_file(content)
    elif file_extension in ['json']:
        json_data = json.loads(content.decode('utf-8'))
        text = json.dumps(json_data, indent=2)
    elif file_extension in ['yml', 'yaml']:
        yaml_data = yaml.safe_load(content)
        text = yaml.dump(yaml_data, default_flow_style=False)
    elif file_extension in ['xml']:
        text = content.decode('utf-8')
    else:
        raise HTTPException(status_code=400, detail="Unsupported file type")

    # Add to ingestion queue
    doc_spec = ProjDefnDocSpecifier(temp_file_path, file_uuid)
    instance.doc_helper.knowledge.addToIngestQueue(doc_spec.name, doc_spec.pathname)

    return {
        'file_path': temp_file_path,
        'file_type': file_type,
        'extracted_content': text,
        'processing_complete': False,
        'file_uuid': file_uuid
    }
@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    session_id: str = None,
    project_id: int = None,
    background_tasks: BackgroundTasks = None,
    current_user: dict = Depends(get_current_user)
):
    """Upload and process a single file"""
    try:
        print("Starting file upload process")  # Debug log
        
        # Create new session if none provided
        if not session_id:
            session_id = str(uuid.uuid4())
            print(f"Created new session: {session_id}")

        # Get existing instance or create new one
        instance = doc_ingestion_manager.get_instance_by_session(session_id)
        if not instance:
            instance = DocIngestionInstance(session_id)
            doc_ingestion_manager._instances[session_id] = instance
            print(f"Created new instance for session: {session_id}")

        # Initialize if not already started
        if not instance.initialization_started:
            print(f"Starting initialization for session: {session_id}")
            document_paths = []
            instance.initialization_started = True
            
            # Create session-specific path
            base_path = os.path.join("./app/utils/prodefn", f'.projdefn_{session_id}')
            os.makedirs(base_path, exist_ok=True)

            # Initialize document helper
            instance.doc_helper = ProjDefn_Helper(
                reporter=instance.reporter,
                base_path=base_path,
                documents=document_paths
            )

            # Start document processing
            instance.doc_helper.knowledge.start()
            
            # Create session
            session = DocIngestionSession(document_paths=document_paths)
            session_manager.sessions[session_id] = session

            # Record in MongoDB if project_id provided
            if project_id:
                mongo_handler = get_mongo_db(
                    db_name=settings.MONGO_DB_NAME,
                    collection_name='document_ingestion'
                )
                await mongo_handler.insert(
                    {
                        "session_id": session_id,
                        "document_ids": [],
                        "start_time": datetime.now(),
                        "project_id": project_id,
                        "user_id": current_user.get("cognito:username"),
                        "status": "initializing"
                    },
                    db=mongo_handler.db
                )

        # Process the uploaded file
        processed_file = await process_uploaded_file(file, session_id, instance)

        # Update processing counts
        instance.total_files += 1

        response = {
            "status": "success",
            "session_id": session_id,
            "file_info": {
                "original_name": file.filename,
                "file_uuid": processed_file['file_uuid'],
                "file_type": processed_file['file_type'],
                "file_path": processed_file['file_path'],
                "content_preview": processed_file['extracted_content'][:1000] + "..." if len(processed_file['extracted_content']) > 1000 else processed_file['extracted_content']
            }
        }

        print(f"Successfully processed file for session: {session_id}")  # Debug log
        return JSONResponse(content=response, status_code=200)

    except HTTPException as he:
        print(f"HTTP Exception in upload_file: {str(he)}")  # Debug log
        raise he
    except Exception as e:
        print(f"Error in upload_file: {str(e)}")  # Debug log
        raise HTTPException(
            status_code=500,
            detail=f"Error processing file: {str(e)}"
        )
async def initialize_doc_ingestion(
    request: DocIngestionRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    try:
        user_id = current_user.get("cognito:username")
        session_id = str(uuid.uuid4())

        # Get MongoDB connection (assuming similar setup to your existing code)
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='document_ingestion'
        )

        # Get project documents
        project_data = await mongo_handler.get_one(
            filter={'project_id': int(request.get('project_id'))},
            db=mongo_handler.db
        )

        if not project_data:
            raise HTTPException(
                status_code=404,
                detail=f"Project with ID {request.get('project_id')} not found"
            )

        # Process document paths
        document_paths = []
        incomplete_docs = []

        # Iterate through documents to find matching document IDs
        for doc in project_data.get('documents', []):
            if doc.get('document_id') in request.get('document_ids', []):
                if doc.get('ingestion_status') != 2:  # Assuming 2 means complete
                    incomplete_docs.append({
                        'document_id': doc.get('document_id'),
                        'status': doc.get('ingestion_status')
                    })
                    continue

                doc_path = doc.get('path')
                if doc_path:
                    document_paths.append(DocumentPath(
                        path=doc_path,
                        name=f"doc_{doc['document_id']}"
                    ))

        if incomplete_docs:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "Document ingestion is not complete for some documents",
                    "incomplete_documents": incomplete_docs
                }
            )

        if not document_paths:
            raise HTTPException(
                status_code=404,
                detail="No valid document paths found for the specified document IDs"
            )

        # Create session
        session = DocIngestionSession(document_paths=document_paths)
        session_manager.sessions[session_id] = session

        # Initialize instance
        instance = await doc_ingestion_manager.get_or_create_instance(session_id)
        
        if not instance.initialization_started:
            await doc_ingestion_manager.initialize_instance(document_paths, session_id, instance)
            background_tasks.add_task(wait_for_initialization, instance)

        # Record session in MongoDB
        await mongo_handler.insert(
            {
                "session_id": session_id,
                "document_ids": request.document_ids,
                "start_time": datetime.now(),
                "project_id": request.get('project_id'),
                "user_id": user_id,
                "status": "initializing"
            },
            db=mongo_handler.db
        )

        return {
            "status": "success",
            "session_id": session_id,
            "project_id": request.project_id,
            "document_paths": [doc.dict() for doc in document_paths]
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error initializing document ingestion: {str(e)}"
        )

@router.get("/session-status/{session_id}", response_model=SessionStatusResponse)
async def get_session_status(session_id: str):
    session = await session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    instance = doc_ingestion_manager.get_instance_by_session(session_id)
    if not instance:
        raise HTTPException(status_code=404, detail="Instance not found")

    is_ready = instance.is_initialized and instance.reporter.is_ready()
    progress = (instance.files_processed / instance.total_files * 100) if instance.total_files > 0 else 0

    return SessionStatusResponse(
        session_id=session_id,
        is_ready=is_ready,
        progress=progress,
        files_processed=instance.files_processed,
        total_files=instance.total_files,
    )

@router.delete("/session/{session_id}")
async def delete_session(session_id: str):
    try:
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Clean up instance
        instance = doc_ingestion_manager.get_instance_by_session(session_id)
        if instance:
            if instance.doc_helper:
                # Clean up any resources
                base_path = os.path.join("./app/utils/prodefn", f'.projdefn_{session_id}')
                if os.path.exists(base_path):
                    # Implement safe directory removal here
                    pass

            await doc_ingestion_manager.remove_instance(session_id)

        # Remove session
        del session_manager.sessions[session_id]

        return {
            "status": "success",
            "message": f"Session {session_id} has been deleted"
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting session: {str(e)}"
        )

@router.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "active_sessions": len(session_manager.sessions),
        "instances": len(doc_ingestion_manager._instances)
    }
    
# Add this new model at the top with other models
class DocumentationResponse(BaseModel):
    task_id: str
    docs: List[dict] = []
    total_docs: int = 0
    status: str = "success"

# Add this new route
@router.get("/task-documentation/{task_id}", response_model=DocumentationResponse)
async def get_task_documentation(task_id: str):
    """
    Retrieve documentation for a specific task ID from kavia_documentation collection
    
    Args:
        task_id: The ID of the task to retrieve documentation for
        
    Returns:
        DocumentationResponse containing the documentation if found
    """
    try:
        # Get MongoDB connection for kavia_documentation collection
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='kavia_documentation'
        )
        
        # Query the documentation
        doc = await mongo_handler.get_one(
            filter={"_id": task_id},
            db=mongo_handler.db
        )
        
        # If documentation exists, return it
        if doc and "documents" in doc:
            return DocumentationResponse(
                task_id=task_id,
                docs=doc["documents"],
                total_docs=len(doc["documents"]),
                status="success"
            )
            
        # If no documentation found, return empty list
        return DocumentationResponse(
            task_id=task_id,
            docs=[],
            total_docs=0,
            status="no_documentation_found"
        )

    except Exception as e:
        print(f"Error retrieving documentation for task {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving documentation: {str(e)}"
        )