from fastapi import APIRout<PERSON>, BackgroundTasks, HTTPException, Request
from pydantic import BaseModel, EmailStr, validator
from app.models.organization_models import Plan
from app.models.organization_models import Organization 
from app.models.organization_models import Configurations, Settings
from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.models.organization_models import User
from app.models.organization_models import initialize_default_plans
from app.routes.authentication_route import add_user_node_db
from app.utils.aws.cognito_user_manager import CognitoUserManager
from app.utils.aws.cognito_userpool import CognitoUserPoolCreator
import re
from pymongo.errors import PyMongoError
import random
import string
from fastapi import HTTPException
from app.core.Settings import settings
from typing import Optional, List, Set
from app.connection.tenant_middleware import get_tenant_id
from fastapi import Query
from fastapi import Request
import urllib.parse
from app.classes.SESHandler import ses_handler, EmailTemplates
from datetime import datetime
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from datetime import datetime, timedelta
import urllib.parse
import zipfile
import os
from fastapi.responses import StreamingResponse
import io
from app.utils.k8.delete_project import delete_kubernetes_deployment
from app.models.auth_model import  ReferralCodeResponse, ReferralStats, ValidateReferralResponse
from app.routes.authentication_route import ReferralService
from app.core.kubernetes_monitor import KubernetesAvailablePodsManager, get_environment_and_namespace, kubernetes_manager
from app.services.session_tracker import get_name_by_user_id,get_organization_name_by_tenant_id


        
ROOT_TENANT_ID = settings.KAVIA_ROOT_TENANT_ID
DB_NAME = KAVIA_ROOT_DB_NAME
#develop_kaviaroot

router = APIRouter(
    prefix="/manage/super",
    tags=["Organization Admin Management"],
    responses={404: {"description": "Not found"}}
)


def cleanup_pod_sync(pod_name: str, env_name: str, namespace: str):
    """Synchronous wrapper for the cleanup function"""
    try:
        delete_kubernetes_deployment(pod_name,env_name)
    except Exception as e:
        print(e)

def safe_parse_datetime(date_string):
    """Safely parse datetime string to datetime object"""
    if not date_string:
        return None
    
    try:
        # Try parsing ISO format first
        if 'T' in date_string:
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        else:
            # Try parsing as date only
            return datetime.strptime(date_string, '%Y-%m-%d')
    except Exception as e:
        print(f"Error parsing datetime '{date_string}': {e}")
        return None
    
    

def get_allowed_tenants() -> Set[str]:
    """
    Get set of allowed tenants from MongoDB where enable_log_download_pod_crud is True.
    Using Set for O(1) lookup performance.
    
    Returns:
        Set[str]: Set of tenant IDs that have enable_log_download_pod_crud set to True
    """
    try:
        
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME).db
        collection = mongo_db["organizations"]
        # Query for documents where enable_log_download_pod_crud is True
        # Using projection to only fetch _id field for better performance
        query = {"settings.enable_log_download_pod_crud": True}
        projection = {"_id": 1}
        # Execute query and extract tenant IDs
        cursor = collection.find(query, projection)
        tenant_ids = {doc["_id"] for doc in cursor}
        
        print(f"Found {len(tenant_ids)} allowed tenants")
        return tenant_ids
        
    except PyMongoError as e:
        print(f"MongoDB error while fetching allowed tenants: {e}")
        return set()  # Return empty set on error
    except Exception as e:
        print(f"Unexpected error while fetching allowed tenants: {e}")
        return set()


def check_tenant_authorization() -> None:
    """
    Check if current tenant is authorized to access the resource.
    Raises HTTPException if not authorized.
    """
    current_tenant = get_tenant_id()
    is_root_tenant = current_tenant == ROOT_TENANT_ID
    allowed_tenants = get_allowed_tenants()
    
    if not allowed_tenants and not is_root_tenant:
        raise HTTPException(
            status_code=500,
            detail="No allowed tenants configured"
        )
    
    if current_tenant not in allowed_tenants and not is_root_tenant:
        raise HTTPException(
            status_code=403,
            detail="You are not authorized to access this resource"
        )
    

def random_password():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=10))

class OrganizationCreate(BaseModel):
    tenant_id: str
    name: str
    business_email: str
    industrial_type: str
    company_size: str
    domain: str
    image: str
    plan_id: str
    admin_name: str
    admin_email: str
    admin_contact_number: str
    admin_department: str
    configurations: Configurations
    settings: Settings

class AddAdminUserToOrganization(BaseModel):
    organization_id: str
    name: str
    email: str
    contact_number: str
    department: str

class BulkUserData(BaseModel):
    email: EmailStr
    name: str = None
    department: str = ""
    designation: str = "User"

    @validator('name', always=True)
    def set_name_from_email(cls, v, values):
        if not v and 'email' in values:
            return values['email']
        return v

class BulkAddUsersToOrganization(BaseModel):
    organization_id: str
    users: List[BulkUserData]
    plan_id: str 
class PodBulkDeleteRequest(BaseModel):
    pod_ids: List[str]


    
@router.get("/plans")
async def get_plans():
    try:
        plans = await Plan.get_all()
        if not plans:  # Check if empty
            await initialize_default_plans()
            plans = await Plan.get_all()
        return plans
    except Exception as e:
        if "Collection not found" in str(e):
            await initialize_default_plans() 
            return await Plan.get_all()
        raise e

@router.get("/plan/{plan_id}")
async def get_plan(plan_id: str,):
    return await Plan.get_by_id(plan_id)

@router.post("/create_plan")
async def create_plan(plan: Plan,):
    return await Plan.create(plan)

# Plan Upgrade Models
class PlanUpgradeRequest(BaseModel):
    user_id: str
    plan: str  # premium_starter, premium_advanced, premium_pro
    tenant_id: str = "b2c"  # Default to b2c
    credits: Optional[int] = None  # Optional custom credits

class PlanUpgradeResponse(BaseModel):
    success: bool
    user_id: str
    plan_name: str
    price_id: str
    credits: int
    tenant_id: str
    updates: dict
    plan_history_preserved: bool
    errors: List[str] = []

@router.post("/upgrade-user-plan", response_model=PlanUpgradeResponse)
async def upgrade_user_plan(request: PlanUpgradeRequest):
    """
    QA Plan Upgrade Endpoint (Simulates Stripe Payment Flow)

    This endpoint simulates the complete Stripe payment success flow for QA testing
    and super admin panel usage. It performs the exact same operations as the
    payment_route.py success endpoint without requiring actual Stripe payment processing.

    Args:
        request: PlanUpgradeRequest containing user_id, plan, tenant_id, and optional credits

    Returns:
        PlanUpgradeResponse with upgrade results and status
    """
    try:
        # Plan mapping with credits
        plan_mapping = {
            "premium_starter": {
                "price_id": "price_1RWBTrCI2zbViAE2WZFApvc8",
                "plan_name": "Premium Starter",
                "default_credits": 220000
            },
            "premium_advanced": {
                "price_id": "price_1RIosGCI2zbViAE25Ny8rOkc",
                "plan_name": "Premium Advanced",
                "default_credits": 550000
            },
            "premium_pro": {
                "price_id": "price_1RIozwCI2zbViAE2beuA7CDk",
                "plan_name": "Premium Pro",
                "default_credits": 1100000
            }
        }

        # Validate plan
        if request.plan not in plan_mapping:
            raise ValueError(f"Invalid plan: {request.plan}. Available: {list(plan_mapping.keys())}")

        plan_info = plan_mapping[request.plan]
        credits = request.credits if request.credits is not None else plan_info["default_credits"]

        # Get MongoDB connection
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)

        result = {
            "user_id": request.user_id,
            "plan_name": plan_info["plan_name"],
            "price_id": plan_info["price_id"],
            "credits": credits,
            "tenant_id": request.tenant_id,
            "success": False,
            "updates": {},
            "errors": [],
            "plan_history_preserved": False
        }

        # Step 1: Create/Update Active Subscription (same as Stripe flow)
        subscription_result = _create_active_subscription(mongo_db, request.user_id, request.tenant_id, plan_info, credits)
        result["updates"]["active_subscriptions"] = subscription_result

        # Step 2: Update Organization Credits (same as Stripe flow)
        org_result = _update_organization_credits(mongo_db, request.tenant_id, credits)
        result["updates"]["organizations"] = org_result

        # Step 3: Update LLM Costs with Plan History (same as Stripe flow)
        llm_result = _update_llm_costs_stripe_flow(mongo_db, request.user_id, request.tenant_id, plan_info)
        result["updates"]["llm_costs"] = llm_result
        result["plan_history_preserved"] = llm_result.get("plan_history_preserved", False)
        try:
            tenant_service = TenantService()
            tenant_cred = await tenant_service.get_or_create_tenant_cred(request.tenant_id)
            cognito_user_manager = CognitoUserManager(
                user_pool_id=tenant_cred['user_pool_id'],
                client_id=tenant_cred['client_id']
            )
            
            # Update the free_user custom attribute to false
            cognito_user_manager.update_user_attributes(
               request.user_id,
                {
                "custom:free_user": "false",
            }
            )
            result["cognito_updated"] = True
            result["updates"]["cognito"] = {"free_user": "false"}
            
        except Exception as cognito_error:
            error_msg = f"Failed to update Cognito free_user attribute: {str(cognito_error)}"
            result["errors"].append(error_msg)
            print(f"Cognito update error: {error_msg}")

        result["success"] = True

        # Convert to response model
        return PlanUpgradeResponse(
            success=result["success"],
            user_id=result["user_id"],
            plan_name=result["plan_name"],
            price_id=result["price_id"],
            credits=result["credits"],
            tenant_id=result["tenant_id"],
            updates=result["updates"],
            plan_history_preserved=result["plan_history_preserved"],
            errors=result["errors"]
        )

    except Exception as e:
        # Return error response
        return PlanUpgradeResponse(
            success=False,
            user_id=request.user_id,
            plan_name=request.plan,
            price_id="",
            credits=request.credits or 0,
            tenant_id=request.tenant_id,
            updates={},
            plan_history_preserved=False,
            errors=[f"Plan upgrade failed: {str(e)}"]
        )

def _create_active_subscription(mongo_db, user_id: str, tenant_id: str, plan_info: dict, credits: int):
    """Create/Update active subscription (same as payment_route.py)"""
    current_time = datetime.utcnow().isoformat()
    expires_at = (datetime.utcnow() + timedelta(days=30)).isoformat()
    subscription_id = f"qa_upgrade_{user_id}_{int(datetime.utcnow().timestamp())}"

    active_subscription_data = {
        "subscription_id": subscription_id,
        "tenant_id": tenant_id,
        "user_id": user_id,
        "status": 2,  # PaymentStatus.COMPLETED
        "created_at": current_time,
        "updated_at": current_time,
        "payment_session_id": subscription_id,
        "price_id": plan_info["price_id"],
        "credits": credits,
        "expires_at": expires_at
    }

    result = mongo_db.db["active_subscriptions"].update_one(
        {"subscription_id": subscription_id},
        {"$set": active_subscription_data},
        upsert=True
    )

    return {"matched": result.matched_count, "modified": result.modified_count, "upserted": result.upserted_id is not None}

def _update_organization_credits(mongo_db, tenant_id: str, credits: int):
    """Update organization credits (same as payment_route.py)"""
    result = mongo_db.db["organizations"].update_one(
        {"_id": tenant_id},
        {"$set": {"credits": credits}}
    )

    return {"matched": result.matched_count, "modified": result.modified_count}

def _update_llm_costs_stripe_flow(mongo_db, user_id: str, tenant_id: str, plan_info: dict):
    """Update LLM costs with plan history preservation (same as payment_route.py)"""
    # Find the organization document
    llm_cost_doc = mongo_db.db["llm_costs"].find_one({"organization_id": tenant_id})

    if not llm_cost_doc:
        raise Exception(f"LLM cost document not found for organization {tenant_id}")

    # Find the user in the users array
    user_found = False
    plan_history_preserved = False

    for user in llm_cost_doc.get("users", []):
        if user.get("user_id") == user_id:
            user_found = True

            # Preserve current plan in history (same as Stripe flow)
            current_plan = user.get("current_plan")
            if current_plan and current_plan != plan_info["price_id"]:
                old_plan = {
                    "plan_id": current_plan,
                    "cost": user.get("cost", "0.000000"),
                    "projects": user.get("projects", []),  # PRESERVE PROJECTS IN HISTORY
                    "upgraded_at": datetime.utcnow().isoformat()
                }

                if "plans_history" not in user:
                    user["plans_history"] = []
                user["plans_history"].append(old_plan)
                plan_history_preserved = True

            # Update user's current plan and reset cost + projects
            user["current_plan"] = plan_info["price_id"]
            user["cost"] = "$0.000"
            user["user_cost"] = "$0.000"
            user["projects"] = []  # RESET PROJECTS FOR NEW PLAN
            break

    if not user_found:
        raise Exception(f"User {user_id} not found in organization {tenant_id}")

    # Update the document (same as Stripe flow)
    result = mongo_db.db["llm_costs"].update_one(
        {"organization_id": tenant_id},
        {"$set": llm_cost_doc}
    )

    return {
        "matched": result.matched_count,
        "modified": result.modified_count,
        "plan_history_preserved": plan_history_preserved
    }

# User Plan Details Models
class UserPlanDetails(BaseModel):
    user_id: str
    email: Optional[str] = None
    name: Optional[str] = None
    current_plan: Optional[str] = None
    plan_name: Optional[str] = None
    allocated_credits: int = 0
    remaining_credits: int = 0
    usage_cost: str = "$0.000"
    status: str = "active"
    current_projects_count: int = 0
    plans_history_count: int = 0

class UserPlanListResponse(BaseModel):
    organization_id: str
    organization_name: str
    total_users: int
    users: List[UserPlanDetails]

@router.get("/users-plan-details/{tenant_id}", response_model=UserPlanListResponse)
async def get_users_plan_details(tenant_id: str = "b2c"):
    """
    Get list of users with their current plan details for super admin panel

    This endpoint provides the data needed for the user management table in the UI,
    showing current plan, credits, usage, and status for each user.

    Args:
        tenant_id: Organization/tenant ID (default: b2c)

    Returns:
        UserPlanListResponse with list of users and their plan details
    """
    try:
        # Get MongoDB connection
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)

        # Plan mapping for display names
        plan_mapping = {
            "price_1RWBTrCI2zbViAE2WZFApvc8": "Premium Starter",
            "price_1RIosGCI2zbViAE25Ny8rOkc": "Premium Advanced",
            "price_1RIozwCI2zbViAE2beuA7CDk": "Premium Pro"
        }

        # Get LLM costs document for the organization
        llm_cost_doc = mongo_db.db["llm_costs"].find_one({"organization_id": tenant_id})

        if not llm_cost_doc:
            return UserPlanListResponse(
                organization_id=tenant_id,
                organization_name="Unknown",
                total_users=0,
                users=[]
            )

        users_list = []

        # Process each user in the organization
        for user in llm_cost_doc.get("users", []):
            user_id = user.get("user_id", "")
            current_plan = user.get("current_plan", "")
            plan_name = plan_mapping.get(current_plan, "Unknown Plan")

            # Get user details from users collection (if available)
            user_doc = mongo_db.db["users"].find_one({"_id": user_id})
            email = user_doc.get("email", "") if user_doc else ""
            name = user_doc.get("name", "") if user_doc else ""

            # Get active subscription for credits info
            subscription = mongo_db.db["active_subscriptions"].find_one(
                {"user_id": user_id},
                sort=[("created_at", -1)]
            )

            allocated_credits = subscription.get("credits", 0) if subscription else 0

            # Calculate remaining credits (this is a simplified calculation)
            # In a real scenario, you'd calculate based on actual usage
            usage_cost = user.get("cost", "$0.000")
            remaining_credits = allocated_credits  # Simplified for now

            user_details = UserPlanDetails(
                user_id=user_id,
                email=email,
                name=name,
                current_plan=current_plan,
                plan_name=plan_name,
                allocated_credits=allocated_credits,
                remaining_credits=remaining_credits,
                usage_cost=usage_cost,
                status="active",  # You can determine this based on your business logic
                current_projects_count=len(user.get("projects", [])),
                plans_history_count=len(user.get("plans_history", []))
            )

            users_list.append(user_details)

        return UserPlanListResponse(
            organization_id=tenant_id,
            organization_name=llm_cost_doc.get("organization_name", "Unknown"),
            total_users=len(users_list),
            users=users_list
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch user plan details: {str(e)}")

@router.get("/organizations")
async def get_organizations(latest: Optional[bool] = Query(default=True), type: Optional[str] = Query(default=None)):
    print("Reached here")
    if type == settings.KAVIA_B2C_CLIENT_ID:
        # For B2C organizations, only return organizations that start with "default_"
        return await Organization.get_with_admin_cost(latest=latest, filter_prefix="default_")
    elif type == "all":
        # For type "all", return all organizations
        return await Organization.get_with_admin_cost(latest=latest)
    else:
        # For default organizations, return organizations that don't start with "default_"
        return await Organization.get_with_admin_cost(latest=latest, exclude_prefix="default_")
    
@router.get('/b2c_user_cost_plan')
async def get_b2c_users_plan_cost():
    
    try:
        organization_id = settings.KAVIA_B2C_CLIENT_ID
        print("organization_id",organization_id)

        if not organization_id:
            raise HTTPException(status_code=400, detail="Organization ID not found.")

        # Call the function to get users for the organization
        users = await Organization.get_users_by_organization_id(organization_id)

        return {"organization_id": organization_id, "users": users}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch users: {str(e)}")
    
# Create Organization
@router.post("/create_organization")
async def create_organization(organization: OrganizationCreate, request: Request):
    tenant_id = organization.tenant_id
    name = organization.name
    business_email = organization.business_email
    industrial_type = organization.industrial_type
    company_size = organization.company_size
    domain = organization.domain
    image = organization.image
    plan_id = organization.plan_id
    admin_name = organization.admin_name
    admin_email = organization.admin_email
    admin_contact_number = organization.admin_contact_number
    admin_department = organization.admin_department
    configurations = organization.configurations
    settings = organization.settings
    admin_designation = "Admin"
    # Validate email format
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, business_email):
        raise HTTPException(status_code=400, detail="Invalid business email format")
    if not re.match(email_pattern, admin_email):
        raise HTTPException(status_code=400, detail="Invalid admin email format")
    
  
    organization = await Organization.get(tenant_id)
    if organization != {}:
            raise HTTPException(status_code=400, detail="Organization already exists")


    # Validate tenant ID format - only allow lowercase letters, numbers with single - or _ between
    tenant_pattern = r'^[a-z0-9]+$'
    if not re.match(tenant_pattern, tenant_id):
        raise HTTPException(
            status_code=400, 
            detail="Invalid tenant ID format. Only lowercase letters and numbers allowed"
        )
    
    response = {}
    org_id = tenant_id
    print("ORG ID", org_id)
    tenant_service = TenantService()
    try:
        tenant_cred = await tenant_service.get_or_create_tenant_cred(org_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        ui_url = request.headers.get('origin', str(request.base_url))
        set_password_url = f"{ui_url}/users/set_password?tenant_id={org_id}&email={urllib.parse.quote(admin_email)}"
        login_url = f"{ui_url}/users/login?tenant_id={org_id}&email={urllib.parse.quote(admin_email)}"
        
        # Ensure the free_user custom attribute exists in the Cognito User Pool schema
        cognito_user_manager.add_free_tier_to_pool()
        
        user = cognito_user_manager.create_user(
            email=admin_email,
            temporary_password=f"Temp@2024{random_password()}",
            set_password_url=set_password_url,
            login_url=login_url,
            organization_name=name,
            custom_attributes={
                "tenant_id": org_id,
                "is_admin": "true",
                "Department": admin_department,
                "Designation": admin_designation,
                "Name": admin_name,
                "free_user": "true"
            }
        )
        user_id = user['Username']
        # active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")

        # subscription_data = {
        #     "tenant_id": tenant_id,
        #     "price_id": "free_plan",
        #     "credits": 50000,
        #     "created_at": datetime.utcnow().isoformat()
        # }

        # # Insert or update the subscription data
        # await active_subscription.update_one_data(
        #   {"user_id": user_id},
        # {"$set" :subscription_data},
        # upsert=True
        # )

        await add_user_node_db(user_id, org_id)
        org = Organization( 
            _id=org_id,
            name=name,
            business_email=business_email,
            industrial_type=industrial_type,
            company_size=company_size,
            domain=domain,
            image=image,
            plan_id=plan_id,
            admin_id=user_id,
            configurations=configurations,
            group_ids=[],
            settings=settings,
            status="active"

        )
        await Organization.create(org.model_dump())
        costs = {
            "organization_id": org_id,
            "organization_cost": "$0.000000",
            "organization_name": name,
            "users": []
        }
        await Organization.defineCosts(costs)
        await User.create({
            "_id": user_id,
            "name": admin_name,
            "email": admin_email,
            "contact_number": admin_contact_number,
            "department": admin_department,
            "designation": admin_designation,
            "organization_id": org_id,
            "group_ids": [],
            "is_admin": True
        })
        response['organization_id'] = org_id
        response['user_id'] = user_id
        return response
    except Exception as e:
        return {"error": str(e)}

@router.post("/update_organization")
async def update_organization(organization: Organization):
    
    print("ORGANIZATION-----", organization)
    return await Organization.update(organization.id, {
        "name": organization.name,
        "business_email": organization.business_email,
        "industrial_type": organization.industrial_type,
        "company_size": organization.company_size,
        "domain": organization.domain,
        "image": organization.image,
        "plan_id": organization.plan_id,
        "admin_id": organization.admin_id,
        "configurations": organization.configurations.model_dump(),
        "group_ids": organization.group_ids,
        "settings": organization.settings.model_dump(),
        "credits": organization.credits
      
    })


@router.post("/bulk_add_users_to_organization")
async def bulk_add_users_to_organization(data: BulkAddUsersToOrganization, request: Request):
    try:
        # Check if organization exists
        organization = await Organization.get(data.organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Validate that plan_id is one of the Premium plans
        allowed_plans = ["price_1RWBTrCI2zbViAE2WZFApvc8", "price_1RIosGCI2zbViAE25Ny8rOkc", "price_1RIozwCI2zbViAE2beuA7CDk"]
        if data.plan_id not in allowed_plans:
            raise HTTPException(status_code=400, detail="Invalid plan ID. Please select one of the Premium plans.")
        
        # Initialize tenant services
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_or_create_tenant_cred(data.organization_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        ui_url = request.headers.get('origin', str(request.base_url))
        tenant_name = organization["name"]
        
        # Ensure the free_user custom attribute exists in the Cognito User Pool schema
        cognito_user_manager.add_free_tier_to_pool()
        
        # Get active_subscriptions collection
        active_subscription_collection = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        llm_costs_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        
        results = {
            "success": [],
            "failed": []
        }
        
        # Current timestamp for subscription creation
        current_time = datetime.utcnow().isoformat() + "+00:00"
        
        # Process each user
        for user_data in data.users:
            try:
                # Create URLs
                set_password_url = f"{ui_url}/users/set_password?tenant_id={data.organization_id}&email={urllib.parse.quote(user_data.email)}"
                login_url = f"{ui_url}/users/login?tenant_id={data.organization_id}&email={urllib.parse.quote(user_data.email)}"
                
                # Create user in Cognito
                user = cognito_user_manager.create_user(
                    email=user_data.email,
                    temporary_password=f"Temp@2024{random_password()}",
                    organization_name=tenant_name,
                    set_password_url=set_password_url,
                    login_url=login_url,
                    custom_attributes={
                        "tenant_id": data.organization_id,
                        "is_admin": "true",
                        "Department": user_data.department,
                        "Designation": user_data.designation,
                        "Name": user_data.name,
                        "free_user": "false"
                    }
                )
                
                user_id = user['Username']
                
                # Add user to database
                await add_user_node_db(user_id, data.organization_id)
                await User.create({
                    "_id": user_id,
                    "name": user_data.name,
                    "email": user_data.email,
                    "contact_number": "",
                    "department": user_data.department,
                    "designation": user_data.designation,
                    "organization_id": data.organization_id,
                    "group_ids": [],
                    "is_admin": True,
                    "free_user": False
                })
                
                # Create subscription record
                subscription_doc = {
                    "created_at": current_time,
                    "updated_at": current_time,
                    "price_id": data.plan_id,
                    "status": 2,  # Assuming 2 means active
                    "tenant_id": data.organization_id,
                    "user_id": user_id
                }
                
                # Insert subscription record
                await active_subscription_collection.insert(subscription_doc, active_subscription_collection.db)
                if data.organization_id == "b2c":
                    # Find the existing llm_costs document for the b2c organization
                    llm_costs_doc = await llm_costs_collection.get_one(
                        {"organization_id": "b2c"}, 
                        llm_costs_collection.db
                    )
                    
                    # Initialize the new user data for llm_costs
                    new_user_data = {
                        "user_id": user_id,
                        "type": "llm_interaction",
                        "cost": "0.00",
                        "current_plan": data.plan_id
                    }
                    if 'users'  in llm_costs_doc:
                        llm_costs_doc['users'].append(new_user_data)
                     
                    if llm_costs_doc:
                        # Update existing document by appending the new user to the users array
                        await llm_costs_collection.update_one(
                            {"_id": llm_costs_doc["_id"]},
                            llm_costs_doc,
                            upsert=True,
                            db = llm_costs_collection.db
                        )
                    else:
                        # Create new document if it doesn't exist
                        new_llm_costs_doc = {
                            "organization_id": "b2c",
                            "organization_cost": "$0.000",
                            "organization_name": organization.get("name", "Default"),
                            "users": [new_user_data],
                            "cost": "0.00",
                            "current_plan": data.plan_id,
                        }
                        await llm_costs_collection.insert(new_llm_costs_doc, llm_costs_collection.db)
                
                results["success"].append({"email": user_data.email, "user_id": user_id})
                
            except Exception as e:
                results["failed"].append({
                    "email": user_data.email,
                    "reason": str(e)
                })
        
        return {
            "message": f"Processed {len(results['success'])} users successfully, {len(results['failed'])} failed",
            "success_count": len(results['success']),
            "failed_count": len(results['failed']),
            "failed_details": results['failed']
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 

@router.post("/add_admin_user_to_organization")
async def add_admin_user_to_organization(data: AddAdminUserToOrganization, request: Request):
    try:
        # Check if organization exists
        organization = await Organization.get(data.organization_id)
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_or_create_tenant_cred(data.organization_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        ui_url = request.headers.get('origin', str(request.base_url))
        tenant_name = organization["name"]
        set_password_url = f"{ui_url}/users/set_password?tenant_id={data.organization_id}&email={urllib.parse.quote(data.email)}"
        login_url = f"{ui_url}/users/login?tenant_id={data.organization_id}&email={urllib.parse.quote(data.email)}"
        
        # Ensure the free_user custom attribute exists in the Cognito User Pool schema
        cognito_user_manager.add_free_tier_to_pool()
        
        try:
            # Try to create user in Cognito
            user = cognito_user_manager.create_user(
                email=data.email,
                temporary_password=f"Temp@2024{random_password()}",
                organization_name=tenant_name,
                set_password_url=set_password_url,
                login_url=login_url,
                custom_attributes={
                    "tenant_id": data.organization_id,
                    "is_admin": "true",
                    "Department": data.department,
                    "Designation": "Admin",
                    "Name": data.name,
                    "free_user": "true"
                }
            )
        except Exception as cognito_error:
            # Handle specific Cognito errors
            error_message = str(cognito_error)
            
            if "User exists" in error_message or "UsernameExistsException" in error_message or "already exists" in error_message:
                raise HTTPException(
                    status_code=409,  # Conflict status code
                    detail=f"User with email {data.email} already exists in this organization"
                )
            elif "InvalidParameterException" in error_message:
                raise HTTPException(
                    status_code=400,  # Bad Request
                    detail="Invalid user parameters provided"
                )
            elif "InvalidPasswordException" in error_message:
                raise HTTPException(
                    status_code=400,  # Bad Request
                    detail="Invalid password format"
                )
            else:
                # For other Cognito errors, re-raise as 500
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create user in authentication service: {error_message}"
                )
        
        user_id = user['Username'] 
        
        # Get active_subscriptions collection
        active_subscription_collection = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        llm_costs_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        
        await add_user_node_db(user_id, data.organization_id)
        await User.create({
            "_id": user_id,
            "name": data.name,
            "email": data.email,
            "contact_number": data.contact_number,
            "department": data.department,
            "designation": "Admin",
            "organization_id": data.organization_id,
            "group_ids": [],
            "is_admin": True
        })
        
        # Current timestamp for subscription creation
        current_time = datetime.utcnow().isoformat() + "+00:00"
        
        # Create subscription record
        subscription_doc = {
            "created_at": current_time,
            "updated_at": current_time,
            "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
            "status": 2,  # Assuming 2 means active
            "tenant_id": data.organization_id,
            "user_id": user_id
        }
        
        # Insert subscription record
        await active_subscription_collection.insert(subscription_doc, active_subscription_collection.db)
        
        if data.organization_id == "b2c":
            # Find the existing llm_costs document for the b2c organization
            llm_costs_doc = await llm_costs_collection.get_one(
                {"organization_id": "b2c"}, 
                llm_costs_collection.db
            )
                    
            # Initialize the new user data for llm_costs
            new_user_data = {
                "user_id": user_id,
                "type": "llm_interaction",
                "cost": "0.00",
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB"
            }
            
            if 'users' in llm_costs_doc:
                llm_costs_doc['users'].append(new_user_data)
                     
            if llm_costs_doc:
                # Update existing document by appending the new user to the users array
                await llm_costs_collection.update_one(
                    {"_id": llm_costs_doc["_id"]},
                    llm_costs_doc,
                    upsert=True,
                    db=llm_costs_collection.db
                )
            else:
                # Create new document if it doesn't exist
                new_llm_costs_doc = {
                    "organization_id": "b2c",
                    "organization_cost": "$0.000",
                    "organization_name": organization.get("name", "Default"),
                    "users": [new_user_data],
                    "cost": "0.00",
                    "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB"
                }
                await llm_costs_collection.insert(new_llm_costs_doc, llm_costs_collection.db)

        return {"message": "Admin user added successfully"}

    except HTTPException as he:
        # Re-raise HTTP exceptions (including our custom ones)
        raise he
    except Exception as e:
        # Handle any other unexpected errors
        print(f"Unexpected error in add_admin_user_to_organization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
    
@router.delete("/organization/{organization_id}")
async def delete_organization(organization_id: str):
    """
    Delete an organization and all its associated data
    
    Args:
        organization_id (str): The ID of the organization to delete
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found or deletion fails
    """
    try:
        tenant_id = get_tenant_id()
        if tenant_id != ROOT_TENANT_ID:
            raise HTTPException(
                status_code=403,
                detail="You are not authorized to delete this organization"
            )
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Delete the organization and its associated data
        deleted = await Organization.delete(organization_id)
        
        # Get tenant credentials before deleting user pool
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(organization_id)
        if tenant_cred and 'user_pool_id' in tenant_cred:
            userpool_creator = CognitoUserPoolCreator()
            userpool_creator.delete_user_pool(tenant_cred['user_pool_id'])
        
        if deleted:
            return {
                "message": "Organization and associated data successfully deleted",
                "organization_id": organization_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete organization"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while deleting the organization: {str(e)}"
        )

@router.get("/delete_admin_user")
async def delete_admin_user(user_id: str):
    try:
        if get_tenant_id() != ROOT_TENANT_ID:
            raise HTTPException(
                status_code=403,
                detail="You are not authorized to delete this admin user"
            )

        # Get user details to find organization
        user = await User.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        organization_id = user.get('organization_id')
        if not organization_id:
            raise HTTPException(status_code=404, detail="Organization not found for this admin")

        # Count total admin users in the organization
        users = await Organization.get_users(organization_id)
        admin_count = sum(1 for u in users if u.get('is_admin', False))

        if admin_count <= 1 and organization_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete the last admin. At least one admin should be present"
            )

        # Delete from Cognito - continue even if this fails
        try:
            tenant_service = TenantService()
            tenant_cred = await tenant_service.get_tenant_cred(organization_id)
            cognito_user_manager = CognitoUserManager(
                user_pool_id=tenant_cred['user_pool_id'],
                client_id=tenant_cred['client_id']
            )
            cognito_user_manager.delete_user(user_id)
        except Exception as cognito_error:
            # Log the error but continue with database deletion
            print(f"Failed to delete user from Cognito: {str(cognito_error)}")

        # Delete from database
        deleted = await User.delete(user_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Failed to delete admin user")
        
        return {"message": "Admin user deleted successfully"}
    except HTTPException as he:
        raise he
    except Exception as e:
        print("ERROR", e)
        raise HTTPException(
            status_code=500,
            detail=f"{str(e)}"
        )

@router.get("/download_logs_CGA/{tenant_id}/{project_id}/{task_id}")
async def download_logs(tenant_id: str, project_id: int, task_id: str):
    """
    Download all logs for a specific task as a zip file.
    Provides a browser download experience similar to downloading images from Google.
    
    Args:
        project_id: Project ID
        task_id: Task ID
        tenant_id: Optional tenant ID (defaults to current tenant)
        
    Returns:
        Streaming response with zip file
    """
    try:
        check_tenant_authorization()
        print(f"Tennat_id: {tenant_id}, project_id: {project_id}, task_id: {task_id}")
        
        # override tenant_id
        tenant_id = "T0000"
        
        if os.getenv("LOCAL_DEBUG"):
            log_dir = f"/tmp/{tenant_id}/{project_id}/logs/{task_id}/"
        else:
            log_dir = f"/app/data/{tenant_id}/{project_id}/logs/{task_id}/"
        
        # Check if directory exists
        if not os.path.exists(log_dir):
            raise HTTPException(status_code=404, detail=f"No logs found for task {task_id}")
        
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs_task_{task_id}_{timestamp}.zip"
        
        # Create zip file in memory
        zip_io = io.BytesIO()
        with zipfile.ZipFile(zip_io, mode='w', compression=zipfile.ZIP_DEFLATED) as zipf:
            # Walk through the directory and add all files
            for root, _, files in os.walk(log_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Add file to zip with relative path
                    arcname = os.path.relpath(file_path, log_dir)
                    zipf.write(file_path, arcname)
        
        # Reset the pointer to the beginning of the BytesIO object
        zip_io.seek(0)
        
        # Return streaming response with appropriate headers for download
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return StreamingResponse(
            zip_io, 
            media_type="application/zip",
            headers=headers
        )
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        import traceback
        print(f"Error creating log zip file: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create log archive: {str(e)}")
    

@router.get("/is_organization_exists")
async def is_organization_exists(tenant_id: str):
    organization = await Organization.get(tenant_id)
    return {"exists": organization != {}}

@router.post("/update_organization_status")
async def update_organization_status(organization_id: str, status: str):
    """
    Update organization status to active or inactive
    
    Args:
        organization_id (str): The ID of the organization to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found, invalid status, or update fails.
    """
    try:
        tenant_id = get_tenant_id()
        # if tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update organization status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Update the organization status
        updated = await Organization.update(organization_id, {"status": status})
        
        if updated:
            try:
                if status == "active":
                    admin_id = organization["admin_id"]
                    # Get admin user details
                    admin_user = await User.get(admin_id)
                    admin_name = admin_user.get("name", "User")
                    
                    # Personalize the email with admin name
                    activation_email_html = open(EmailTemplates.ACTIVATION_TEMPLATE, "r").read().replace(
                        "%name%", admin_name
                    )
                    ses_handler.send_email_smtp(
                        to_address=organization["business_email"],
                        subject="✅ Account Activation Successful",
                        body_text="Your account has been successfully activated.",
                        body_html=activation_email_html
                    )
                elif status == "inactive":
                    admin_id = organization["admin_id"]
                    # Get admin user details
                    admin_user = await User.get(admin_id)
                    admin_name = admin_user.get("name", "User")
                    deactivation_email_html = open(EmailTemplates.DEACTIVATION_TEMPLATE, "r").read().replace(
                        "{{name}}", admin_name
                    )
                    ses_handler.send_email_smtp(
                        to_address=organization["business_email"],
                        subject=" Account Deactivated",
                        body_text="Your account has been deactivated.",
                        body_html=deactivation_email_html
                    )
            except Exception as e:
                print("ERROR", e)
            return {
                "message": f"Organization status updated to {status} successfully",
                "organization_id": organization_id,
                "status": status
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to update organization status"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating organization status: {str(e)}"
        )

@router.post("/update_organization_public_access")
async def update_organization_public_access(organization_id: str, opentopublic: bool):
    """
    Update organization's public access setting
    
    Args:
        organization_id (str): The ID of the organization to update
        opentopublic (bool): Whether the organization should be open to public
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If organization not found or update fails
    """
    try:
        tenant_id = get_tenant_id()
        print("TENANT ID", tenant_id)
        # if tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update organization public access"
        #     )
            
        # First check if organization exists
        organization = await Organization.get(organization_id)
        if not organization:
            # Only create the organization if it's the Kavia super tenant ID
            if organization_id == ROOT_TENANT_ID:
                try:
                    org = Organization( 
                        _id=organization_id,
                        name=f"Super Admin",
                        business_email="<EMAIL>",
                        industrial_type="IT",
                        company_size="small",
                        domain="kavia.ai",
                        image=None,
                        plan_id="",  # Use default or first available plan
                        admin_id="",  # Will be updated later
                        opentopublic=opentopublic,
                        configurations=Configurations(),
                        group_ids=[],
                        settings=Settings()
                    )
                    await Organization.create(org.model_dump())
                    return {
                        "message": f"Organization created with public access set to {opentopublic}",
                        "organization_id": organization_id,
                        "opentopublic": opentopublic
                    }
                except Exception as e:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create organization: {str(e)}"
                    )
            else:
                # For non-ROOT_TENANT_ID, return error that organization doesn't exist
                raise HTTPException(
                    status_code=404,
                    detail="Organization not found"
                )

        # Update the organization's public access setting
        updated = await Organization.update(organization_id, {"opentopublic": opentopublic})
        
        if updated:
            return {
                "message": f"Organization public access updated to {opentopublic} successfully",
                "organization_id": organization_id,
                "opentopublic": opentopublic
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to update organization public access"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating organization public access: {str(e)}"
        )

@router.post("/update_user_status")
async def update_user_status(tenant_id: str, email: str, status: str):
    """
    Update a user's status to active or inactive
    
    Args:
        tenant_id (str): The ID of the tenant/organization
        email (str): The email of the user to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If user not found, invalid status, or update fails
    """
    try:
        # Check if request is from super tenant
        current_tenant_id = get_tenant_id()
        # if current_tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update user status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # Check if organization exists
        organization = await Organization.get(tenant_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Get the user through Cognito by email
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        try:
            # Update user's enabled status in Cognito
            if status == "active":
                cognito_user_manager.enable_user(email)
            else:  # status == "inactive"
                cognito_user_manager.disable_user(email)
                
            # Update user status in MongoDB
            users_collection = User.get_collection()
            
            # Try to update the user by email first
            result = users_collection.update_one(
                {"email": email, "organization_id": tenant_id},
                {"$set": {"status": status, "updated_at": datetime.utcnow()}}
            )
            
            if result.modified_count == 0:
                # Try by username/ID if update by email wasn't successful
                # Get the user ID from Cognito
                user = cognito_user_manager.get_user_by_identifier(email)
                username = user['Username']
                
                result = users_collection.update_one(
                    {"_id": username, "organization_id": tenant_id},
                    {"$set": {"status": status, "updated_at": datetime.utcnow()}}
                )
                
                if result.modified_count == 0:
                    result = users_collection.update_one(
                        {"cognito_id": username, "organization_id": tenant_id},
                        {"$set": {"status": status, "updated_at": datetime.utcnow()}}
                    )
            
            # Even if MongoDB update fails, we return success as Cognito is the source of truth
            return {
                "message": f"User status updated to {status} successfully",
                "email": email,
                "organization_id": tenant_id,
                "status": status
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"User not found or could not be updated: {str(e)}"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating user status: {str(e)}"
        )

@router.post("/update_user_status_by_id")
async def update_user_status_by_id(tenant_id: str, user_id: str, status: str):
    """
    Update a user's status to active or inactive using user ID
    
    Args:
        tenant_id (str): The ID of the tenant/organization
        user_id (str): The ID of the user to update
        status (str): The new status ('active' or 'inactive')
        
    Returns:
        dict: A message indicating success or failure
        
    Raises:
        HTTPException: If user not found, invalid status, or update fails
    """
    try:
        # Check if request is from super tenant
        current_tenant_id = get_tenant_id()
        # if current_tenant_id != ROOT_TENANT_ID:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="You are not authorized to update user status"
        #     )
            
        # Validate status
        if status not in ["active", "inactive"]:
            raise HTTPException(
                status_code=400,
                detail="Status must be either 'active' or 'inactive'"
            )
            
        # Check if organization exists
        organization = await Organization.get(tenant_id)
        if not organization:
            raise HTTPException(
                status_code=404,
                detail="Organization not found"
            )

        # Get user from database first to get their email
        user = await User.get(user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        email = user.get("email")
        if not email:
            raise HTTPException(
                status_code=404,
                detail="User email not found in database"
            )

        # Get the Cognito user manager
        tenant_service = TenantService()
        tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
        cognito_user_manager = CognitoUserManager(
            user_pool_id=tenant_cred['user_pool_id'],
            client_id=tenant_cred['client_id']
        )
        
        try:
            # Update user's enabled status in Cognito
            if status == "active":
                cognito_user_manager.enable_user(email)
            else:  # status == "inactive"
                cognito_user_manager.disable_user(email)
                
            # Update user status in MongoDB
            result = await User.update(
                user_id, 
                {"status": status}
            )
            
            if not result:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to update user status in database"
                )
            
            return {
                "message": f"User status updated to {status} successfully",
                "user_id": user_id,
                "organization_id": tenant_id,
                "status": status
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail=f"User not found or could not be updated: {str(e)}"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating user status: {str(e)}"
        )




# Replace the existing get_all_used_pod function with this updated version
@router.get("/get_all_used_pod")
async def get_all_used_pod():
    """Get all used pods using direct Kubernetes API instead of MongoDB."""
    try:
        check_tenant_authorization()
        
        # Create Kubernetes pods manager instance
        environment, namespace = get_environment_and_namespace()
        
        if not environment or not namespace:
            raise HTTPException(
                status_code=500,
                detail="Environment or namespace not configured properly"
            )
        
        
        # Get used pods directly from Kubernetes
        used_pods, count = kubernetes_manager.get_used_codegen_pods()
        
        return {
            "status": "success",
            "data": used_pods,
            "count": count,
            "message": f"Successfully retrieved {count} used pod records from Kubernetes",
            "source": "kubernetes_api",  # Indicate data source
            "environment": kubernetes_manager.environment,
            "namespace": kubernetes_manager.namespace
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching used pods: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch used pods data: {str(e)}"
        )
    
@router.get("/get_all_available_pod")
async def get_all_available_pod():
    """Get all available pods using direct Kubernetes API instead of MongoDB."""
    try:
        check_tenant_authorization()
        
        # Create Kubernetes pods manager instance
        environment, namespace = get_environment_and_namespace()
        
        if not environment or not namespace:
            raise HTTPException(
                status_code=500,
                detail="Environment or namespace not configured properly"
            )
        
        # Get available pods directly from Kubernetes
        available_pods, count = kubernetes_manager.get_available_codegen_pods()
        
        return {
            "status": "success",
            "data": available_pods,
            "count": count,
            "message": f"Successfully retrieved {count} available pod records from Kubernetes",
            "source": "kubernetes_api",  # Indicate data source
            "environment": kubernetes_manager.environment,
            "namespace": kubernetes_manager.namespace
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching available pods: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch available pods data: {str(e)}"
        )

@router.get("/check_project_id_to_pod_map/{project_id}")
async def check_project_id_to_pod_map(project_id: str):
    """
    Check which pod project ID is connected to the given application project ID.
    
    This function searches through used pods to find matches
    for the given project ID and returns detailed pod information.
    
    Args:
        project_id: The application project ID to search for
        
    Returns:
        Dictionary containing pod mapping information
        
    Raises:
        HTTPException: If authorization fails or internal error occurs
    """
    try:
        check_tenant_authorization()
        
        # Get environment and namespace
        environment, namespace = get_environment_and_namespace()
        
        if not environment or not namespace:
            raise HTTPException(
                status_code=500,
                detail="Environment or namespace not configured properly"
            )
        
        # Initialize results structure
        result = {
            "application_project_id": project_id,
            "environment": environment,
            "namespace": namespace,
            "found_pods": [],
            "total_matches": 0,
            "search_timestamp": datetime.utcnow().isoformat()
        }
        
        # Search in used pods only
        try:
            used_pods, used_count = kubernetes_manager.get_used_codegen_pods()
            
            for pod in used_pods:
                pod_project_id = pod.get("project_id", "").lower()
                application_project_id = pod.get("application_project_id","")
                # Check if this pod project ID matches the application project ID
                if str(application_project_id) == str(project_id):
                    pod_info = {
                        "pod_name": pod.get("name", ""),
                        "pod_id": pod_project_id,
                        "application_project_id": application_project_id,
                        "pod_status": "used",
                        "pod_phase": pod.get("status", ""),
                        "pod_ip": pod.get("ip", ""),
                        "node": pod.get("node", ""),
                        "age": pod.get("age", ""),
                        "ready": pod.get("ready", ""),
                        "restarts": pod.get("restarts", 0),
                        "containers": pod.get("containers", []),
                        "labels": pod.get("labels", {}),
                        "namespace": pod.get("namespace", ""),
                        "configmap_name": f"pod-status-{pod_project_id}-{environment}",
                        "session_id": pod.get("session_id", ""),
                        "tenant_id": pod.get("tenant_id", ""),
                        "assigned_at": pod.get("assigned_at", ""),
                        "usage_status": pod.get("usage_status", "")
                    }
                    result["found_pods"].append(pod_info)
                    result["total_matches"] += 1
                    
        except Exception as e:
            print(f"Error searching used pods: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to search used pods: {str(e)}"
            )
        
        # Check if any pods were found
        if result["total_matches"] == 0:
            return {
                "status": "not_found",
                "message": f"No used pods found for application project ID: {project_id}",
                "data": result
            }
        else:
            return {
                "status": "success",
                "message": f"Found {result['total_matches']} used pod(s) for application project ID: {project_id}",
                "data": result
            }
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in check_project_id_to_pod_map: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check project ID to pod mapping: {str(e)}"
        )



@router.get("/tenants_filter_service_type")
async def get_tenants(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None)
):
    """Get all tenants with session statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                match_stage["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                match_stage["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        if status:
            match_stage["status"] = status
            
        if service_type:
            match_stage["service_type"] = service_type
        
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": "$tenant_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_activity": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_activity": -1}}
        ])
        
        tenants = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "tenants": [
                {
                    "id": tenant["_id"],
                    "name": get_organization_name_by_tenant_id(tenant["_id"]),
                    "total_sessions": tenant["total_sessions"],
                    "active_sessions": tenant["active_sessions"],
                    "completed_sessions": tenant["completed_sessions"],
                    "failed_sessions": tenant["failed_sessions"],
                    "total_cost": tenant["total_cost"],
                    "last_activity": tenant["last_activity"].isoformat() if tenant["last_activity"] else None
                }
                for tenant in tenants
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status,
                "service_type": service_type
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/tenants/{tenant_id}/users")
async def get_users_by_tenant(
    tenant_id: str,
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None)
):
    """Get all users for a specific tenant with session statistics and optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage
        match_stage = {"tenant_id": tenant_id}
        
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                match_stage["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                match_stage["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        if status:
            match_stage["status"] = status
            
        if service_type:
            match_stage["service_type"] = service_type
        
        pipeline = [
            {"$match": match_stage},
            {
                "$group": {
                    "_id": "$user_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_session": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_session": -1}}
        ]
        
        users = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "users": [
                {
                    "id": user["_id"],
                    "name": get_name_by_user_id(user["_id"]),
                    "total_sessions": user["total_sessions"],
                    "active_sessions": user["active_sessions"],
                    "total_cost": user["total_cost"],
                    "last_session": user["last_session"].isoformat() if user["last_session"] else None
                }
                for user in users
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status,
                "service_type": service_type
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/tenants/{tenant_id}/users/{user_id}/sessions")
async def get_sessions_by_user(
    tenant_id: str, 
    user_id: str,
    status: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(0, ge=0, le=1000)  # Changed: Allow 0 for no limit, increased max to 1000
):
    """Get sessions for a specific user"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build query
        query = {"tenant_id": tenant_id, "user_id": user_id}
        
        if status:
            query["status"] = status
            
        if service_type:
            query["service_type"] = service_type
            
        if date_from or date_to:
            query["created_at"] = {}
            if date_from:
                query["created_at"]["$gte"] = datetime.fromisoformat(date_from)
            if date_to:
                query["created_at"]["$lte"] = datetime.fromisoformat(date_to)
        
        # Get total count
        total = mongo_db.db["session_tracking"].count_documents(query)
        
        # Get sessions with conditional pagination
        sessions_query = mongo_db.db["session_tracking"].find(query).sort("created_at", -1)
        
        # Apply pagination only if limit > 0
        if limit > 0:
            sessions = list(sessions_query.skip((page - 1) * limit).limit(limit))
            total_pages = (total + limit - 1) // limit
        else:
            # No limit - return all records
            sessions = list(sessions_query)
            total_pages = 1
            limit = total  # Set limit to total for response consistency
            page = 1  # Reset page to 1 since we're returning everything
        
        # Format sessions
        formatted_sessions = []
        for session in sessions:
            session["_id"] = str(session["_id"])
            if session.get("session_start"):
                session["session_start"] = session["session_start"].isoformat()
            if session.get("session_end"):
                session["session_end"] = session["session_end"].isoformat()
            if session.get("last_updated"):
                session["last_updated"] = session["last_updated"].isoformat()
            if session.get("created_at"):
                session["created_at"] = session["created_at"].isoformat()
            
            # Format cost history timestamps
            if session.get("cost_history"):
                for cost_entry in session["cost_history"]:
                    if cost_entry.get("timestamp"):
                        cost_entry["timestamp"] = cost_entry["timestamp"].isoformat()
                
            formatted_sessions.append(session)
        
        return {
            "success": True,
            "sessions": formatted_sessions,
            "total": total,
            "page": page,
            "limit": limit,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/{task_id}")
async def get_session_details(task_id: str):
    """Get detailed session information including cost history"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Get session
        session = mongo_db.db["session_tracking"].find_one({"task_id": task_id})
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Format dates
        if session.get("session_start"):
            session["session_start"] = session["session_start"].isoformat()
        if session.get("session_end"):
            session["session_end"] = session["session_end"].isoformat()
        if session.get("last_updated"):
            session["last_updated"] = session["last_updated"].isoformat()
        if session.get("created_at"):
            session["created_at"] = session["created_at"].isoformat()
        
        session["_id"] = str(session["_id"])
        
        # Format cost history timestamps
        if session.get("cost_history"):
            for cost_entry in session["cost_history"]:
                if cost_entry.get("timestamp"):
                    cost_entry["timestamp"] = cost_entry["timestamp"].isoformat()
        
        return {
            "success": True,
            "session": session
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



    
@router.delete("/delete_available_pod/{pod_name}")
async def delete_particular_available_pod(pod_name: str, background_tasks: BackgroundTasks):
    try:
        # Authorization check
        check_tenant_authorization()
        pod = KubernetesAvailablePodsManager('dev')
        namespace = pod._get_namespace()
        
        STAGE = os.environ.get("STAGE",None)
        if 'dev' in STAGE:
            STAGE = 'dev'
        background_tasks.add_task(cleanup_pod_sync, pod_name, STAGE, namespace)
        return {
            "status": "success",
            "message": f"Successfully deleted available pod with pod_id: {pod_name}"
        }
        
    except HTTPException:
        raise
    except PyMongoError as e:
        print(f"Database error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Database operation failed"
        )
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete available pod"
        )


@router.delete("/delete_project_to_pod_map/{project_id}")
async def delete_pod_by_project(project_id: str):
    try:
        # TODO
        pass
    except:
        pass
@router.delete("/delete_pod_by_pod_id/{pod_id}")
async def delete_pod_by_project(pod_id: str):
    try:
        # TODO
        pass
    except:
        pass


@router.delete("/delete_all_available_pods")
async def delete_all_available_pods():
    try:
        # TODO
        pass
    except:
        pass

@router.delete("/delete_multiple_used_pods")
async def delete_multiple_used_pods(request: PodBulkDeleteRequest):
    try:
        # TODO
        pass
    except:
        pass
    
@router.get("/stats/overall")
async def get_overall_stats(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """Get overall dashboard statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        # Main pipeline for overall stats
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": None,
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "unique_tenants": {"$addToSet": "$tenant_id"},
                    "unique_users": {"$addToSet": "$user_id"}
                }
            }
        ])
        
        result = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        if result:
            stats = result[0]
            return {
                "success": True,
                "stats": {
                    "total_sessions": stats["total_sessions"],
                    "active_sessions": stats["active_sessions"],
                    "completed_sessions": stats["completed_sessions"],
                    "failed_sessions": stats["failed_sessions"],
                    "total_cost": stats["total_cost"],
                    "total_tenants": len(stats["unique_tenants"]),
                    "total_users": len(stats["unique_users"])
                },
                "filters_applied": {
                    "date_from": date_from,
                    "date_to": date_to,
                    "status": status
                }
            }
        else:
            return {
                "success": True,
                "stats": {
                    "total_sessions": 0,
                    "active_sessions": 0,
                    "completed_sessions": 0,
                    "failed_sessions": 0,
                    "total_cost": 0.0,
                    "total_tenants": 0,
                    "total_users": 0
                },
                "filters_applied": {
                    "date_from": date_from,
                    "date_to": date_to,
                    "status": status
                }
            }
        
    except Exception as e:
        print(f"Error in get_overall_stats: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/tenants")
async def get_tenants(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """Get all tenants with session statistics with optional date filtering"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage for filtering
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        pipeline = []
        
        # Add match stage if we have filters
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": "$tenant_id",
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}
                    },
                    "completed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "failed_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}
                    },
                    "total_cost": {"$sum": "$total_cost"},
                    "last_activity": {"$max": "$last_updated"}
                }
            },
            {"$sort": {"last_activity": -1}}
        ])
        
        tenants = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "tenants": [
                {
                    "id": tenant["_id"],
                    "name": get_organization_name_by_tenant_id(tenant["_id"]),
                    "total_sessions": tenant["total_sessions"],
                    "active_sessions": tenant["active_sessions"],
                    "completed_sessions": tenant["completed_sessions"],
                    "failed_sessions": tenant["failed_sessions"],
                    "total_cost": tenant["total_cost"],
                    "last_activity": tenant["last_activity"].isoformat() if tenant["last_activity"] else None
                }
                for tenant in tenants
            ],
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status
            }
        }
        
    except Exception as e:
        print(f"Error in get_tenants: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/stats/chart-data")
async def get_chart_data(
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    granularity: Optional[str] = Query("day", regex="^(day|hour)$")
):
    """Get chart data for sessions over time"""
    try:
        mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        
        # Build match stage
        match_stage = {}
        
        # Safe datetime parsing
        if date_from or date_to:
            match_stage["created_at"] = {}
            if date_from:
                parsed_from = safe_parse_datetime(date_from)
                if parsed_from:
                    match_stage["created_at"]["$gte"] = parsed_from
            if date_to:
                parsed_to = safe_parse_datetime(date_to)
                if parsed_to:
                    match_stage["created_at"]["$lte"] = parsed_to
        
        if status and status.lower() != 'all':
            match_stage["status"] = status
        
        # Set default date range if not provided (last 7 days)
        if not date_from and not date_to:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=7)
            match_stage["created_at"] = {
                "$gte": start_date,
                "$lte": end_date
            }
        
        # Choose date format based on granularity
        if granularity == "hour":
            date_format = "%Y-%m-%d %H:00:00"
        else:
            date_format = "%Y-%m-%d"
        
        pipeline = []
        
        if match_stage:
            pipeline.append({"$match": match_stage})
        
        pipeline.extend([
            {
                "$group": {
                    "_id": {
                        "date": {"$dateToString": {"format": date_format, "date": "$created_at"}},
                        "tenant_id": "$tenant_id",
                        "user_id": "$user_id"
                    },
                    "sessions": {"$sum": 1},
                    "total_cost": {"$sum": "$total_cost"},
                    "active_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "active"]}, 1, 0]}},
                    "completed_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}},
                    "failed_sessions": {"$sum": {"$cond": [{"$eq": ["$status", "failed"]}, 1, 0]}}
                }
            },
            {
                "$group": {
                    "_id": "$_id.date",
                    "unique_users": {"$addToSet": "$_id.user_id"},
                    "unique_tenants": {"$addToSet": "$_id.tenant_id"},
                    "total_sessions": {"$sum": "$sessions"},
                    "total_cost": {"$sum": "$total_cost"},
                    "active_sessions": {"$sum": "$active_sessions"},
                    "completed_sessions": {"$sum": "$completed_sessions"},
                    "failed_sessions": {"$sum": "$failed_sessions"}
                }
            },
            {
                "$project": {
                    "date": "$_id",
                    "unique_users_count": {"$size": "$unique_users"},
                    "unique_tenants_count": {"$size": "$unique_tenants"},
                    "total_sessions": 1,
                    "total_cost": 1,
                    "active_sessions": 1,
                    "completed_sessions": 1,
                    "failed_sessions": 1,
                    "_id": 0
                }
            },
            {"$sort": {"date": 1}}
        ])
        
        result = list(mongo_db.db["session_tracking"].aggregate(pipeline))
        
        return {
            "success": True,
            "chart_data": result,
            "granularity": granularity,
            "filters_applied": {
                "date_from": date_from,
                "date_to": date_to,
                "status": status
            }
        }
        
    except Exception as e:
        print(f"Error in get_chart_data: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


referral_service = ReferralService()

# SUPER ADMIN ENDPOINTS
@router.post("/admin/referral/create-code/{user_id}", response_model=ReferralCodeResponse)
async def create_referral_code_for_user(
    user_id: str,
    force_regenerate: bool = Query(False, description="Force regenerate if code already exists")
):
    """Create or regenerate referral code for a specific user (Super Admin only)"""
    try:
        result = await referral_service.create_referral_code(user_id, force_regenerate)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error creating referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/referral/validate/{referral_code}", response_model=ValidateReferralResponse)
async def validate_referral_code(referral_code: str):
    """Validate a referral code (Public endpoint for registration form)"""
    try:
        result = await referral_service.validate_referral_code(referral_code)
        return result
    except Exception as e:
        print(f"Error validating referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/stats/{user_id}")
async def get_user_referral_stats(user_id: str):
    """Get referral statistics for a user (Super Admin only)"""
    try:
        stats = await referral_service.get_referral_stats(user_id)
        return {
            "success": True,
            "data": stats
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error getting referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/organization-stats/{organization_id}")
async def get_organization_referral_stats(organization_id: str):
    """Get referral statistics for an entire organization (Super Admin only)"""
    try:
        stats = await referral_service.get_organization_referral_stats(organization_id)
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        print(f"Error getting organization referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/all-referrers")
async def get_all_referrers():
    """Get all users with referral codes across all organizations (Super Admin only)"""
    try:
        # Get all users with referral codes
        referrers_cursor = referral_service.users_collection.db.users.find(
            {"referral_code": {"$exists": True, "$ne": None}},
            {
                "_id": 1,
                "name": 1,
                "email": 1,
                "organization_id": 1,
                "referral_code": 1,
                "referral_stats": 1,
                "status": 1,
                "created_at": 1
            }
        ).sort("organization_id", 1)
        
        referrers = list(referrers_cursor)
        
        # Group by organization
        organizations = {}
        for referrer in referrers:
            org_id = referrer["organization_id"]
            if org_id not in organizations:
                organizations[org_id] = {
                    "organization_id": org_id,
                    "referrers": [],
                    "total_referrals": 0
                }
            
            organizations[org_id]["referrers"].append(referrer)
            organizations[org_id]["total_referrals"] += referrer.get("referral_stats", {}).get("total_referrals", 0)
        
        return {
            "success": True,
            "data": {
                "total_referrers": len(referrers),
                "organizations": list(organizations.values())
            }
        }
        
    except Exception as e:
        print(f"Error getting all referrers: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/referral/user-codes/{user_id}")
async def get_user_referral_code(user_id: str):
    """Get referral code for a specific user (for sharing purposes)"""
    try:
        user = await referral_service.users_collection.get_one(
            filter={"_id": user_id},
            db=referral_service.users_collection.db.users
        )
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        referral_code = user.get("referral_code")
        if not referral_code:
            return {
                "success": False,
                "message": "No referral code found for this user"
            }
        
        stats = ReferralStats(**user.get("referral_stats", {}))
        
        return {
            "success": True,
            "data": {
                "referral_code": referral_code,
                "user_name": user["name"],
                "organization_id": user["organization_id"],
                "stats": stats.model_dump(),
                "share_url": f"{getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')}/signup?ref={referral_code}"
            }
        }
        
    except Exception as e:
        print(f"Error getting referral code: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/referral/update-stats/{user_id}")
async def update_referral_stats_on_verification(
    user_id: str,
    action: str = Query(..., description="Action: verified, activated, deactivated")
):
    """Update referral stats when referred user status changes"""
    try:
        user = await referral_service.users_collection.get_one(
            filter={"_id": user_id},
            db=referral_service.users_collection.db.users
        )
        
        if not user or not user.get("referred_by"):
            return {
                "success": True,
                "message": "No referral to update"
            }
        
        referrer_id = user["referred_by"]["user_id"]
        
        # Update referrer stats based on action
        if action == "verified":
            await referral_service.update_referral_stats(referrer_id, "verified_referrals")
        elif action == "activated":
            await referral_service.update_referral_stats(referrer_id, "active_referrals")
        elif action == "deactivated":
            # Decrement active referrals
            await referral_service.users_collection.update_one_data(
                filter={"_id": referrer_id},
                update={
                    "$inc": {"referral_stats.active_referrals": -1},
                    "$set": {"updated_at": datetime.now()}
                }
            )
        
        return {
            "success": True,
            "message": f"Referral stats updated for action: {action}"
        }
        
    except Exception as e:
        print(f"Error updating referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@router.get("/admin/referral/detailed-stats/{user_id}")
async def get_detailed_referral_stats(user_id: str):
    """Get detailed referral statistics with individual usage records"""
    try:
        referral_service = ReferralService()
        stats = await referral_service.get_detailed_referral_stats(user_id)
        return {
            "success": True,
            "data": stats
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"Error getting detailed referral stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/admin/referral/usage-history/{user_id}")
async def get_referral_usage_history(
    user_id: str,
    limit: int = Query(50, ge=1, le=1000),
    skip: int = Query(0, ge=0)
):
    """Get paginated referral usage history for a user"""
    try:
        referral_service = ReferralService()
        
        referrer = referral_service.users_collection.db[referral_service.users_collection.collection].find_one(
            {"_id": user_id},
            {"referral_stats.referral_history": 1, "name": 1, "email": 1}
        )
        
        if not referrer:
            raise HTTPException(status_code=404, detail="User not found")
        
        referral_history = referrer.get("referral_stats", {}).get("referral_history", [])
        
        # Sort by referred_at descending and paginate
        sorted_history = sorted(
            referral_history,
            key=lambda x: x.get("referred_at", datetime.min),
            reverse=True
        )
        
        paginated_history = sorted_history[skip:skip + limit]
        
        return {
            "success": True,
            "data": {
                "referrer_name": referrer["name"],
                "referrer_email": referrer["email"],
                "total_referrals": len(referral_history),
                "returned_count": len(paginated_history),
                "skip": skip,
                "limit": limit,
                "referral_history": paginated_history
            }
        }
        
    except Exception as e:
        print(f"Error getting referral usage history: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")