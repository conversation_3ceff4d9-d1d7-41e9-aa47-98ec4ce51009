from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, Request
import boto3,time
from botocore.exceptions import ClientError, ParamValidationError
from app.core.Settings import settings
from app.connection.establish_db_connection import get_node_db
from fastapi import Query, Body, BackgroundTasks
from app.models.auth_model import Cognito<PERSON>ser, SignUpUser, ReferralCodeResponse, ReferralStats, ValidateReferralResponse, ReferredBy, ReferralUsage
from app.routes.users_route import format_response_user
from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_userpool import CognitoUserPoolCreator
from app.utils.hash import decrypt_tenant_id, encrypt_tenant_id
from app.models.organization_models import Organization, User
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.connection.tenant_middleware import get_opentopublic
import logging
from datetime import datetime, timedelta
import random
import string
import requests
from fastapi.responses import RedirectResponse, JSONResponse
from typing import Optional, Dict, Any
import jwt
import urllib.parse
from pydantic import BaseModel
from app.utils.aws.cognito_user_manager import CognitoUserManager

DB_NAME = KAVIA_ROOT_DB_NAME

db = get_node_db()
tenant_service = TenantService()
default_tenant_id = settings.KAVIA_SUPER_TENANT_ID

# Set up logging
logger = logging.getLogger(__name__)

# Add this configuration setting near the top of the file after the imports
ENABLE_PASSWORDLESS_GOOGLE_AUTH = getattr(settings, 'ENABLE_PASSWORDLESS_GOOGLE_AUTH', True)  # Default to True

class PromotionalCounterService:
    def __init__(self):
        self.counters_collection = get_mongo_db(
            db_name=KAVIA_ROOT_DB_NAME,
            collection_name="promotional_counters"
        )
        self.FIRST_1000_PROMOTION_ID = "first_1000_promotion"
        self.FIRST_1000_LIMIT = 1000
        self.PROMOTIONAL_PRICE_ID = "price_1RqqagCI2zbViAE2XU0Hx3i6"  # Early Users plan
        self.PROMOTIONAL_CREDITS = 220000  # 220,000 credits for Early Users
        self.PROMOTIONAL_PRODUCT_ID = "prod_SmPPog1xkuhHza"  # Early Users product

    async def initialize_counter(self):
        """Initialize the promotional counter if it doesn't exist"""
        try:
            # Check if counter already exists
            existing = self.counters_collection.db[self.counters_collection.collection].find_one(
                {"_id": self.FIRST_1000_PROMOTION_ID}
            )

            if not existing:
                counter_doc = {
                    "_id": self.FIRST_1000_PROMOTION_ID,
                    "count": 0,
                    "limit": self.FIRST_1000_LIMIT,
                    "active": True,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }

                self.counters_collection.db[self.counters_collection.collection].insert_one(counter_doc)
                logger.info(f"Initialized promotional counter: {self.FIRST_1000_PROMOTION_ID}")

        except Exception as e:
            logger.error(f"Error initializing promotional counter: {str(e)}")
            raise e

    async def check_and_increment_counter(self) -> bool:
        """
        Atomically check if promotion is available and increment counter
        Returns True if user gets promotional credits, False otherwise
        """
        try:
            # Ensure counter exists
            await self.initialize_counter()

            # Atomic operation: increment only if count < limit
            result = self.counters_collection.db[self.counters_collection.collection].find_one_and_update(
                {
                    "_id": self.FIRST_1000_PROMOTION_ID,
                    "count": {"$lt": self.FIRST_1000_LIMIT},
                    "active": True
                },
                {
                    "$inc": {"count": 1},
                    "$set": {"updated_at": datetime.now()}
                },
                return_document=True  # Return updated document
            )

            if result:
                logger.info(f"Promotional counter incremented to {result['count']}/{self.FIRST_1000_LIMIT}")
                return True
            else:
                logger.info("Promotional limit reached or counter inactive")
                return False

        except Exception as e:
            logger.error(f"Error checking/incrementing promotional counter: {str(e)}")
            return False

    async def get_current_count(self) -> Dict[str, Any]:
        """Get current promotional counter status"""
        try:
            counter = self.counters_collection.db[self.counters_collection.collection].find_one(
                {"_id": self.FIRST_1000_PROMOTION_ID}
            )

            if counter:
                return {
                    "count": counter.get("count", 0),
                    "limit": counter.get("limit", self.FIRST_1000_LIMIT),
                    "active": counter.get("active", True),
                    "remaining": max(0, counter.get("limit", self.FIRST_1000_LIMIT) - counter.get("count", 0))
                }
            else:
                return {
                    "count": 0,
                    "limit": self.FIRST_1000_LIMIT,
                    "active": True,
                    "remaining": self.FIRST_1000_LIMIT
                }

        except Exception as e:
            logger.error(f"Error getting promotional counter status: {str(e)}")
            return {
                "count": 0,
                "limit": self.FIRST_1000_LIMIT,
                "active": False,
                "remaining": 0
            }

class ReferralService:
    def __init__(self):
        self.users_collection = get_mongo_db(
            db_name=KAVIA_ROOT_DB_NAME, 
            collection_name="users"
        )
    
    def generate_referral_code(self, organization_id: str, user_name: str) -> str:
        """Generate a unique referral code for a user"""
        # Format: [ORG_PREFIX][USER_INITIALS][RANDOM_NUMBER]
        org_prefix = organization_id.upper()[:3]
        
        # Get user initials (max 2 characters)
        name_parts = user_name.strip().split()
        if len(name_parts) >= 2:
            initials = f"{name_parts[0][0]}{name_parts[1][0]}".upper()
        elif len(name_parts) == 1:
            initials = name_parts[0][:2].upper()
        else:
            initials = "XX"
        
        # Generate random suffix
        random_suffix = ''.join(random.choices(string.digits, k=3))
        
        base_code = f"{org_prefix}{initials}{random_suffix}"
        
        # Ensure uniqueness by checking database
        attempt = 0
        while attempt < 10:  # Max 10 attempts to find unique code
            test_code = f"{base_code}" if attempt == 0 else f"{base_code}{attempt}"
            
            # Check if code already exists - Use direct MongoDB access
            existing = self.users_collection.db[self.users_collection.collection].find_one(
                {"referral_code": test_code}
            )
            
            if not existing:
                return test_code
            
            attempt += 1
        
        # Fallback to timestamp-based code if all attempts fail
        timestamp_suffix = str(int(datetime.now().timestamp()))[-4:]
        return f"{org_prefix}{initials}{timestamp_suffix}"
    
    async def create_referral_code(self, user_id: str, force_regenerate: bool = False) -> ReferralCodeResponse:
        """Create or regenerate referral code for a user"""
        try:
            # DEBUG: Log the user_id being searched
            logger.info(f"Searching for user with ID: {user_id} (type: {type(user_id)})")
            
            # APPROACH 1: Use direct MongoDB access instead of get_one
            user = self.users_collection.db[self.users_collection.collection].find_one(
                {"_id": user_id}
            )
            
            # DEBUG: Log the result
            logger.info(f"Direct MongoDB query result: {user is not None}")
            
            if not user:
                # APPROACH 2: Try with different query patterns if first approach fails
                logger.info("Trying alternative query approaches...")
                
                # Try with string conversion
                user = self.users_collection.db[self.users_collection.collection].find_one(
                    {"_id": str(user_id)}
                )
                
                if not user:
                    # Try searching by email or other unique field if available
                    # This is a fallback - you might need to adjust based on your data
                    logger.info("User still not found with string conversion")
                    
                    # List some users to debug
                    sample_users = list(self.users_collection.db[self.users_collection.collection].find(
                        {}, {"_id": 1, "name": 1, "email": 1}
                    ).limit(5))
                    logger.info(f"Sample users in collection: {sample_users}")
                    
                    raise ValueError(f"User with ID {user_id} not found in database")
            
            logger.info(f"User found: {user.get('name', 'Unknown')} ({user.get('email', 'No email')})")
            
            # Check if user already has a referral code
            if user.get("referral_code") and not force_regenerate:
                # Return existing code with current stats
                stats = ReferralStats(**user.get("referral_stats", {}))
                return ReferralCodeResponse(
                    referral_code=user["referral_code"],
                    user_id=user_id,
                    user_name=user["name"],
                    organization_id=user["organization_id"],
                    created_at=user.get("updated_at", datetime.now()),
                    stats=stats
                )
            
            # Generate new referral code
            referral_code = self.generate_referral_code(
                user["organization_id"], 
                user["name"]
            )
            
            # Initialize referral stats if not exists
            current_stats = user.get("referral_stats", {})
            referral_stats = ReferralStats(**current_stats)
            
            # Update user document with referral code - Use direct MongoDB update
            update_result = self.users_collection.db[self.users_collection.collection].update_one(
                {"_id": user_id},
                {
                    "$set": {
                        "referral_code": referral_code,
                        "referral_stats": referral_stats.model_dump(),
                        "updated_at": datetime.now()
                    }
                }
            )
            
            logger.info(f"Update result - matched: {update_result.matched_count}, modified: {update_result.modified_count}")
            logger.info(f"Created referral code {referral_code} for user {user_id}")
            
            return ReferralCodeResponse(
                referral_code=referral_code,
                user_id=user_id,
                user_name=user["name"],
                organization_id=user["organization_id"],
                created_at=datetime.now(),
                stats=referral_stats
            )
            
        except Exception as e:
            logger.error(f"Error creating referral code for user {user_id}: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            raise e
    
    async def validate_referral_code(self, referral_code: str) -> ValidateReferralResponse:
        """Validate a referral code and return referrer information"""
        try:
            if not referral_code or referral_code.strip() == "":
                return ValidateReferralResponse(
                    valid=False,
                    message="Referral code is required"
                )
            
            # Find user with this referral code - Use direct MongoDB access
            referrer = self.users_collection.db[self.users_collection.collection].find_one(
                {"referral_code": referral_code.upper()}
            )
            
            if not referrer:
                return ValidateReferralResponse(
                    valid=False,
                    message="Invalid referral code"
                )
            
            # Check if referrer is active
            if referrer.get("status") != "active":
                return ValidateReferralResponse(
                    valid=False,
                    message="Referral code is not active"
                )
            
            return ValidateReferralResponse(
                valid=True,
                referrer_name=referrer["name"],
                referrer_organization=referrer["organization_id"],
                message="Valid referral code"
            )
            
        except Exception as e:
            logger.error(f"Error validating referral code {referral_code}: {str(e)}")
            return ValidateReferralResponse(
                valid=False,
                message="Error validating referral code"
            )
    
    async def update_referral_stats(self, referrer_user_id: str, increment_field: str = "total_referrals"):
        """Update referral statistics for a referrer"""
        try:
            # Valid increment fields
            valid_fields = ["total_referrals", "verified_referrals", "active_referrals"]
            if increment_field not in valid_fields:
                raise ValueError(f"Invalid increment field: {increment_field}")
            
            # First, ensure the user has referral_stats initialized
            user = self.users_collection.db[self.users_collection.collection].find_one(
                {"_id": referrer_user_id}
            )
            
            if not user:
                raise ValueError(f"User {referrer_user_id} not found")
            
            # Initialize referral_stats if missing
            if "referral_stats" not in user:
                logger.info(f"Initializing referral_stats for user {referrer_user_id}")
                self.users_collection.db[self.users_collection.collection].update_one(
                    {"_id": referrer_user_id},
                    {
                        "$set": {
                            "referral_stats": {
                                "total_referrals": 0,
                                "verified_referrals": 0,
                                "active_referrals": 0,
                                "last_referral_date": None
                            }
                        }
                    }
                )
            
            # Update the specific stat and last referral date
            update_result = self.users_collection.db[self.users_collection.collection].update_one(
                {"_id": referrer_user_id},
                {
                    "$inc": {f"referral_stats.{increment_field}": 1},
                    "$set": {
                        "referral_stats.last_referral_date": datetime.now(),
                        "updated_at": datetime.now()
                    }
                }
            )
            
            logger.info(f"Updated {increment_field} for user {referrer_user_id} - matched: {update_result.matched_count}, modified: {update_result.modified_count}")
            
            if update_result.modified_count == 0:
                logger.warning(f"No documents were modified when updating {increment_field} for user {referrer_user_id}")
            
            return update_result
            
        except Exception as e:
            logger.error(f"Error updating referral stats for user {referrer_user_id}: {str(e)}")
            raise e

    
    async def get_referral_stats(self, user_id: str) -> Dict[str, Any]:
        """Get detailed referral statistics for a user"""
        try:
            # Get referrer details - Use direct MongoDB access
            referrer = self.users_collection.db[self.users_collection.collection].find_one(
                {"_id": user_id}
            )
            
            if not referrer:
                raise ValueError(f"User with ID {user_id} not found")
            
            # Get all users referred by this user
            referred_users = []
            if referrer.get("referral_code"):
                referred_users_cursor = self.users_collection.db[self.users_collection.collection].find(
                    {"referred_by.user_id": user_id},
                    {
                        "name": 1,
                        "email": 1,
                        "status": 1,
                        "organization_id": 1,
                        "referred_by.referred_at": 1,
                        "created_at": 1
                    }
                )
                referred_users = list(referred_users_cursor)
            
            # Calculate stats
            stats = referrer.get("referral_stats", {})
            
            return {
                "user_id": user_id,
                "user_name": referrer["name"],
                "user_email": referrer["email"],
                "organization_id": referrer["organization_id"],
                "referral_code": referrer.get("referral_code"),
                "stats": stats,
                "referred_users": referred_users,
                "total_users_referred": len(referred_users)
            }
            
        except Exception as e:
            logger.error(f"Error getting referral stats for user {user_id}: {str(e)}")
            raise e
    
    async def get_organization_referral_stats(self, organization_id: str) -> Dict[str, Any]:
        """Get referral statistics for an entire organization"""
        try:
            # Get all users with referral codes in this organization
            referrers_cursor = self.users_collection.db[self.users_collection.collection].find(
                {
                    "organization_id": organization_id,
                    "referral_code": {"$exists": True, "$ne": None}
                },
                {
                    "name": 1,
                    "email": 1,
                    "referral_code": 1,
                    "referral_stats": 1,
                    "status": 1
                }
            )
            referrers = list(referrers_cursor)
            
            # Calculate organization totals
            total_referrals = sum(
                referrer.get("referral_stats", {}).get("total_referrals", 0) 
                for referrer in referrers
            )
            total_verified = sum(
                referrer.get("referral_stats", {}).get("verified_referrals", 0) 
                for referrer in referrers
            )
            total_active = sum(
                referrer.get("referral_stats", {}).get("active_referrals", 0) 
                for referrer in referrers
            )
            
            return {
                "organization_id": organization_id,
                "total_referrers": len(referrers),
                "organization_totals": {
                    "total_referrals": total_referrals,
                    "verified_referrals": total_verified,
                    "active_referrals": total_active
                },
                "referrers": referrers
            }
            
        except Exception as e:
            logger.error(f"Error getting organization referral stats for {organization_id}: {str(e)}")
            raise e
    
    async def process_referral_signup(self, new_user_data: Dict[str, Any], referral_code: str) -> Dict[str, Any]:
        """Process a new user signup with referral code"""
        try:
            # Validate referral code
            validation = await self.validate_referral_code(referral_code)
            if not validation.valid:
                raise ValueError(validation.message)
            
            # Find referrer - Use direct MongoDB access
            referrer = self.users_collection.db[self.users_collection.collection].find_one(
                {"referral_code": referral_code.upper()}
            )
            
            if not referrer:
                raise ValueError("Referrer not found")
            
            # Add referral information to new user data
            new_user_data["referred_by"] = ReferredBy(
                user_id=referrer["_id"],
                referral_code=referral_code.upper(),
                organization_id=referrer["organization_id"],
                referred_at=datetime.now()
            ).model_dump()
            
            # Update referrer's stats (increment total_referrals)
            await self.update_referral_stats(referrer["_id"], "total_referrals")
            
            logger.info(f"Processed referral signup for new user with referral code {referral_code}")
            
            return {
                "referrer_name": referrer["name"],
                "referrer_organization": referrer["organization_id"],
                "referral_processed": True
            }
            
        except Exception as e:
            logger.error(f"Error processing referral signup with code {referral_code}: {str(e)}")
            raise e

    async def update_referral_stats_on_signup(self, referrer_user_id: str, new_user_data: dict) -> dict:
        """Update referrer stats when someone signs up with their code"""
        try:
            current_time = datetime.now()
            
            # Create referral usage record
            referral_usage = {
                "user_id": new_user_data["_id"],
                "user_email": new_user_data["email"], 
                "user_name": new_user_data["name"],
                "referred_at": current_time,
                "verified_at": None,
                "status": "pending"
            }
            
            # Update referrer's stats with proper initialization
            update_result = self.users_collection.db[self.users_collection.collection].update_one(
                {"_id": referrer_user_id},
                {
                    "$inc": {"referral_stats.total_referrals": 1},
                    "$set": {
                        "referral_stats.last_referral_date": current_time,
                        "updated_at": current_time
                    },
                    "$push": {
                        "referral_stats.referral_history": referral_usage
                    }
                }
            )
            
            # Initialize referral_stats if it doesn't exist
            if update_result.matched_count == 0:
                logger.info(f"Initializing referral_stats for user {referrer_user_id}")
                self.users_collection.db[self.users_collection.collection].update_one(
                    {"_id": referrer_user_id},
                    {
                        "$set": {
                            "referral_stats": {
                                "total_referrals": 1,
                                "verified_referrals": 0,
                                "active_referrals": 0,
                                "last_referral_date": current_time,
                                "referral_history": [referral_usage]
                            },
                            "updated_at": current_time
                        }
                    }
                )
            
            logger.info(f"Updated signup referral stats for referrer {referrer_user_id}")
            return {"success": True, "message": "Referral stats updated successfully"}
            
        except Exception as e:
            logger.error(f"Error updating signup referral stats: {str(e)}")
            raise e
    
    async def update_referral_stats_by_code(self, referral_code: str, verified_user_email: str) -> dict:
        """Update referrer stats using referral code - Robust approach"""
        try:
            logger.info(f"=== REFERRAL VERIFICATION BY CODE ===")
            logger.info(f"Referral code: {referral_code}")
            logger.info(f"Verified user email: {verified_user_email}")
            
            # Step 1: Find the referrer by referral code
            referrer = self.users_collection.db[self.users_collection.collection].find_one(
                {"referral_code": referral_code}
            )
            
            if not referrer:
                logger.error(f"Could not find referrer with code: {referral_code}")
                return {"success": False, "message": "Invalid referral code"}
            
            logger.info(f"Found referrer: {referrer.get('name')} (ID: {referrer['_id']})")
            
            # Step 2: Find the verified user by email (try multiple approaches)
            verified_user = None
            verified_user_id = None
            
            # Approach 1: Direct email lookup
            verified_user = self.users_collection.db[self.users_collection.collection].find_one(
                {"email": verified_user_email}
            )
            
            if verified_user:
                verified_user_id = verified_user["_id"]
                logger.info(f"Found verified user by email: {verified_user.get('name')} (ID: {verified_user_id})")
            else:
                logger.warning(f"Could not find verified user with email: {verified_user_email}")
                
                # Approach 2: Look for the user in referral history and use that user_id
                referral_stats = referrer.get("referral_stats", {})
                referral_history = referral_stats.get("referral_history", [])
                
                # Find the user in referral history by email
                matching_history_entry = None
                for entry in referral_history:
                    if entry.get("user_email") == verified_user_email:
                        matching_history_entry = entry
                        verified_user_id = entry.get("user_id")
                        logger.info(f"Found user in referral history: {entry.get('user_name')} (ID: {verified_user_id})")
                        break
                
                if not matching_history_entry:
                    logger.error(f"User {verified_user_email} not found in referral history either")
                    return {"success": False, "message": "User not found in referral history"}
                
                # Approach 3: Try to find the user by the ID found in history
                if verified_user_id:
                    verified_user = self.users_collection.db[self.users_collection.collection].find_one(
                        {"_id": verified_user_id}
                    )
                    if verified_user:
                        logger.info(f"Found verified user by ID from history: {verified_user.get('name')}")
                    else:
                        logger.warning(f"User with ID {verified_user_id} from history not found in users collection")
                        # We'll proceed with just the ID from history
            
            # Step 3: Update the referrer's referral_history array
            current_time = datetime.now()
            
            logger.info("=== UPDATING REFERRAL HISTORY ===")
            logger.info(f"Looking for user_id {verified_user_id} in referrer's history")
            
            # Check current referral stats to see what's in the history
            referral_stats = referrer.get("referral_stats", {})
            referral_history = referral_stats.get("referral_history", [])
            logger.info(f"Current history has {len(referral_history)} entries")
            
            for i, entry in enumerate(referral_history):
                logger.info(f"History[{i}]: user_id={entry.get('user_id')}, email={entry.get('user_email')}, status={entry.get('status')}")
            
            # Update the specific entry in referral_history
            update_result = self.users_collection.db[self.users_collection.collection].update_one(
                {
                    "_id": referrer["_id"],
                    "referral_stats.referral_history.user_id": verified_user_id
                },
                {
                    "$set": {
                        "referral_stats.referral_history.$.verified_at": current_time,
                        "referral_stats.referral_history.$.status": "verified",
                        "updated_at": current_time
                    },
                    "$inc": {"referral_stats.verified_referrals": 1}
                }
            )
            
            logger.info(f"History update result: matched={update_result.matched_count}, modified={update_result.modified_count}")
            
            if update_result.matched_count == 0:
                logger.warning(f"Could not find user {verified_user_id} in referrer's history")
                
                # Debug: Let's see what user_ids are actually in the history vs what we're searching for
                for entry in referral_history:
                    entry_user_id = entry.get('user_id')
                    logger.info(f"Comparing: '{entry_user_id}' vs '{verified_user_id}'")
                    logger.info(f"Types: {type(entry_user_id)} vs {type(verified_user_id)}")
                    logger.info(f"Equal: {entry_user_id == verified_user_id}")
                
                # Alternative approach: Update by email in history (more reliable)
                logger.info("=== TRYING UPDATE BY EMAIL IN HISTORY ===")
                email_update_result = self.users_collection.db[self.users_collection.collection].update_one(
                    {
                        "_id": referrer["_id"],
                        "referral_stats.referral_history.user_email": verified_user_email
                    },
                    {
                        "$set": {
                            "referral_stats.referral_history.$.verified_at": current_time,
                            "referral_stats.referral_history.$.status": "verified",
                            "updated_at": current_time
                        },
                        "$inc": {"referral_stats.verified_referrals": 1}
                    }
                )
                
                logger.info(f"Email-based update result: matched={email_update_result.matched_count}, modified={email_update_result.modified_count}")
                
                if email_update_result.modified_count > 0:
                    logger.info(f"Successfully updated referral stats using email match")
                    return {"success": True, "message": "Referral verification stats updated successfully (by email)"}
                
                # Final fallback: Just increment the verified counter
                logger.info("=== ATTEMPTING FINAL FALLBACK UPDATE ===")
                fallback_update = self.users_collection.db[self.users_collection.collection].update_one(
                    {"_id": referrer["_id"]},
                    {
                        "$inc": {"referral_stats.verified_referrals": 1},
                        "$set": {"updated_at": current_time}
                    }
                )
                
                if fallback_update.modified_count > 0:
                    logger.info(f"Fallback: Updated verified_referrals for referrer {referrer['_id']}")
                    return {"success": True, "message": "Referral verification stats updated (fallback - counter only)"}
                else:
                    logger.error(f"Failed to update referral stats for referrer {referrer['_id']}")
                    return {"success": False, "message": "Failed to update referral stats"}
            
            logger.info(f"=== SUCCESS ===")
            logger.info(f"Updated verification stats for referrer {referrer.get('name')}")
            logger.info(f"User {verified_user_email} referral status changed to 'verified'")
            
            return {"success": True, "message": "Referral verification stats updated successfully"}
            
        except Exception as e:
            logger.error(f"Error in referral verification by code: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise e
    
    
    async def update_referral_stats_on_activation(self, user_id: str) -> dict:
        """Update referrer stats when referred user becomes active"""
        try:
            # Get the activated user to find their referrer
            activated_user = self.users_collection.db[self.users_collection.collection].find_one(
                {"_id": user_id}
            )
            
            if not activated_user or not activated_user.get("referred_by"):
                logger.info(f"User {user_id} was not referred or not found")
                return {"message": "No referral to update"}
            
            referrer_id = activated_user["referred_by"]["user_id"]
            current_time = datetime.now()
            
            # Update the specific referral record in the referrer's history
            update_result = self.users_collection.db[self.users_collection.collection].update_one(
                {
                    "_id": referrer_id,
                    "referral_stats.referral_history.user_id": user_id
                },
                {
                    "$set": {
                        "referral_stats.referral_history.$.status": "active",
                        "updated_at": current_time
                    },
                    "$inc": {"referral_stats.active_referrals": 1}
                }
            )
            
            # Fallback if history update fails
            if update_result.matched_count == 0:
                logger.warning(f"Could not find referral history record for user {user_id} in referrer {referrer_id}")
                
                fallback_update = self.users_collection.db[self.users_collection.collection].update_one(
                    {"_id": referrer_id},
                    {
                        "$inc": {"referral_stats.active_referrals": 1},
                        "$set": {"updated_at": current_time}
                    }
                )
                
                if fallback_update.modified_count > 0:
                    logger.info(f"Fallback: Updated active_referrals for referrer {referrer_id}")
                    return {"success": True, "message": "Referral activation stats updated (fallback)"}
            
            logger.info(f"Updated activation referral stats for referrer {referrer_id}, referred user {user_id}")
            return {"success": True, "message": "Referral activation stats updated successfully"}
            
        except Exception as e:
            logger.error(f"Error updating activation referral stats: {str(e)}")
            raise e



    
    async def get_detailed_referral_stats(self, user_id: str) -> dict:
        """Get detailed referral statistics including individual usage records"""
        try:
            referrer = self.users_collection.db[self.users_collection.collection].find_one(
                {"_id": user_id}
            )
            
            if not referrer:
                raise ValueError(f"User with ID {user_id} not found")
            
            referral_stats = referrer.get("referral_stats", {})
            referral_history = referral_stats.get("referral_history", [])
            
            # Calculate additional metrics
            pending_referrals = len([r for r in referral_history if r.get("status") == "pending"])
            verified_referrals = len([r for r in referral_history if r.get("status") == "verified"])
            
            # Get recent referrals (last 10)
            recent_referrals = sorted(
                referral_history, 
                key=lambda x: x.get("referred_at", datetime.min), 
                reverse=True
            )[:10]
            
            return {
                "user_id": user_id,
                "user_name": referrer["name"],
                "user_email": referrer["email"],
                "organization_id": referrer["organization_id"],
                "referral_code": referrer.get("referral_code"),
                "stats": {
                    "total_referrals": referral_stats.get("total_referrals", 0),
                    "verified_referrals": referral_stats.get("verified_referrals", 0),
                    "pending_referrals": pending_referrals,
                    "last_referral_date": referral_stats.get("last_referral_date"),
                    "conversion_rate": round(
                        (verified_referrals / max(1, referral_stats.get("total_referrals", 1))) * 100, 
                        2
                    )
                },
                "referral_history": recent_referrals,
                "total_history_count": len(referral_history)
            }
            
        except Exception as e:
            logger.error(f"Error getting detailed referral stats: {str(e)}")
            raise e

        
def verify_google_config():
    required_settings = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'GOOGLE_REDIRECT_URI'
    ]
    
    missing = [setting for setting in required_settings 
              if not getattr(settings, setting, None)]
    
    if missing:
        logger.error(f"Missing required Google OAuth settings: {', '.join(missing)}")
        raise ValueError(f"Missing required Google OAuth settings: {', '.join(missing)}")

# Call this when your application starts
verify_google_config()

def decode_tenant_id(tenant_id: str):
    try:
        if '%' in tenant_id:
            from urllib.parse import unquote
            tenant_id = unquote(tenant_id)
        return tenant_id
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to decode tenant ID: {str(e)}")

async def get_tenant_credentials(tenant_id: str) -> dict:
    """Helper function to get the appropriate credentials based on tenant_id"""
    if  tenant_id == default_tenant_id:
        return {
            'user_pool_id': settings.AWS_COGNITO_USER_POOL_ID,
            'client_id': settings.AWS_COGNITO_APP_CLIENT_ID
        }
    else:
        print("External Tenant cred", tenant_id)
        return await tenant_service.get_tenant_cred(tenant_id)

_SHOW_NAME = "auth"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

async def add_user_node_db(username, tenant_id):
    try:
        db = get_node_db(tenant_id)
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=username
        )
        
        # Add user to node database
        await db.upsert_user_db(user_details.get('Username'), format_response_user(user_details))
        
        logger.info(f"Successfully added user {username} to node database for tenant {tenant_id}")
        return True
    except Exception as e:
        logger.error(f"Error adding user to node database: {str(e)}")
        return False

@router.get("/get_organization_name")
async def get_organization_name(tenant_id: str):
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        print("Decrypted Tenant ID", tenant_id)
        if tenant_id == settings.KAVIA_SUPER_TENANT_ID:
            return {
                "name": "Super Admin",
                "id": settings.KAVIA_SUPER_TENANT_ID,
                "created_at": None
            }
        organization = Organization(**(await Organization.get(tenant_id)))
        return {
            "name": organization.name,
            "id": organization.id,
            "created_at": organization.created_at
        }
    except Exception as e:
        logger.error(f"Error getting organization name: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get_organization_name_by_id")
async def get_organization_name_by_id(tenant_id: str):
    if tenant_id == settings.KAVIA_SUPER_TENANT_ID:
        return {
            "name": "Super Admin",
            "id": settings.KAVIA_SUPER_TENANT_ID,
            "created_at": None,
            "login_id": encrypt_tenant_id(settings.KAVIA_SUPER_TENANT_ID)
        }
    organization = Organization(**(await Organization.get(tenant_id)))
    return {
        "name": organization.name,
        "id": organization.id,
        "created_at": organization.created_at,
        "login_id": encrypt_tenant_id(organization.id)
    }

@router.post("/login", summary="Login for existing users")
async def login(user: CognitoUser):
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # First, update the app client to enable USER_PASSWORD_AUTH
        cognito_pool = CognitoUserPoolCreator()
        cognito_pool.update_app_client_auth_flows(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        try:
            # Now attempt login
            response = client.initiate_auth(
                ClientId=creds['client_id'],
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': user.email,
                    'PASSWORD': user.password
                }
            )
        except client.exceptions.NotAuthorizedException:
            raise HTTPException(status_code=401, detail="Invalid username or password")
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=401, detail="User not found")

        # Check token expiry times
        auth_result = response['AuthenticationResult']
        access_token_exp = auth_result.get('ExpiresIn', 3600)
        
        # If token expiry is less than desired (e.g., 24 hours = 86400 seconds)
        if access_token_exp < 86400:  # 24 hours in seconds
            # Update token validity
            cognito_pool.update_app_client_token_validity(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id'],
                access_token_validity=24,  # 24 hours
                id_token_validity=24,      # 24 hours
                refresh_token_validity=30   # 30 days
            )
            
            try:
                # Perform login again to get new tokens with updated expiry
                response = client.initiate_auth(
                    ClientId=creds['client_id'],
                    AuthFlow='USER_PASSWORD_AUTH',
                    AuthParameters={
                        'USERNAME': user.email,
                        'PASSWORD': user.password
                    }
                )
            except client.exceptions.NotAuthorizedException:
                raise HTTPException(status_code=401, detail="Invalid username or password")
            except client.exceptions.UserNotFoundException:
                raise HTTPException(status_code=401, detail="User not found")

        # Format response
        id_token = response['AuthenticationResult'].pop('IdToken')
        refresh_token = response['AuthenticationResult'].pop('RefreshToken')
        
        # Get user details from Cognito to extract custom attributes
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=user.email
        )
        
        # Extract custom attributes from user details
        is_admin = False
        is_free_user = False
        _tenant_id = tenant_id
        tenant_id = ''
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                tenant_id = attr['Value']
        if tenant_id == '':
            tenant_id = _tenant_id
        response_to_return = {
            "message": "Login successful",
            "id_token": id_token,
            "refresh_token": refresh_token,
            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
            "is_admin": is_admin,
            "is_free_user": is_free_user,
            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
            **response['AuthenticationResult']
        }

        
        return response_to_return
    
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

def check_signup_permission(userpool_id, client_id, client=None):
    """Check if self-signup is enabled"""
    try:
        if client is None:
            client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        response = client.describe_user_pool(UserPoolId=userpool_id)
        admin_config = response['UserPool'].get('AdminCreateUserConfig', {})
        allow_admin_only = admin_config.get('AllowAdminCreateUserOnly', False)
        
        return not allow_admin_only
        
    except ClientError:
        return False

def enable_signup_permission(userpool_id, client_id, client=None):
    """Enable self-signup and automatic verification"""
    try:
        if client is None:
            client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        client.update_user_pool(
            UserPoolId=userpool_id,
            AdminCreateUserConfig={'AllowAdminCreateUserOnly': False},
            AutoVerifiedAttributes=['email'],
            EmailVerificationMessage='Your verification code is {####}',
            EmailVerificationSubject='Verification Code'
        )
        
        return True
        
    except ClientError:
        return False
    




referral_service = ReferralService()
promotional_counter_service = PromotionalCounterService()

@router.post("/signup", summary="Sign up a new user with referral support")
async def signup(user: SignUpUser):
    try:
        random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        org_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        
        if tenant_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(status_code=400, detail="Signup is not allowed for this organization")
        
        logger.info(f"Signing up user {user.email} for organization {tenant_id}")
        
        # REFERRAL CODE PROCESSING
        referral_service = ReferralService()
        referral_info = None
        referrer_data = None
        has_valid_referral = False
        
        if user.referral_code:
            try:
                # Validate referral code
                validation = await referral_service.validate_referral_code(user.referral_code)
                if not validation.valid:
                    logger.warning(f"Invalid referral code: {user.referral_code}")
                    raise HTTPException(status_code=400, detail=f"Invalid referral code: {validation.message}")
                
                # Get referrer data using direct MongoDB access
                referrer_data = referral_service.users_collection.db[referral_service.users_collection.collection].find_one(
                    {"referral_code": user.referral_code.upper()}
                )
                
                if referrer_data:
                    has_valid_referral = True
                    referral_info = {
                        "referrer_name": referrer_data["name"],
                        "referrer_organization": referrer_data["organization_id"],
                        "referral_code": user.referral_code.upper()
                    }
                    logger.info(f"Valid referral code {user.referral_code} from {referrer_data['name']}")
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error processing referral code: {str(e)}")
                raise HTTPException(status_code=400, detail="Error processing referral code")
        
        # COGNITO USER CREATION (RESTORED ORIGINAL CODE)
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

        # Check and enable signup permission if needed
        if not check_signup_permission(creds['user_pool_id'], creds['client_id'], client):
            logger.info(f"Self-signup is disabled for user pool {creds['user_pool_id']}, enabling it...")
            if not enable_signup_permission(creds['user_pool_id'], creds['client_id'], client):
                logger.error(f"Failed to enable self-signup for user pool {creds['user_pool_id']}")
                raise HTTPException(status_code=500, detail="Failed to enable signup permission")
            logger.info(f"Self-signup enabled successfully for user pool {creds['user_pool_id']}")

        # Ensure SES email configuration is applied
        cognito_pool_creator = CognitoUserPoolCreator()
        ses_configured = cognito_pool_creator.ensure_ses_email_config(creds['user_pool_id'])
        if ses_configured:
            logger.info(f"SES email configuration ensured for user pool {creds['user_pool_id']}")
        else:
            logger.warning(f"Could not configure SES for user pool {creds['user_pool_id']}")

        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )

        # Update Cognito custom attributes
        user_manager.add_free_tier_to_pool()

        # Check for promotional credits BEFORE creating Cognito user
        gets_promotional_credits = False
        if not has_valid_referral:
            gets_promotional_credits = await promotional_counter_service.check_and_increment_counter()

        # Set free_user based on referral OR promotional status
        is_free_user = 'false' if (has_valid_referral or gets_promotional_credits) else 'true'

        # Sign up the user in Cognito
        response = client.sign_up(
            ClientId=creds['client_id'],
            Username=user.email,
            Password=user.password,
            UserAttributes=[
                {'Name': 'email', 'Value': user.email},
                {'Name': 'custom:Name', 'Value': user.name},
                {'Name': 'custom:Designation', 'Value': user.designation},
                {'Name': 'custom:Department', 'Value': user.department},
                {'Name': 'custom:tenant_id', 'Value': org_id},
                {'Name': 'custom:is_admin', 'Value': 'true'},
                {'Name': 'custom:free_user', 'Value': is_free_user}
            ],
            ValidationData=[
                {'Name': 'email', 'Value': user.email},
            ],
        )
        
        # CREATE USER IN MONGODB (ENHANCED WITH REFERRAL SUPPORT)
        user_id = response['UserSub']
        designation = user.designation
        opentopublic = get_opentopublic()
        current_time = datetime.utcnow().isoformat() + "+00:00"
        
        # Create user data object for B2C tenant
        user_data = {
            "_id": user_id,
            "email": user.email,
            "name": user.name,
            "contact_number": "",
            "department": user.department,
            "organization_id": tenant_id,
            "group_ids": [],
            "is_admin": True,
            "designation": designation,
            "status": "active" if opentopublic or (user.referral_code and referrer_data) else "inactive",
            "free_user": not has_valid_referral,
            "has_accepted_terms": user.has_accepted_terms,
            "accepted_terms_at": datetime.now() if user.has_accepted_terms else None
        }
        
        # ADD REFERRAL INFORMATION IF PROVIDED
        if referrer_data:
            user_data["referred_by"] = ReferredBy(
                user_id=referrer_data["_id"],
                referral_code=user.referral_code.upper(),
                organization_id=referrer_data["organization_id"],
                referred_at=datetime.now()
            ).model_dump()
        
        # GET MONGODB COLLECTIONS
        active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        llm_costs_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")

        # HANDLE SUBSCRIPTION BASED ON REFERRAL STATUS
        if has_valid_referral:
            # VIP ACCESS SUBSCRIPTION FOR REFERRAL USERS
            referral_subscription_data = {
                "created_at": current_time,
                "updated_at": current_time,
                "price_id": "price_1RqqYECI2zbViAE2qI3YLJ96",  # VIP Access plan for referral users
                "status": 2,  # Active status
                "tenant_id": "b2c",
                "user_id": user_id
            }
            
            # Insert premium subscription record
            await active_subscription.insert(referral_subscription_data, active_subscription.db)
            logger.info(f"Premium subscription created for referred user {user_id}")
            
            # UPDATE LLM COSTS FOR REFERRAL USER (B2C organization)
            llm_costs_doc = await llm_costs_collection.get_one(
                {"organization_id": "b2c"}, 
                llm_costs_collection.db
            )
            
            # Initialize the new user data for llm_costs
            new_user_data = {
                "user_id": user_id,
                "type": "llm_interaction",
                "cost": "0.00",
                "current_plan": "price_1RqqYECI2zbViAE2qI3YLJ96"  # VIP Access plan
            }
            
            if llm_costs_doc:
                # Update existing document by appending the new user to the users array
                if 'users' not in llm_costs_doc:
                    llm_costs_doc['users'] = []
                llm_costs_doc['users'].append(new_user_data)
                
                await llm_costs_collection.update_one(
                    {"_id": llm_costs_doc["_id"]},
                    llm_costs_doc,
                    upsert=True,
                    db=llm_costs_collection.db
                )
                logger.info(f"Updated llm_costs for referred user {user_id}")
            else:
                # Create new document if it doesn't exist
                new_llm_costs_doc = {
                    "organization_id": "b2c",
                    "organization_cost": "$0.000",
                    "organization_name": "B2C Organization",
                    "users": [new_user_data],
                    "cost": "0.00",
                    "current_plan": "price_1RqqYECI2zbViAE2qI3YLJ96",  # VIP Access plan
                }
                await llm_costs_collection.insert(new_llm_costs_doc, llm_costs_collection.db)
                logger.info(f"Created new llm_costs document for referred user {user_id}")
        else:
            # HANDLE NON-REFERRAL USERS (PROMOTIONAL OR FREE)
            if gets_promotional_credits:
                # PROMOTIONAL $20 SUBSCRIPTION FOR FIRST 1000 USERS
                subscription_data = {
                    "tenant_id": "b2c",
                    "price_id": promotional_counter_service.PROMOTIONAL_PRICE_ID,  # price_1RWBTrCI2zbViAE2WZFApvc8
                    "credits": promotional_counter_service.PROMOTIONAL_CREDITS,  # 220,000 credits
                    "created_at": current_time
                }

                # Mark user as promotional (not free_user)
                user_data["free_user"] = False
                user_data["promotional_plan"] = "early_users_promotion"

                logger.info(f"User {user_id} received promotional $20 credits (220,000 credits)")

                # Insert promotional subscription data
                await active_subscription.update_one_data(
                    {"user_id": user_id},
                    {"$set": subscription_data},
                    upsert=True
                )

                # PROMOTIONAL LLM COSTS
                llm_cost_data = {
                    "user_id": user_id,
                    "type": "llm_interaction",
                    "user_cost": "$0",
                    "projects": [],
                    "cost": "$0",
                    "plans_history": [],
                    "current_plan": promotional_counter_service.PROMOTIONAL_PRICE_ID,
                    "_task_tracking": {},
                }
            else:
                # DEFAULT FREE SUBSCRIPTION FOR NON-REFERRAL USERS (after 1000 limit)
                subscription_data = {
                    "tenant_id": "b2c",
                    "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
                    "credits": 50000,
                    "created_at": current_time
                }

                # Insert or update the subscription data
                await active_subscription.update_one_data(
                    {"user_id": user_id},
                    {"$set": subscription_data},
                    upsert=True
                )

                # DEFAULT LLM COSTS FOR NON-REFERRAL USERS
                llm_cost_data = {
                    "user_id": user_id,
                    "type": "llm_interaction",
                    "user_cost": "$0",
                    "projects": [],
                    "cost": "$0",
                    "plans_history": [],
                    "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB",
                    "_task_tracking": {},
                }
            await llm_costs_collection.update_one_data(
                {"organization_id": "b2c"},
                {"$push": {"users": llm_cost_data}},
                upsert=True
            )
            logger.info(f"Default subscription and llm_costs created for user {user_id}")
        
        # Save user to database for B2C tenant
        try:
            await User.create(user_data)
            logger.info(f"User {user.email} created successfully with ID {user_id}")
        except Exception as e:
            if "duplicate key error" in str(e):
                logger.warning(f"User {user_id} already exists in B2C tenant, skipping creation")
            else:
                raise e
        
        # Update referrer stats AFTER user is successfully created
        if referrer_data:
            try:
                await referral_service.update_referral_stats_on_signup(
                    referrer_data["_id"], 
                    user_data
                )
                logger.info(f"Successfully updated referral stats for referrer {referrer_data['_id']}")
                
            except Exception as stats_error:
                logger.error(f"Error updating referrer stats: {stats_error}")
                # Don't fail signup if stats update fails, but log the error
        
        logger.info(f"Creating default organization with ID: {org_id} for user {user.email}")
        
        # BUILD RESPONSE WITH REFERRAL AND PROMOTIONAL INFO
        if has_valid_referral:
            subscription_type = "premium"
        elif gets_promotional_credits:
            subscription_type = "promotional"
        else:
            subscription_type = "free"

        response_data = {
            "message": "User created successfully. Verification code sent.",
            "user_sub": user_id,
            "tenant_id": tenant_id,
            "organization_id": org_id,
            "subscription_type": subscription_type
        }

        # Add referral info to response
        if referral_info:
            response_data["referral_info"] = referral_info
            response_data["message"] = f"User created successfully with referral from {referral_info['referrer_name']}. Premium subscription activated. Verification code sent."
        elif gets_promotional_credits:
            response_data["promotional_info"] = {
                "credits": promotional_counter_service.PROMOTIONAL_CREDITS,
                "plan_name": "Early Users",
                "plan_value": "$20 worth",
                "product_id": promotional_counter_service.PROMOTIONAL_PRODUCT_ID,
                "message": "Congratulations! You received Early Users promotional credits as one of our first 1000 users!"
            }
            response_data["message"] = "User created successfully. Early Users promotional credits activated! Verification code sent."
        
        return response_data
        
    except ClientError as e:
        # Handle Cognito errors
        if e.__class__.__name__ == 'UsernameExistsException':
            logger.warning(f"Signup failed: Username {user.email} already exists")
            raise HTTPException(status_code=400, detail="Username (email) already exists")
        elif e.__class__.__name__ == 'InvalidPasswordException':
            logger.warning(f"Signup failed: Invalid password for user {user.email}")
            raise HTTPException(status_code=400, detail="Invalid password. Password must meet Cognito requirements.")
        elif e.__class__.__name__ == 'ParamValidationError':
            logger.error(f"Signup failed: Parameter validation error for user {user.email}: {e}")
            raise HTTPException(status_code=400, detail=f"Parameter validation error: {e}")
        else:
            logger.error(f"Signup failed: Cognito API error for user {user.email}: {e}")
            raise HTTPException(status_code=500, detail=f"Cognito API error: {e}")
    except Exception as e:
        logger.error(f"Signup failed: Internal server error for user {user.email}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

# @router.post("/signup", summary="Sign up a new user with referral support")
# async def signup(user: SignUpUser):
#     try:
#         random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
#         org_id = settings.KAVIA_B2C_CLIENT_ID
#         tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        
#         if tenant_id != settings.KAVIA_B2C_CLIENT_ID:
#             raise HTTPException(status_code=400, detail="Signup is not allowed for this organization")
        
#         logger.info(f"Signing up user {user.email} for organization {tenant_id}")
        
#         # REFERRAL CODE PROCESSING
#         referral_service = ReferralService()
#         referral_info = None
#         referrer_data = None
        
#         if user.referral_code:
#             try:
#                 # Validate referral code
#                 validation = await referral_service.validate_referral_code(user.referral_code)
#                 if not validation.valid:
#                     logger.warning(f"Invalid referral code: {user.referral_code}")
#                     raise HTTPException(status_code=400, detail=f"Invalid referral code: {validation.message}")
                
#                 # Get referrer data using direct MongoDB access
#                 referrer_data = referral_service.users_collection.db[referral_service.users_collection.collection].find_one(
#                     {"referral_code": user.referral_code.upper()}
#                 )
                
#                 if referrer_data:
#                     referral_info = {
#                         "referrer_name": referrer_data["name"],
#                         "referrer_organization": referrer_data["organization_id"],
#                         "referral_code": user.referral_code.upper()
#                     }
#                     logger.info(f"Valid referral code {user.referral_code} from {referrer_data['name']}")
                
#             except HTTPException:
#                 raise
#             except Exception as e:
#                 logger.error(f"Error processing referral code: {str(e)}")
#                 raise HTTPException(status_code=400, detail="Error processing referral code")
        
#         # COGNITO USER CREATION (RESTORED ORIGINAL CODE)
#         creds = await get_tenant_credentials(tenant_id)
#         client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
#                             aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#                             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

#         # Check and enable signup permission if needed
#         if not check_signup_permission(creds['user_pool_id'], creds['client_id'], client):
#             logger.info(f"Self-signup is disabled for user pool {creds['user_pool_id']}, enabling it...")
#             if not enable_signup_permission(creds['user_pool_id'], creds['client_id'], client):
#                 logger.error(f"Failed to enable self-signup for user pool {creds['user_pool_id']}")
#                 raise HTTPException(status_code=500, detail="Failed to enable signup permission")
#             logger.info(f"Self-signup enabled successfully for user pool {creds['user_pool_id']}")

#         user_manager = CognitoUserManager(
#             user_pool_id=creds['user_pool_id'],
#             client_id=creds['client_id']
#         )

#         # Update Cognito custom attributes
#         user_manager.add_free_tier_to_pool()

#         # Sign up the user in Cognito
#         response = client.sign_up(
#             ClientId=creds['client_id'],
#             Username=user.email,
#             Password=user.password,
#             UserAttributes=[
#                 {'Name': 'email', 'Value': user.email},
#                 {'Name': 'custom:Name', 'Value': user.name},
#                 {'Name': 'custom:Designation', 'Value': user.designation},
#                 {'Name': 'custom:Department', 'Value': user.department},
#                 {'Name': 'custom:tenant_id', 'Value': org_id},
#                 {'Name': 'custom:is_admin', 'Value': 'true'},
#                 {'Name': 'custom:free_user', 'Value': 'true'}
#             ],
#             ValidationData=[
#                 {'Name': 'email', 'Value': user.email},
#             ],
#         )
        
#         # CREATE USER IN MONGODB (ENHANCED WITH REFERRAL SUPPORT)
#         user_id = response['UserSub']
#         designation = user.designation
#         opentopublic = get_opentopublic()
        
#         # Create user data object for B2C tenant
#         user_data = {
#             "_id": user_id,
#             "email": user.email,
#             "name": user.name,
#             "contact_number": "",
#             "department": user.department,
#             "organization_id": tenant_id,
#             "group_ids": [],
#             "is_admin": True,
#             "designation": designation,
#             "status": "active" if opentopublic else "inactive",
#             "free_user": True,
#             "has_accepted_terms": user.has_accepted_terms,
#             "accepted_terms_at": datetime.now() if user.has_accepted_terms else None
#         }
        
#         # ADD REFERRAL INFORMATION IF PROVIDED
#         if referrer_data:
#             user_data["referred_by"] = ReferredBy(
#                 user_id=referrer_data["_id"],
#                 referral_code=user.referral_code.upper(),
#                 organization_id=referrer_data["organization_id"],
#                 referred_at=datetime.now()
#             ).model_dump()
        
#         #  CREATE SUBSCRIPTION DATA (RESTORED)
#         active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
#         subscription_data = {
#             "tenant_id": "b2c",
#             "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
#             "credits": 50000,
#             "created_at": datetime.utcnow().isoformat()
#         }

#         # Insert or update the subscription data
#         await active_subscription.update_one_data(
#             {"user_id": user_id},
#             {"$set": subscription_data},
#             upsert=True
#         )
        
#         llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
#         llm_cost_data = {
#             "user_id": user_id,
#             "type": "llm_interaction",
#             "user_cost": "$0",
#             "projects": [], 
#             "cost": "$0",
#             "plans_history": [],  
#             "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB",  
#             "_task_tracking": {}, 
#         }
#         await llm_cost_collection.update_one_data(
#             {"organization_id": "b2c"},
#             {"$push": {"users": llm_cost_data}},
#             upsert=True
#         )
        
#         # Save user to database for B2C tenant
#         try:
#             await User.create(user_data)
#             logger.info(f"User {user.email} created successfully with ID {user_id}")
#         except Exception as e:
#             if "duplicate key error" in str(e):
#                 logger.warning(f"User {user_id} already exists in B2C tenant, skipping creation")
#             else:
#                 raise e
        
#         # Update referrer stats AFTER user is successfully created
#         if referrer_data:
#             try:
#                 await referral_service.update_referral_stats_on_signup(
#                     referrer_data["_id"], 
#                     user_data
#                 )
#                 logger.info(f"Successfully updated referral stats for referrer {referrer_data['_id']}")
                
#             except Exception as stats_error:
#                 logger.error(f"Error updating referrer stats: {stats_error}")
#                 # Don't fail signup if stats update fails, but log the error
        
#         logger.info(f"Creating default organization with ID: {org_id} for user {user.email}")
        
     
#         logger.info(f"User {user.email} created successfully with ID {user_id} and organization {org_id}")
        
#         # BUILD RESPONSE WITH REFERRAL INFO
#         response_data = {
#             "message": "User created successfully. Verification code sent.", 
#             "user_sub": user_id,
#             "tenant_id": tenant_id,
#             "organization_id": org_id
#         }
        
#         # Add referral info to response
#         if referral_info:
#             response_data["referral_info"] = referral_info
#             response_data["message"] = f"User created successfully with referral from {referral_info['referrer_name']}. Verification code sent."
        
#         return response_data
        
#     except ClientError as e:
#         # Handle Cognito errors
#         if e.__class__.__name__ == 'UsernameExistsException':
#             logger.warning(f"Signup failed: Username {user.email} already exists")
#             raise HTTPException(status_code=400, detail="Username (email) already exists")
#         elif e.__class__.__name__ == 'InvalidPasswordException':
#             logger.warning(f"Signup failed: Invalid password for user {user.email}")
#             raise HTTPException(status_code=400, detail="Invalid password. Password must meet Cognito requirements.")
#         elif e.__class__.__name__ == 'ParamValidationError':
#             logger.error(f"Signup failed: Parameter validation error for user {user.email}: {e}")
#             raise HTTPException(status_code=400, detail=f"Parameter validation error: {e}")
#         else:
#             logger.error(f"Signup failed: Cognito API error for user {user.email}: {e}")
#             raise HTTPException(status_code=500, detail=f"Cognito API error: {e}")
#     except Exception as e:
#         logger.error(f"Signup failed: Internal server error for user {user.email}: {e}")
#         raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
    
@router.post("/change_password")
async def change_password(username: str, new_password: str, tenant_id: str = Query(..., description="Tenant ID")):
    client = None
    try:
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        response = client.admin_set_user_password(
            UserPoolId=creds['user_pool_id'],
            Username=username,
            Password=new_password,
            Permanent=True
        )

        print("Password changed successfully.")
    except client.exceptions.UserNotFoundException:
        print("User not found.")
    except client.exceptions.InvalidPasswordException:
        print("Invalid password, please check password policy.")
    except Exception as e:
        print("An error occurred:", e)

@router.post("/confirm_signup", summary="Confirm user signup with verification code")
async def confirm_signup(
    request: Request,
    username: str = Query(..., description="The user's email address (username)"),
    confirmation_code: str = Query(..., description="The confirmation code sent to the user's email"),
    tenant_id: str = Query(None, description="Tenant ID (optional - will be retrieved from user attributes if not provided)"),
    referral_code: str = Query(None, description="Referral code for referral stats update"),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    client = None
    try:
        # Handle tenant_id properly
        if tenant_id:
            try:
                tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
            except Exception as e:
                logger.warning(f"Error decoding tenant_id: {e}, using as-is")
        
        # If tenant_id is not provided or decoding failed, use default tenant
        if not tenant_id:
            tenant_id = default_tenant_id
            logger.info(f"No tenant_id provided, using default tenant: {tenant_id}")
        
        logger.info(f"Confirming signup for user {username} in organization {tenant_id}")
        
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Confirm signup in Cognito
        client.confirm_sign_up(
            ClientId=creds['client_id'],
            Username=username,
            ConfirmationCode=confirmation_code,
        )
        
        # Get user details from Cognito
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=username
        )
        user_id = user_details.get('Username')
        
        # Get the organization ID and user attributes
        org_id = None
        user_name = ""
        user_email = username
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
                logger.info(f"Retrieved organization_id from user attributes: {org_id}")
            elif attr['Name'] == 'custom:Name':
                user_name = attr['Value']
            elif attr['Name'] == 'email':
                user_email = attr['Value']
        
        # IMPORTANT: Initialize Neo4j database for the organization if it doesn't exist
        if org_id:
            try:
                # Import here to avoid circular imports
                from app.classes.NodeDB import NodeDB
                # Use the already imported settings from the module level
                
                # Prepend environment prefix to database name if needed
                env_prefix = getattr(settings, 'ENV_PREFIX', '')
                db_name = f"{env_prefix}{org_id}" if env_prefix else org_id
                # Replace hyphens with underscores for Neo4j compatibility
                db_name = db_name.replace('-', '_')
                logger.info(f"Initializing Neo4j database for organization: {db_name}")
                
                # Initialize NodeDB which will create the database if it doesn't exist
                node_db = NodeDB(
                    uri=settings.NEO4J_URI,
                    user=settings.NEO4J_USER,
                    password=settings.NEO4J_PASSWORD,
                    database=db_name
                )
                
                logger.info(f"Successfully initialized Neo4j database for organization: {db_name}")
            except Exception as e:
                logger.error(f"Error initializing Neo4j database for organization {org_id}: {e}")
                # Continue execution even if database initialization fails
        
        # Add user to node database for the current tenant
        background_tasks.add_task(add_user_node_db, user_id, tenant_id)
        
        # IMPORTANT: Use the correct collection name from organization_models.py
        # User.get_collection() returns the correct collection
        users_collection = User.get_collection()
        
        # Update user status in B2C tenant
        try:
            # Update by email
            '''
            Will be replace this operation by admin
            '''
            # result = users_collection.update_one(
            #     {"email": username, "organization_id": tenant_id},
            #     {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
            # )
            
            # if result.modified_count > 0:
            #     logger.info(f"User {username} status updated to active in B2C tenant")
            # else:
            #     # Try by user_id
            #     result = users_collection.update_one(
            #         {"_id": user_id, "organization_id": tenant_id},
            #         {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
            #     )
            #     if result.modified_count > 0:
            #         logger.info(f"User {user_id} status updated to active in B2C tenant (found by ID)")
            #     else:
            #         logger.warning(f"Could not find user in B2C tenant to update status")
        except Exception as e:
            logger.error(f"Error updating user status in B2C tenant: {e}")
        
        # Now handle the user's personal organization if we found one in attributes
        if org_id:
            try:
                logger.info(f"Activating user in their personal organization: {org_id}")
                
                # Add user to node database for their organization
                background_tasks.add_task(add_user_node_db, user_id, org_id)
                
                # DIRECT DB UPDATE: Update all users with this email in the organization
                # First try by email
                result = users_collection.update_many(
                    {"email": username, "organization_id": org_id},
                    {"$set": {"updated_at": datetime.utcnow()}}
                )
                
                if result.modified_count > 0:
                    logger.info(f"Updated {result.modified_count} users with email {username} in organization {org_id}")
                else:
                    # Try by cognito_id
                    result = users_collection.update_many(
                        {"cognito_id": user_id, "organization_id": org_id},
                        {"$set": {"updated_at": datetime.utcnow()}}
                    )
                    
                    if result.modified_count > 0:
                        logger.info(f"Updated {result.modified_count} users with cognito_id {user_id} in organization {org_id}")
                    else:
                        # Try with combined ID format
                        org_user_id = f"{user_id}-{org_id}"
                        result = users_collection.update_one(
                            {"_id": org_user_id},
                            {"$set": {"updated_at": datetime.utcnow()}}
                        )
                        
                        if result.modified_count > 0:
                            logger.info(f"Updated user with ID {org_user_id} in organization {org_id}")
                        else:
                            logger.warning(f"Could not find any users to update in organization {org_id}")
                
                # ADDITIONAL DIRECT UPDATE: Update all users with this user_id as admin_id in any organization
                orgs_collection = Organization.get_collection()
                orgs = list(orgs_collection.find({"admin_id": user_id}))
                
                for org in orgs:
                    org_id = org.get("_id")
                    logger.info(f"Found organization {org_id} with user {user_id} as admin, updating users")
                    
                    # Update all users in this organization
                    result = users_collection.update_many(
                        {"organization_id": org_id},
                        {"$set": {"updated_at": datetime.utcnow()}}
                    )
                    
                    if result.modified_count > 0:
                        logger.info(f"Updated {result.modified_count} users in organization {org_id}")
                    
            except Exception as e:
                logger.error(f"Error updating user status in organization {org_id}: {e}")
                
        if referral_code:
            try:
                referral_service = ReferralService()
                result = await referral_service.update_referral_stats_by_code(
                    referral_code=referral_code.upper(),
                    verified_user_email=username
                )
                logger.info(f"Updated referral verification stats: {result}")
            except Exception as referral_error:
                logger.error(f"Error updating referral verification stats: {referral_error}")
        
        # Send welcome email after successful confirmation
        try:
            user_manager = CognitoUserManager(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id']
            )
            
            # Extract origin domain from request
            def get_origin_from_request(request: Request) -> str:
                """Extract the origin domain from the request headers"""
                # Try to get origin from different headers in order of preference
                origin = request.headers.get("origin")
                if origin:
                    return origin
                
                # Try referer header as fallback
                referer = request.headers.get("referer")
                if referer:
                    from urllib.parse import urlparse
                    parsed = urlparse(referer)
                    return f"{parsed.scheme}://{parsed.netloc}"
                
                # Try host header with https as default
                host = request.headers.get("host")
                if host:
                    # Check if it's localhost or contains port (likely dev environment)
                    if "localhost" in host or ":" in host:
                        return f"http://{host}"
                    else:
                        return f"https://{host}"
                
                # Fallback to the original domain if no headers are available
                return "https://kavia.ai"
            
            origin_domain = get_origin_from_request(request)
            
            # Generate URLs for the welcome email using the request origin
            default_url = f"{origin_domain}/login?tenant_id={tenant_id}&email={urllib.parse.quote(user_email)}"
            set_password_url = f"{origin_domain}/users/set_password?tenant_id={tenant_id}&email={urllib.parse.quote(user_email)}"
            login_url = f"{origin_domain}/login?tenant_id={tenant_id}&email={urllib.parse.quote(user_email)}"
            
            # Send welcome email
            user_manager._send_welcome_email(
                email=user_email,
                username=user_email,
                temporary_password="",  # No temporary password for confirmed users
                user_fullname=user_name or user_email.split('@')[0],
                organization_name="KAVIA AI",
                set_password_url=set_password_url,
                login_url=login_url,
                ses_from_email=settings.SES_FROM_EMAIL,
                ses_reply_to=settings.SES_REPLY_TO
            )
            logger.info(f"Welcome email sent successfully to {user_email} after signup confirmation")
            
        except Exception as email_error:
            logger.error(f"Warning: User confirmed but welcome email failed to send: {str(email_error)}")
            # Don't fail the confirmation if email sending fails
        
        return {"message": "Signup confirmed successfully. User is now active. Welcome email sent!"}
    
    except Exception as e:
        # Generic exception handler to catch all exceptions
        logger.error(f"Confirm signup failed: Error for user {username}: {e}")
        
        # Check specific exception types
        if client is not None:
            if hasattr(client, 'exceptions'):
                if isinstance(e, client.exceptions.CodeMismatchException):
                    logger.warning(f"Confirm signup failed: Invalid confirmation code for user {username}")
                    raise HTTPException(status_code=400, detail="Invalid confirmation code.")
                elif isinstance(e, client.exceptions.ExpiredCodeException):
                    logger.warning(f"Confirm signup failed: Expired confirmation code for user {username}")
                    raise HTTPException(status_code=400, detail="Confirmation code has expired.")
                elif isinstance(e, ClientError):
                    logger.error(f"Confirm signup failed: Cognito API error for user {username}: {e}")
                    raise HTTPException(status_code=400, detail=str(e))
        
        # If we get here, it's a general error
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/resend_confirmation_code")
async def resend_confirmation_code(
    username: str = Query(..., description="The user's email address (username)"),
    tenant_id: str = Query(None, description="Tenant ID (optional - will use default if not provided)")
):
    client = None
    try:
        # Handle tenant_id properly
        if tenant_id:
            try:
                tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
            except Exception as e:
                logger.warning(f"Error decoding tenant_id: {e}, using as-is")
        
        # If tenant_id is not provided or decoding failed, use default tenant
        if not tenant_id:
            tenant_id = default_tenant_id
            logger.info(f"No tenant_id provided, using default tenant: {tenant_id}")
            
        logger.info(f"Resending confirmation code for user {username} in organization {tenant_id}")
        
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Ensure SES email configuration is applied
        cognito_pool_creator = CognitoUserPoolCreator()
        ses_configured = cognito_pool_creator.ensure_ses_email_config(creds['user_pool_id'])
        if ses_configured:
            logger.info(f"SES email configuration ensured for resend confirmation in user pool {creds['user_pool_id']}")
        else:
            logger.warning(f"Could not configure SES for resend confirmation in user pool {creds['user_pool_id']}")
        
        # First, check if the user exists and get their status
        try:
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=username
            )
            
            user_status = user_details.get('UserStatus', '')
            logger.info(f"User {username} found with status: {user_status}")
            
            # If user is already confirmed, we need to handle differently
            if user_status == 'CONFIRMED':
                logger.info(f"User {username} is already confirmed. Sending password reset instead.")
                # Use forgot password flow instead
                client.forgot_password(
                    ClientId=creds['client_id'],
                    Username=username
                )
                return {"message": "User is already confirmed. Password reset code sent instead."}
                
        except client.exceptions.UserNotFoundException:
            logger.warning(f"User {username} not found in Cognito. Will attempt to resend code anyway.")
            # Continue with resend_confirmation_code even if admin_get_user fails
            # This is because the user might exist but the admin_get_user might not have permission
        
        # Try the standard resend confirmation code
        try:
            client.resend_confirmation_code(
                ClientId=creds['client_id'],
                Username=username,
            )
            logger.info(f"Confirmation code resent successfully for user {username}")
            return {"message": "Confirmation code resent successfully."}
        except client.exceptions.UserNotFoundException:
            # If user not found with resend_confirmation_code, try to sign them up again
            logger.warning(f"User {username} not found with resend_confirmation_code. Checking MongoDB.")
            
            # Check if user exists in MongoDB
            mongo_handler = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="tenant_users")
            db_user = await mongo_handler.get_one(
                filter={"email": username, "organization_id": tenant_id}, 
                db=mongo_handler.db.users
            )
            
            if not db_user:
                logger.error(f"User {username} not found in MongoDB either.")
                raise HTTPException(status_code=404, detail="User not found in our system.")
            
            # User exists in MongoDB but not in Cognito or is in an invalid state
            # We should return a more helpful message
            logger.error(f"User {username} exists in MongoDB but cannot receive confirmation code.")
            raise HTTPException(
                status_code=400, 
                detail="Unable to resend confirmation code. Please contact support or try the forgot password option."
            )
            
    except client.exceptions.LimitExceededException:
        logger.warning(f"Resend confirmation code failed: Rate limit exceeded for user {username}")
        raise HTTPException(status_code=429, detail="Too many requests. Please try again later.")
    except ClientError as e:
        if 'UserNotFoundException' in str(e):
            logger.error(f"User {username} not found in Cognito")
            raise HTTPException(status_code=404, detail="User not found.")
        logger.error(f"Resend confirmation code failed: Cognito API error for user {username}: {e}")
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])  # More informative error message
    except Exception as e:
        logger.error(f"Resend confirmation code failed: Internal server error for user {username}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/forgot_password")
async def forgot_password(
    email: str = Query(..., description="The user's email address"),
    tenant_id: str = Query(..., description="Tenant ID")
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Ensure SES email configuration is applied
        cognito_pool_creator = CognitoUserPoolCreator()
        ses_configured = cognito_pool_creator.ensure_ses_email_config(creds['user_pool_id'])
        if ses_configured:
            logger.info(f"SES email configuration ensured for forgot password in user pool {creds['user_pool_id']}")
        else:
            logger.warning(f"Could not configure SES for forgot password in user pool {creds['user_pool_id']}")
        
        # First check if user exists in this Cognito pool
        try:
            client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=email
            )
        except client.exceptions.UserNotFoundException:
            logger.error(f"User {email} not found in Cognito user pool for tenant {tenant_id}")
            raise HTTPException(status_code=404, detail="User not found in this organization.")
        
        # If we get here, user exists, so proceed with forgot password
        client.forgot_password(
            ClientId=creds['client_id'],
            Username=email,
        )
        return {"message": "Password reset code sent to your email."}
    except (client.exceptions.LimitExceededException if client else Exception):
        raise HTTPException(status_code=429, detail="Too many requests. Please try again later.")
    except ClientError as e:  # Catch boto3 errors for better debugging
        if 'UserNotFoundException' in str(e):
            raise HTTPException(status_code=404, detail="User not found in this organization.")
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])
    except Exception as e:  # General exception catch
        raise HTTPException(status_code=500, detail=str(e))
    
    
@router.post("/confirm_forgot_password")
async def confirm_forgot_password(
    user: CognitoUser,
    confirmation_code: str = Query(..., description="Code from email"),
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        client.confirm_forgot_password(
            ClientId=creds['client_id'],
            Username=user.email,
            ConfirmationCode=confirmation_code,
            Password=user.password,
        )
        return {"message": "Password reset successful."}
    except client.exceptions.CodeMismatchException:
        raise HTTPException(status_code=400, detail="Invalid confirmation code.")
    except client.exceptions.ExpiredCodeException:
        raise HTTPException(status_code=400, detail="Confirmation code has expired.")
    except ClientError as e:
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refresh_token", summary="Refresh expired ID Token using refresh token")
async def refresh_token(
    refresh_token: str = Body(..., embed=True),
    tenant_id: str = Query(..., description="Tenant ID")
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        response = client.initiate_auth(
            ClientId=creds['client_id'],
            AuthFlow="REFRESH_TOKEN_AUTH",
            AuthParameters={
                'REFRESH_TOKEN': refresh_token,
            }
        )
        id_token = response['AuthenticationResult']['IdToken']
        return {"message": "ID token refreshed successfully.", "id_token": id_token}
    except client.exceptions.NotAuthorizedException as e:
        raise HTTPException(status_code=401, detail="Invalid refresh token.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/logout")
async def logout(
    user_id: str,
    refresh_token: str = Body(..., embed=True, default_factory=""),
    tenant_id: str = Query(..., description="Tenant ID")
):
    """Logs out a user by invalidating their tokens."""
    client = None
    try:
        tenant_id = decode_tenant_id(tenant_id)
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        client.admin_user_global_sign_out(
            UserPoolId=creds['user_pool_id'],
            Username=user_id,
        )

        client.revoke_token(
            Token=refresh_token,
            ClientId=creds['client_id'],
        )

        return {"message": "Logout successful. ID and refresh tokens invalidated."}

    except client.exceptions.InvalidParameterException as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameter: {e}")

    except client.exceptions.NotAuthorizedException:
        raise HTTPException(status_code=401, detail="Unauthorized: ID token may be invalid or expired.")

    except ClientError as ce:
        raise HTTPException(status_code=500, detail=f"Cognito API error: {ce}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

# Add Google OAuth routes
@router.get("/google")
async def login_google(request: Request):
    """Initiate Google OAuth flow"""
    try:
        # Decode tenant ID
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        
        # Get the action parameter (signin or signup)
        action = request.query_params.get("action", "signin")
        
        # Store the action and tenant_id in the state parameter to retrieve it in the callback
        state = f"{action}:{tenant_id}"
        
        google_auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "response_type": "code",
            "scope": "email profile openid",
            "access_type": "offline",
            "prompt": "consent",
            "state": state  # Pass the action and tenant_id as state
        }
        
        # Build the authorization URL with query parameters
        auth_url = f"{google_auth_url}?"
        auth_url += "&".join([f"{key}={value}" for key, value in params.items()])
        print("auth_url", auth_url)
        return RedirectResponse(auth_url)
    except Exception as e:
        logger.error(f"Error initiating Google OAuth flow: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initiating Google OAuth: {str(e)}")

@router.get("/google/callback")
async def google_callback(
    request: Request,
    code: str, 
    state: Optional[str] = None,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Handle the Google OAuth callback"""
    try:
        # Parse state to get action and tenant_id
        state_parts = state.split(":") if state else ["signin", settings.KAVIA_B2C_CLIENT_ID]
        action = state_parts[0] if state_parts[0] in ["signin", "signup"] else "signin"
        # tenant_id = state_parts[1] if len(state_parts) > 1 else settings.KAVIA_B2C_CLIENT_ID
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
        
        logger.info(f"Google callback received with action: {action}, tenant_id: {tenant_id}")
        
        # Exchange authorization code for tokens
        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            "code": code,
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "grant_type": "authorization_code"
        }
        
        logger.info(f"Exchanging authorization code for tokens with Google")
        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        tokens = token_response.json()
        logger.info(f"Successfully obtained tokens from Google")
        
        # Get user info from Google
        google_user_info_url = "https://www.googleapis.com/oauth2/v3/userinfo"
        logger.info(f"Fetching user info from Google")
        user_info_response = requests.get(
            google_user_info_url,
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        user_info_response.raise_for_status()
        user_info = user_info_response.json()
        logger.info(f"User information from Google: {user_info}")
        
        # Get Cognito credentials for the tenant
        logger.info(f"Getting Cognito credentials for tenant: {tenant_id}")
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Check if the user exists in Cognito
        user_exists = False
        try:
            # Try to find the user in Cognito
            logger.info(f"Checking if user {user_info['email']} exists in Cognito")
            user_response = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
            user_exists = True
            logger.info(f"User {user_info['email']} found in Cognito for tenant {tenant_id}")
        except client.exceptions.UserNotFoundException:
            user_exists = False
            logger.info(f"User {user_info['email']} not found in Cognito for tenant {tenant_id}")
        except Exception as e:
            logger.error(f"Error checking user existence: {str(e)}")
            # Default to not existing if there's an error
            user_exists = False
        
        # Handle signin vs signup based on action and user existence
        if action == "signin" and not user_exists:
            # User tried to sign in but doesn't exist
            logger.info(f"User {user_info['email']} tried to sign in but doesn't exist")
            return JSONResponse(
                content={
                    "authenticated": False,
                    "error": "user_not_found",
                    "message": "User does not exist. Please sign up first.",
                    "user_info": {
                        "email": user_info['email'],
                        "name": user_info.get('name', ''),
                        "picture": user_info.get('picture', '')
                    }
                }
            )

        elif action == "signup":
            if user_exists:
                # User already exists but trying to sign up
                # Instead of showing an error, automatically sign them in
                logger.info(f"User {user_info['email']} already exists but trying to sign up. Signing them in automatically.")
                
                # Use password-less authentication instead of setting a password
                try:
                    logger.info(f"Using password-less authentication for existing Google user {user_info['email']}")
                    
                    # Get user details without modifying password
                    user_details = client.admin_get_user(
                        UserPoolId=creds['user_pool_id'],
                        Username=user_info['email']
                    )
                    
                    # Extract user attributes for the response
                    is_admin = False
                    is_free_user = True
                    org_id = ""
                    cognito_user_id = user_details.get('Username')
                    
                    for attr in user_details.get('UserAttributes', []):
                        if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                            is_admin = True
                        elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                            is_free_user = True
                        elif attr['Name'] == 'custom:tenant_id':
                            org_id = attr['Value']
                    
                    # Enable user if disabled, but DON'T reset password
                    if user_details.get('Enabled', True) is False or user_details.get('UserStatus') == 'DISABLED':
                        logger.info(f"Enabling disabled user {user_info['email']} without password reset")
                        client.admin_enable_user(
                            UserPoolId=creds['user_pool_id'],
                            Username=user_info['email']
                        )
                    
                    # Create JWT tokens without password authentication
                    current_time = int(time.time())
                    
                    # Create JWT tokens that are compatible with your existing system
                    token_payload = {
                        "sub": cognito_user_id,
                        "email": user_info['email'],
                        "cognito:username": cognito_user_id,
                        "iss": f"kavia-google-sso",
                        "aud": creds['client_id'],
                        "iat": current_time,
                        "exp": current_time + 604800,  # 7 days
                        "custom:is_admin": str(is_admin).lower(),
                        "custom:free_user": str(is_free_user).lower(),
                        "custom:tenant_id": org_id,
                        "auth_provider": "google_sso",
                        "email_verified": True,
                        "token_use": "id"
                    }
                    
                    id_token = jwt.encode(token_payload, "kavia-google-sso-secret", algorithm="HS256")
                    
                    # Create refresh token
                    refresh_payload = {
                        "sub": cognito_user_id,
                        "token_use": "refresh",
                        "iat": current_time,
                        "exp": current_time + (30 * 24 * 60 * 60)  # 30 days
                    }
                    refresh_token = jwt.encode(refresh_payload, "kavia-google-sso-secret", algorithm="HS256")
                    
                    # Add user to node database if needed
                    background_tasks.add_task(add_user_node_db, user_info['email'], tenant_id)
                    
                    return JSONResponse(
                        content={
                            "authenticated": True,
                            "action": "signin",
                            "message": "User signed in automatically (password preserved)",
                            "provider": "google",
                            "user": {
                                "email": user_info['email'],
                                "name": user_info.get('name', ''),
                                "picture": user_info.get('picture', '')
                            },
                            "userId": cognito_user_id,
                            "username": user_info.get('name', ''),
                            "id_token": id_token,
                            "refresh_token": refresh_token,
                            "access_token": id_token,
                            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                            "organization_id": org_id,
                            "is_admin": is_admin,
                            "free_user": is_free_user,
                            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                            "ExpiresIn": 86400,
                            "TokenType": "Bearer",
                            "auth_method": "google_sso_passwordless"
                        }
                    )
                    
                except Exception as passwordless_error:
                    logger.error(f"Password-less authentication failed: {str(passwordless_error)}")
                    # Fall back to the original behavior if password-less auth fails
                    return JSONResponse(
                        content={
                            "authenticated": False,
                            "error": "user_exists",
                            "message": "User already exists. Please sign in instead.",
                            "user_info": {
                                "email": user_info['email'],
                                "name": user_info.get('name', ''),
                                "picture": user_info.get('picture', '')
                            }
                        }
                    )
            else:
                # Create the user in Cognito
                logger.info(f"Creating new user {user_info['email']} in Cognito")
                user_data = GoogleSignUpRequest(
                    email=user_info['email'],
                    name=user_info.get('name', ''),
                    picture=user_info.get('picture', ''),
                    tenant_id=tenant_id
                )
                logger.info(f"User data for signup: {user_data.model_dump()}")
                return await google_signup(user_data, background_tasks)

        # Default case: action is signin and user exists
        # Authenticate the user with Cognito using password-less method for Google SSO
        logger.info(f"Authenticating existing Google user {user_info['email']}")
        
        # Check if password-less authentication is enabled
        if ENABLE_PASSWORDLESS_GOOGLE_AUTH:
            try:
                logger.info(f"ENABLE_PASSWORDLESS_GOOGLE_AUTH is set to: {ENABLE_PASSWORDLESS_GOOGLE_AUTH}")
                logger.info(f"Attempting password-less authentication for user {user_info['email']}")
                # Use the new password-less authentication method
                auth_response_data = await authenticate_google_user_without_password(
                    user_info=user_info,
                    tenant_id=tenant_id,
                    client=client,
                    creds=creds
                )
                
                logger.info(f"Password-less authentication succeeded for user {user_info['email']}")
                # Add user to node database if needed
                background_tasks.add_task(add_user_node_db, user_info['email'], tenant_id)
                
                return JSONResponse(content=auth_response_data)
                
            except Exception as passwordless_error:
                logger.error(f"Password-less authentication failed for user {user_info['email']}: {str(passwordless_error)}")
                logger.error(f"Error type: {type(passwordless_error).__name__}")
                logger.warning(f"Password-less authentication failed, falling back to password method: {str(passwordless_error)}")
                # Continue to password-based authentication below
        else:
            logger.info(f"Password-less authentication is disabled (ENABLE_PASSWORDLESS_GOOGLE_AUTH={ENABLE_PASSWORDLESS_GOOGLE_AUTH}), using password-based method")

        # Fallback to password-based authentication
        logger.warning(f"Using password-based authentication for user {user_info['email']}")
        
        # IMPORTANT: For Google SSO users, we should NOT reset their password
        # Instead, let's try to authenticate them without changing their existing password
        try:
            # First, check if we can get user details without modifying anything
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
            
            # Extract user attributes for the response
            is_admin = False
            is_free_user = True
            org_id = tenant_id
            cognito_user_id = user_details.get('Username')
            
            for attr in user_details.get('UserAttributes', []):
                if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                    is_admin = True
                elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                    is_free_user = True
                elif attr['Name'] == 'custom:tenant_id':
                    org_id = attr['Value']
            
            # Enable user if disabled, but DON'T reset password
            if user_details.get('Enabled', True) is False or user_details.get('UserStatus') == 'DISABLED':
                logger.info(f"Enabling disabled user {user_info['email']} without password reset")
                client.admin_enable_user(
                    UserPoolId=creds['user_pool_id'],
                    Username=user_info['email']
                )
            
            # Create a minimal response that indicates successful Google SSO without Cognito tokens
            # This preserves the user's existing password
        except client.exceptions.UserNotFoundException:
            logger.error(f"User {user_info['email']} not found in Cognito")
            raise HTTPException(status_code=404, detail="User not found in Cognito")
        
        # Extract user attributes for response
        is_admin = False
        is_free_user = True
        org_id = tenant_id
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
        
        # Instead of creating custom tokens, try to generate tokens through Cognito without password
        # Use admin_create_user_auth_event or similar approach
        try:
            # Try to initiate auth using admin flow without password by creating temporary credentials
            # This is a workaround - we'll create a very short-lived session
            current_time = int(time.time())
            
            # Create a simple session token that your system can recognize as a Google SSO session
            # This mimics the structure your frontend expects but without using Cognito auth flows
            simple_session_token = jwt.encode({
                "sub": cognito_user_id,
                "email": user_info['email'],
                "cognito:username": user_info['email'],
                "iss": f"kavia-google-sso",
                "aud": creds['client_id'],
                "iat": current_time,
                "exp": current_time + 86400,  # 24 hours
                "custom:is_admin": str(is_admin).lower(),
                "custom:free_user": str(is_free_user).lower(),
                "custom:tenant_id": org_id,
                "auth_provider": "google_sso",
                "email_verified": True
            }, "kavia-secret-key", algorithm="HS256")
            
            # Create simple refresh token
            refresh_token = jwt.encode({
                "sub": cognito_user_id,
                "token_use": "refresh",
                "iat": current_time,
                "exp": current_time + (30 * 24 * 60 * 60)  # 30 days
            }, "kavia-secret-key", algorithm="HS256")
            
            logger.info(f"Created password-less session tokens for user {user_info['email']}")
            
            return {
                "authenticated": True,
                "action": "signin",
                "message": "User signed in successfully with Google SSO (password-less)",
                "provider": "google",
                "user": {
                    "email": user_info['email'],
                    "name": user_info.get('name', ''),
                    "picture": user_info.get('picture', ''),
                    "userId": cognito_user_id or user_info['email'],
                    "username": user_info.get('name', '')
                },
                "userId": cognito_user_id or user_info['email'],
                "username": user_info.get('name', ''),
                "id_token": simple_session_token,
                "refresh_token": refresh_token,
                "access_token": simple_session_token,  # Use same token
                "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                "organization_id": org_id,
                "is_admin": is_admin,
                "free_user": is_free_user,
                "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                "ExpiresIn": 86400,
                "TokenType": "Bearer"
            }
            
        except Exception as token_error:
            logger.error(f"Error creating session tokens: {str(token_error)}")
            # If session token creation fails, fall back to a simpler approach
            raise Exception(f"Session token creation failed: {str(token_error)}")
        
    except Exception as e:
        logger.error(f"Error in password-less Google authentication: {str(e)}")
        # Instead of raising HTTPException, just raise a regular exception to trigger fallback
        raise Exception(f"Password-less authentication failed: {str(e)}")

async def authenticate_google_user_without_password(user_info: dict, tenant_id: str, client, creds: dict) -> dict:
    """
    Authenticate a Google SSO user without resetting their password.
    """
    try:
        logger.info(f"Authenticating Google user {user_info['email']} without password reset")
        
        # Get user details
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=user_info['email']
        )
        cognito_user_id = user_details.get('Username')
        
        # Extract user attributes
        is_admin = False
        is_free_user = True
        org_id = tenant_id
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
        
        # Enable user if disabled (but don't reset password)
        if user_details.get('Enabled', True) is False:
            client.admin_enable_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
        
        # Create JWT tokens without password authentication
        current_time = int(time.time())
        id_token = jwt.encode({
            "sub": cognito_user_id,
            "email": user_info['email'],
            "cognito:username": user_info['email'],
            "iss": "kavia-google-sso",
            "aud": creds['client_id'],
            "iat": current_time,
            "exp": current_time + 86400,
            "custom:is_admin": str(is_admin).lower(),
            "custom:free_user": str(is_free_user).lower(),
            "custom:tenant_id": org_id,
            "auth_provider": "google_sso",
            "email_verified": True
        }, "kavia-google-sso-secret", algorithm="HS256")
        
        refresh_token = jwt.encode({
            "sub": cognito_user_id,
            "token_use": "refresh",
            "iat": current_time,
            "exp": current_time + (30 * 24 * 60 * 60)
        }, "kavia-google-sso-secret", algorithm="HS256")
        
        return {
            "authenticated": True,
            "action": "signin",
            "message": "User signed in successfully with Google SSO (password preserved)",
            "provider": "google",
            "user": {
                "email": user_info['email'],
                "name": user_info.get('name', ''),
                "picture": user_info.get('picture', ''),
                "userId": cognito_user_id,
                "username": user_info.get('name', '')
            },
            "userId": cognito_user_id,
            "username": user_info.get('name', ''),
            "id_token": id_token,
            "refresh_token": refresh_token,
            "access_token": id_token,
            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
            "organization_id": org_id,
            "is_admin": is_admin,
            "free_user": is_free_user,
            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
            "ExpiresIn": 86400,
            "TokenType": "Bearer"
        }
        
    except Exception as e:
        logger.error(f"Error in password-less Google authentication: {str(e)}")
        raise Exception(f"Password-less authentication failed: {str(e)}")

@router.get("/debug/google-passwordless")
async def debug_google_passwordless():
    """Debug endpoint to check Google password-less authentication configuration"""
    return {
        "ENABLE_PASSWORDLESS_GOOGLE_AUTH": ENABLE_PASSWORDLESS_GOOGLE_AUTH,
        "settings_value": getattr(settings, 'ENABLE_PASSWORDLESS_GOOGLE_AUTH', 'NOT_SET'),
        "google_client_id": settings.GOOGLE_CLIENT_ID[:10] + "..." if settings.GOOGLE_CLIENT_ID else "NOT_SET",
        "message": "Password-less authentication is " + ("ENABLED" if ENABLE_PASSWORDLESS_GOOGLE_AUTH else "DISABLED")
    }

@router.get("/verify-token")
async def verify_token(token: str = Query(..., description="JWT token to verify")):
    """Verify a JWT token"""
    try:
        # Decode the token without verification (just to get the claims)
        # In a production environment, you should verify the signature
        decoded_token = jwt.decode(token, options={"verify_signature": False})
        
        return JSONResponse(content={"valid": True, "user": decoded_token})
    except Exception as e:
        logger.error(f"Error verifying token: {str(e)}")
        return JSONResponse(content={"valid": False, "error": str(e)})

@router.post("/update-auth-flows")
async def update_auth_flows(tenant_id: str = Query(..., description="Tenant ID")):
    """Update auth flows for a tenant's app client"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Update the app client auth flows
        cognito_pool = CognitoUserPoolCreator()
        updated_client = cognito_pool.update_app_client_auth_flows(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        return {
            "message": "Auth flows updated successfully",
            "client_id": updated_client['ClientId'],
            "auth_flows": updated_client.get('ExplicitAuthFlows', [])
        }
    except Exception as e:
        logger.error(f"Error updating auth flows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating auth flows: {str(e)}")

@router.get("/check-auth-flows")
async def check_auth_flows(tenant_id: str = Query(..., description="Tenant ID")):
    """Check current auth flows for a tenant's app client"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Get the Cognito client
        cognito_pool = CognitoUserPoolCreator()
        
        # Get client details
        client_details = cognito_pool.cognito.describe_user_pool_client(
            UserPoolId=creds['user_pool_id'],
            ClientId=creds['client_id']
        )['UserPoolClient']
        
        return {
            "message": "Auth flows retrieved successfully",
            "client_id": client_details['ClientId'],
            "client_name": client_details.get('ClientName', ''),
            "auth_flows": client_details.get('ExplicitAuthFlows', []),
            "token_validity": {
                "access_token": f"{client_details.get('AccessTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('AccessToken', 'hours')}",
                "id_token": f"{client_details.get('IdTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('IdToken', 'hours')}",
                "refresh_token": f"{client_details.get('RefreshTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('RefreshToken', 'days')}"
            }
        }
    except Exception as e:
        logger.error(f"Error checking auth flows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking auth flows: {str(e)}")

@router.post("/update-ses-email-config")
async def update_ses_email_config(tenant_id: str = Query(..., description="Tenant ID")):
    """Update email configuration for a tenant's user pool to use SES"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Update the user pool email configuration
        cognito_pool = CognitoUserPoolCreator()
        updated_pool = cognito_pool.update_user_pool_email_config(creds['user_pool_id'])
        
        return {
            "message": "Email configuration updated successfully to use SES",
            "user_pool_id": creds['user_pool_id'],
            "email_configuration": updated_pool.get('EmailConfiguration', {}),
            "verification_template": updated_pool.get('VerificationMessageTemplate', {})
        }
    except Exception as e:
        logger.error(f"Error updating SES email config: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating SES email config: {str(e)}")

@router.get("/check-email-config")
async def check_email_config(tenant_id: str = Query(..., description="Tenant ID")):
    """Check current email configuration for a tenant's user pool"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Get the Cognito client
        cognito_pool = CognitoUserPoolCreator()
        
        # Get user pool details
        pool_details = cognito_pool.cognito.describe_user_pool(
            UserPoolId=creds['user_pool_id']
        )['UserPool']
        
        email_config = pool_details.get('EmailConfiguration', {})
        verification_template = pool_details.get('VerificationMessageTemplate', {})
        
        return {
            "message": "Email configuration retrieved successfully",
            "user_pool_id": creds['user_pool_id'],
            "email_configuration": {
                "email_sending_account": email_config.get('EmailSendingAccount', 'COGNITO_DEFAULT'),
                "source_arn": email_config.get('SourceArn', ''),
                "reply_to_email": email_config.get('ReplyToEmailAddress', ''),
                "from_email": email_config.get('From', ''),
                "using_ses": email_config.get('EmailSendingAccount') == 'DEVELOPER'
            },
            "verification_message_template": verification_template,
            "email_verification_message": pool_details.get('EmailVerificationMessage', ''),
            "email_verification_subject": pool_details.get('EmailVerificationSubject', '')
        }
    except Exception as e:
        logger.error(f"Error checking email config: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking email config: {str(e)}")

# Add a new model for Google user signup
class GoogleSignUpRequest(BaseModel):
    email: str
    name: str
    picture: Optional[str] = None
    tenant_id: str
    department: Optional[str] = "External"
    designation: Optional[str] = "Google User"

# Update the password generation to ensure it meets Cognito requirements (used only for new user creation, not Google SSO)
def generate_secure_password():
    # Ensure at least one of each required character type
    lowercase = ''.join(random.choices(string.ascii_lowercase, k=3))
    uppercase = ''.join(random.choices(string.ascii_uppercase, k=3))
    numbers = ''.join(random.choices(string.digits, k=3))
    special = ''.join(random.choices('!@#$%^&*()_+-=[]{}|', k=3))
    
    # Combine all characters and add some random ones to meet length requirement
    all_chars = lowercase + uppercase + numbers + special
    # Add extra random characters to make it 16 characters long
    extra = ''.join(random.choices(string.ascii_letters + string.digits + '!@#$%^&*()_+-=[]{}|', k=4))
    
    # Combine and shuffle
    password = list(all_chars + extra)
    random.shuffle(password)
    return ''.join(password)

@router.post("/google/signup")
async def google_signup(
    user_data: GoogleSignUpRequest,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Endpoint to create a new user account from Google authentication data.
    This creates NEW users without password operations for existing users.
    """
    try:
        # Generate a unique tenant ID for the new user
        random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        org_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = user_data.tenant_id

        if tenant_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(status_code=400, detail="Signup is not allowed for this organization")
        
        logger.info(f"Creating Google user account for {user_data.email} in tenant {tenant_id}")
        
        # Get Cognito credentials for the tenant
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Check if the user already exists
        try:
            client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_data.email
            )
            # User already exists, use password-less sign in
            logger.info(f"User {user_data.email} already exists, using password-less sign in")
            
            # Return password-less authentication response
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_data.email
            )
            
            # Extract user attributes
            is_admin = False
            is_free_user = True
            org_id = ""
            cognito_user_id = user_details.get('Username')
            
            for attr in user_details.get('UserAttributes', []):
                if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                    is_admin = True
                elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                    is_free_user = True
                elif attr['Name'] == 'custom:tenant_id':
                    org_id = attr['Value']
            
            # Create JWT tokens without password operations
            current_time = int(time.time())
            id_token = jwt.encode({
                "sub": cognito_user_id,
                "email": user_data.email,
                "cognito:username": user_data.email,
                "iss": f"kavia-google-sso",
                "aud": creds['client_id'],
                "iat": current_time,
                "exp": current_time + 86400,
                "custom:is_admin": str(is_admin).lower(),
                "custom:free_user": str(is_free_user).lower(),
                "custom:tenant_id": org_id,
                "auth_provider": "google_sso",
                "email_verified": True
            }, "kavia-google-sso-secret", algorithm="HS256")
            
            refresh_token = jwt.encode({
                "sub": cognito_user_id,
                "token_use": "refresh",
                "iat": current_time,
                "exp": current_time + (30 * 24 * 60 * 60)
            }, "kavia-google-sso-secret", algorithm="HS256")
            
            background_tasks.add_task(add_user_node_db, user_data.email, tenant_id)
            
            return JSONResponse(
                content={
                    "authenticated": True,
                    "action": "signin",
                    "message": "User signed in successfully (password preserved)",
                    "provider": "google",
                    "user": {
                        "email": user_data.email,
                        "name": user_data.name,
                        "picture": user_data.picture
                    },
                    "userId": cognito_user_id,
                    "username": user_data.name,
                    "id_token": id_token,
                    "refresh_token": refresh_token,
                    "access_token": id_token,
                    "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                    "organization_id": org_id,
                    "is_admin": is_admin,
                    "free_user": is_free_user,
                    "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                    "ExpiresIn": 86400,
                    "TokenType": "Bearer",
                    "auth_method": "google_sso_passwordless"
                }
            )

        except client.exceptions.UserNotFoundException:
            # User doesn't exist, proceed with creation (this is for NEW users only)
            pass
        except Exception as e:
            logger.error(f"Error checking user existence: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error checking user existence: {str(e)}")

        # Ensure SES email configuration is applied for Google signup
        cognito_pool_creator = CognitoUserPoolCreator()
        ses_configured = cognito_pool_creator.ensure_ses_email_config(creds['user_pool_id'])
        if ses_configured:
            logger.info(f"SES email configuration ensured for Google signup in user pool {creds['user_pool_id']}")
        else:
            logger.warning(f"Could not configure SES for Google signup in user pool {creds['user_pool_id']}")

        # Create NEW user in Cognito (this is the only place where we create passwords for NEW users)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )

        # Update Cognito custom attributes
        user_manager.add_free_tier_to_pool()

        # Only generate password for NEW user creation (not for existing Google SSO users)
        random_password = generate_secure_password()

        # Create the user in Cognito
        cognito_response = client.admin_create_user(
            UserPoolId=creds['user_pool_id'],
            Username=user_data.email,
            UserAttributes=[
                {'Name': 'email', 'Value': user_data.email},
                {'Name': 'email_verified', 'Value': 'true'},
                {'Name': 'custom:Name', 'Value': user_data.name},
                {'Name': 'custom:Designation', 'Value': user_data.designation},
                {'Name': 'custom:Department', 'Value': user_data.department},
                {'Name': 'custom:tenant_id', 'Value': org_id},
                {'Name': 'custom:is_admin', 'Value': 'true'},
                {'Name': 'custom:free_user', 'Value': 'true'}
            ],
            MessageAction='SUPPRESS' if tenant_id == settings.KAVIA_B2C_CLIENT_ID else 'RESEND'
        )
        
        # Set password for NEW user only
        client.admin_set_user_password(
            UserPoolId=creds['user_pool_id'],
            Username=user_data.email,
            Password=random_password,
            Permanent=True
        )

        # Create user in MongoDB
        user_id = cognito_response['User']['Username']
        opentopublic = get_opentopublic()
        
        # Create subscription
        active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        subscription_data = {
            "tenant_id": org_id,
            "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
            "credits": 50000,
            "created_at": datetime.utcnow().isoformat()
        }

        await active_subscription.update_one_data(
            {"user_id": user_id},
            {"$set": subscription_data},
            upsert=True
        )
        llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        llm_cost_data = {
               "user_id": user_id,
                "type": "llm_interaction",
                "user_cost": "$0",
                "projects": [], 
                "cost": "$0",
                "plans_history": [],  
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB",  
                "_task_tracking": {}, 
            }
        await llm_cost_collection.update_one_data(
            {"organization_id": org_id},
            {"$push": {"users": llm_cost_data}},
            upsert=True

        )

        # Create user data object
        mongo_user_data = {
            "_id": user_id,
            "email": user_data.email,
            "name": user_data.name,
            "contact_number": "",
            "department": user_data.department,
            "organization_id": org_id,
            "group_ids": [],
            "is_admin": True,
            "free_user": True,
            "designation": user_data.designation,
            "status": "active" if opentopublic else "inactive",
            "auth_provider": "google",
            "picture": user_data.picture or ""
        }
        
        # Save user to database
        try:
            await User.create(mongo_user_data)
        except Exception as e:
            if "duplicate key error" in str(e):
                logger.warning(f"User {user_id} already exists in database, skipping creation")
            else:
                logger.error(f"Error creating user in database: {str(e)}")

        # Add user to node database
        background_tasks.add_task(add_user_node_db, user_id, org_id)

        # Return success without authentication (user needs to sign in)
        return JSONResponse(
            content={
                "authenticated": False,
                "action": "signup",
                "message": "User created successfully. Please sign in with Google SSO.",
                "user_created": True,
                "email": user_data.email
            }
        )
    except Exception as e:
        logger.error(f"Error during Google signup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during Google signup: {str(e)}"
        )

@router.get("/user-organizations", summary="Get all organizations for a user")
async def get_user_organizations(email: str = Query(..., description="User's email address")):
    """
    Get all organizations/tenants associated with a user's email address.
    """
    try:
        # Initialize MongoDB connection
        users_collection = User.get_collection()
        org_collection = Organization.get_collection()
        
        # Query users with matching email
        users = list(users_collection.find({"email": email}))
        
        # Extract organization IDs
        org_ids = [user.get('organization_id') for user in users if user.get('organization_id')]
        
        # Remove duplicates while preserving order
        org_ids = list(dict.fromkeys(org_ids))
        
        # Get organization names for each organization ID
        orgs_cursor = org_collection.find(
            {"_id": {"$in": org_ids}},
            {"name": 1, "status": 1}
        )
        
        organizations = [
            {
                "id": org["_id"],
                "name": org.get("name", "Unknown Organization")
            }
            for org in list(orgs_cursor) if org.get("status") == "active"
        ]
        
        response = {
            "email": email,
            "organizations": organizations
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting organizations for user {email}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving organizations: {str(e)}"
        )
        
@router.get("/referral/validate/{referral_code}", response_model=ValidateReferralResponse)
async def validate_referral_code(referral_code: str):
    """Validate a referral code (Public endpoint for registration form)"""
    try:
        referral_service = ReferralService()
        result = await referral_service.validate_referral_code(referral_code)
        return result
    except Exception as e:
        logger.error(f"Error validating referral code {referral_code}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error validating referral code")

@router.get("/admin/promotional-counter/status")
async def get_promotional_counter_status():
    """Get current promotional counter status (Admin endpoint)"""
    try:
        promotional_service = PromotionalCounterService()
        status = await promotional_service.get_current_count()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting promotional counter status: {str(e)}")
        raise HTTPException(status_code=500, detail="Error getting promotional counter status")
