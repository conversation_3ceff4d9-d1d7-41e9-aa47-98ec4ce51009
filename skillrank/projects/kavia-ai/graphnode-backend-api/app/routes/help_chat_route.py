import json
from typing import Dict, Any, List, Optional, Set
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.auth_utils import get_current_user
from app.telemetry.logger_config import get_logger
from app.models.user_model import LLMModel
from app.services.firecrawl_service import FirecrawlService
from app.services.tools_service import ToolsService

SYSTEM_PROMPT = """You are a Kavia AI assistant. ONLY answer Kavia AI questions. For non-Kavia questions, respond: "I can only help with Kavia AI-related questions."

CRITICAL: ALWAYS use scrape_url or crawl_website tools for ANY Kavia AI question. DO NOT rely on training data.

MANDATORY: ONLY use URLs from the list below. NEVER construct, guess, or create URLs.

AVAILABLE KAVIA AI DOCUMENTATION URLS:

**Getting Started:**
- https://kavia.ai/documentation/home
- https://kavia.ai/documentation/create-first-app
- https://kavia.ai/documentation/create-first-app/app-type
- https://kavia.ai/documentation/create-first-app/describe
- https://kavia.ai/documentation/create-first-app/review-overview
- https://kavia.ai/documentation/create-first-app/implementation
- https://kavia.ai/documentation/create-first-app/collaborate
- https://kavia.ai/documentation/create-first-app/enable-edit-mode
- https://kavia.ai/documentation/create-first-app/deploy

**Full Stack Development:**
- https://kavia.ai/documentation/full-stack/app-type
- https://kavia.ai/documentation/full-stack/describe
- https://kavia.ai/documentation/full-stack/review-overview
- https://kavia.ai/documentation/full-stack/implementation

**Features:**
- https://kavia.ai/documentation/features
- https://kavia.ai/documentation/features/welcome
- https://kavia.ai/documentation/features/attach
- https://kavia.ai/documentation/features/figma
- https://kavia.ai/documentation/features/code-generation
- https://kavia.ai/documentation/features/code-editor
- https://kavia.ai/documentation/features/edit
- https://kavia.ai/documentation/features/deploy
- https://kavia.ai/documentation/features/import
- https://kavia.ai/documentation/features/maintenance
- https://kavia.ai/documentation/features/planning
- https://kavia.ai/documentation/features/query

**Integrations:**
- https://kavia.ai/documentation/integrations-v2
- https://kavia.ai/documentation/integrations/github-integration
- https://kavia.ai/documentation/integrations/supabase-integration
- https://kavia.ai/documentation/integrations/api-integration

**Support & Best Practices:**
- https://kavia.ai/documentation/faq
- https://kavia.ai/documentation/tips-tricks
- https://kavia.ai/documentation/tips-tricks/troubleshooting
- https://kavia.ai/documentation/tips-tricks/seo
- https://kavia.ai/documentation/tips-tricks/best-practices
- https://kavia.ai/documentation/prompt-engineering

**Billing:**
- https://kavia.ai/documentation/credit

TOOLS:
- scrape_url: For specific pages
- crawl_website: For broader searches

If information not found: "I could not find this information. Check https://kavia.ai/documentation/home"

Always cite source URLs when providing information."""

logger = get_logger(__name__)
router = APIRouter(prefix="/help_chat", tags=["kavia_help_chat"])


class KaviaChatRequest(BaseModel):
    query: str


class ChatService:
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.source_urls: Set[str] = set()  # Use set for efficient deduplication
        
        try:
            self.tools_service = ToolsService(FirecrawlService())
            self.tools_available = True
        except ValueError:
            self.tools_service = None
            self.tools_available = False
        
        self.llm = LLMInterface(
            session_dir="logs/kavia_chat",
            instance_name="kavia_help_chat",
            user_id=user_id,
            project_id=0,
            agent_name="kavia_help_chat"
        )

    async def _track_url_executor(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Wrapper around tool executor to track URLs"""
        # Track the URL before execution
        if tool_name in ["scrape_url", "crawl_website"]:
            url = arguments.get("url")
            if url:
                self.source_urls.add(url)
        
        # Execute the actual tool
        result = await self.tools_service.execute_tool(tool_name, arguments)
        
        # Track additional URLs from crawl results if any
        if tool_name == "crawl_website" and result.get("success"):
            pages = result.get("content", "")
            # Extract URLs from crawl results if needed
            if "===" in pages:
                lines = pages.split("\n")
                for line in lines:
                    if line.startswith("=== ") and line.endswith(" ==="):
                        url = line[4:-4].strip()
                        if url.startswith("https://kavia.ai/"):
                            self.source_urls.add(url)
        
        return result

    async def process_chat(self, query: str) -> Dict[str, Any]:
        if not self.tools_available:
            return self._response(
                "Cannot access documentation. Check https://kavia.ai/documentation/home",
                ["https://kavia.ai/documentation/home"],
                False,
                "Tools unavailable"
            )
        
        try:
            messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": query}
            ]
            
            completion = await self.llm.llm_interaction_wrapper(
                messages=messages,
                user_prompt="",
                system_prompt="", 
                model=LLMModel.gpt_4_1.value,
                function_schemas=self.tools_service.tool_definitions,
                function_executor=self._track_url_executor,
                response_format={"type": "text"},
                stream=False
            )            
            
            if isinstance(completion, str):
                return self._response(completion)
        except Exception as e:
            logger.error(f"Error in process_chat: {str(e)}")
            return self._response("Unexpected error occurred", [], False, str(e))
            
    def _response(self, content: str, sources: Optional[List[str]] = None, 
                 success: bool = True, error: Optional[str] = None) -> Dict[str, Any]:
        """Return response with sources as array"""
        if sources is None:
            sources = sorted(list(self.source_urls))  # Convert set to sorted list
        
        return {
            "response": content,
            "source": sources,  # Now returns array of URLs
            "success": success,
            "error": error
        }


@router.post("/ask")
async def kavia_chat_ask(request: KaviaChatRequest, current_user=Depends(get_current_user)):
    try:
        user_id = current_user.get('cognito:username', 'unknown')
        return await ChatService(user_id).process_chat(request.query)
    except Exception as e:
        logger.error(f"Endpoint error: {str(e)}")
        return {
            "response": "Unexpected error occurred",
            "source": [],  # Empty array for consistency
            "success": False,
            "error": str(e)
        }


@router.post("/test-scraping")
async def test_scraping(url: str = "https://kavia.ai/documentation/home"):
    try:
        result = await FirecrawlService().scrape_url(url)
        return {
            "response": f"Test {'successful' if result['success'] else 'failed'}",
            "source": [url] if result['success'] else [],  # Consistent array format
            "success": result["success"],
            "error": result.get("error")
        }
    except Exception as e:
        return {
            "response": "Test failed",
            "source": [],
            "success": False,
            "error": str(e)
        }