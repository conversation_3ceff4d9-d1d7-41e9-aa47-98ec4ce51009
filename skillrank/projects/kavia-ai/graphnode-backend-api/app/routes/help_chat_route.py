import json
import os
from typing import Set

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from llm_wrapper.core.llm_interface import LLMInterface
from app.utils.auth_utils import get_current_user
from app.telemetry.logger_config import get_logger
from app.models.user_model import LLMModel

from app.services.firecrawl_service import FirecrawlService
from app.services.tools_service import ToolsService


# System prompt for the Kavia AI help chat assistant
HELP_CHAT_SYSTEM_PROMPT = """You are a specialized Kavia AI assistant. You help developers with Kavia AI questions, troubleshooting, best practices, and implementation guidance.

**IMPORTANT: You can ONLY answer questions related to Kavia AI. If a user asks about anything else (other technologies, general programming, personal questions, etc.), politely decline and redirect them to ask Kavia-related questions.**

**Scope of Questions You Can Answer:**
- Kavia AI features, APIs, and services
- Kavia AI authentication and data operations
- Kavia AI real-time features and integrations
- Kavia AI SDK usage and CLI commands
- Kavia AI deployment and configuration
- Kavia AI pricing, limits, and service updates
- Kavia AI troubleshooting and error resolution
- Kavia AI best practices and implementation guides
- Kavia AI integrations with frameworks (Next.js, React, Vue, etc.)

**For Non-Kavia Questions:**
If a user asks about anything not related to Kavia AI, respond with:
"I'm a specialized Kavia AI assistant and can only help with Kavia-related questions. Please ask me about Kavia AI features, APIs, troubleshooting, or implementation guidance. For other topics, I'd be happy to help once you have Kavia-related questions!"

You have access to web scraping tools that can fetch the latest information from the following documentation resources:

**Available Tools:**
- **scrape_url**: Get content from a specific documentation page or resource
- **crawl_website**: Search across multiple pages of the documentation or related sites

**Primary Documentation Resources to scrape when needed:**
- https://kavia-team.atlassian.net/wiki/spaces/~712020fdcb65bf97f74cbf8196f6e52e847380/pages/84049977/Build+your+first+App
- https://docs.google.com/document/d/1hRETGWtq-8qfTQoUKD4OSv7oSWTc9W7J/edit?usp=sharing&ouid=114516339071947775762&rtpof=true&sd=true

**When to use tools (prioritize these actions):**
- User asks about specific Kavia AI features, APIs, or services
- Questions about Kavia AI authentication, data operations, real-time features, integrations
- Troubleshooting Kavia AI errors or configuration issues
- Need current Kavia AI pricing, limits, or service updates
- User asks about Kavia AI SDK usage, CLI commands, or deployment guides
- Need to fetch latest Kavia AI examples, tutorials, or best practices
- Questions about Kavia AI integrations with frameworks (Next.js, React, Vue, etc.)

**Response Guidelines:**
- Always cite source URLs when using scraped content
- Provide code examples and practical implementation steps
- Include relevant CLI commands when applicable
- Mention specific Kavia AI services (Auth, Data, Storage, Integrations, etc.)
- If discussing errors, provide debugging steps and common solutions
- Keep responses technical but accessible
- Only answer Kavia AI-related questions

**If you cannot find relevant Kavia AI information:**
- Suggest checking the official Kavia AI documentation
- Recommend raising an issue or contacting Kavia AI support
- Offer to search additional Kavia AI resources"""

# Chat configuration constants
CHAT_CONFIG = {
    "max_iterations": 3,
    "session_dir": "logs/kavia_chat",
    "instance_name": "kavia_assistant",
    "agent_name": "kavia_assistant",
    "response_format": {"type": "text"},
    "stream": False
}

# Common Kavia AI URLs for quick reference
KAVIA_RESOURCES = {
    "google_doc": "https://docs.google.com/document/d/1hRETGWtq-8qfTQoUKD4OSv7oSWTc9W7J/edit?usp=sharing&ouid=114516339071947775762&rtpof=true&sd=true",
    "atlassian_wiki": "https://kavia-team.atlassian.net/wiki/spaces/~712020fdcb65bf97f74cbf8196f6e52e847380/pages/84049977/Build+your+first+App",
}

logger = get_logger(__name__)

router = APIRouter(prefix="/help_chat", tags=["kavia_assistant"])


class KaviaChatRequest(BaseModel):
    query: str


class ChatService:
    """Service for handling Kavia AI-focused chat interactions with documentation scraping support."""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.scraped_sources: Set[str] = set()
        
        # Initialize services
        try:
            firecrawl_service = FirecrawlService()
            self.tools_service = ToolsService(firecrawl_service)
            self.tools_available = True
        except ValueError:
            logger.warning("Firecrawl not configured - Kavia AI documentation scraping disabled")
            self.tools_service = None
            self.tools_available = False
        
        # Initialize LLM
        self.llm = LLMInterface(
            session_dir=CHAT_CONFIG["session_dir"],
            instance_name=CHAT_CONFIG["instance_name"],
            user_id=user_id,
            project_id=0,
            agent_name=CHAT_CONFIG["agent_name"]
        )

    async def process_chat(self, query: str) -> dict:
        """Process Kavia AI-related chat query with documentation scraping support."""
        if not self.tools_available:
            return {
                "response": "I apologize, but I cannot access Kavia AI documentation at the moment due to scraping tools being unavailable. Please check the official Kavia AI docs at https://kavia.ai/docs or contact support.",
                "error": "Scraping tools not configured"
            }
        
        messages = [
            {"role": "system", "content": HELP_CHAT_SYSTEM_PROMPT},
            {"role": "user", "content": query}
        ]
        
        return await self._chat_with_tools(messages)

    async def _chat_with_tools(self, messages: list) -> dict:
        """Handle chat interaction with tool support."""
        max_iterations = CHAT_CONFIG["max_iterations"]
        
        for iteration in range(1, max_iterations + 1):
            logger.info(f"Chat iteration {iteration}")
            
            completion = await self.llm.llm_interaction_wrapper(
                messages=messages,
                user_prompt="",
                system_prompt="", 
                model=LLMModel.gpt_4_1.value,
                function_schemas=self.tools_service.tool_definitions,
                function_executor=self.tools_service.execute_tool,
                response_format=CHAT_CONFIG["response_format"],
                stream=CHAT_CONFIG["stream"]
            )
            
            # Handle different response types
            if isinstance(completion, str):
                return self._create_response(completion, iteration)
            
            if isinstance(completion, dict):
                error_msg = completion.get("error", json.dumps(completion))
                logger.error(f"LLM error: {error_msg}")
                return self._create_response(error_msg, iteration)
            
            # Handle tool calls
            response_message = completion.choices[0].message
            
            if not (hasattr(response_message, 'tool_calls') and response_message.tool_calls):
                return self._create_response(response_message.content, iteration)
            
            # Process tool calls
            await self._process_tool_calls(messages, response_message)
        
        # Max iterations reached
        fallback = "I'm having trouble gathering the Kavia AI information you need. Please try rephrasing your question, check the official docs at https://kavia.ai/docs, or ask in the Kavia AI community."
        return self._create_response(fallback, max_iterations)

    async def _process_tool_calls(self, messages: list, response_message) -> None:
        """Process and execute tool calls."""
        logger.info(f"Processing {len(response_message.tool_calls)} tool calls")
        
        # Add assistant message with tool calls
        messages.append({
            "role": "assistant",
            "content": response_message.content or "",
            "tool_calls": [
                {
                    "id": tc.id,
                    "type": tc.type,
                    "function": {
                        "name": tc.function.name,
                        "arguments": tc.function.arguments
                    }
                } for tc in response_message.tool_calls
            ]
        })
        
        # Execute each tool call
        for tool_call in response_message.tool_calls:
            tool_name = tool_call.function.name
            arguments = json.loads(tool_call.function.arguments)
            
            logger.info(f"Executing {tool_name}: {arguments.get('url', 'N/A')}")
            
            # Execute tool and track sources
            tool_result = await self.tools_service.execute_tool(tool_name, arguments)
            
            # Debug logging for source tracking
            if tool_result.get("success"):
                logger.info(f"Tool {tool_name} succeeded, tracking sources")
                self._track_sources(tool_result)
            else:
                logger.warning(f"Tool {tool_name} failed: {tool_result.get('error')}")
            
            # Add tool result to conversation
            messages.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": json.dumps(tool_result, indent=2)
            })

    def _track_sources(self, tool_result: dict) -> None:
        """Track scraped sources for response metadata."""
        if not tool_result.get("success"):
            return
        
        tool_type = tool_result.get("tool")
        logger.info(f"Tracking sources for tool: {tool_type}")
        
        if tool_type == "scrape_url":
            url = tool_result.get("url")
            if url:
                self.scraped_sources.add(url)
                logger.info(f"Added source: {url}")
        elif tool_type == "crawl_website":
            pages = tool_result.get("individual_pages", [])
            logger.info(f"Processing {len(pages)} pages for sources")
            for page in pages:
                url = page.get("url")
                if url:
                    self.scraped_sources.add(url)
                    logger.info(f"Added source: {url}")

    def _create_response(self, content: str, iterations: int) -> dict:
        """Create standardized response object."""
        response = {
            "response": content
        }
        
        # Only include sources if any were scraped
        if self.scraped_sources:
            response["sources_scraped"] = list(self.scraped_sources)
            
        return response


@router.post("/ask")
async def kavia_chat_ask(
    request: KaviaChatRequest,
    current_user=Depends(get_current_user)
):
    """Main Kavia AI assistant endpoint - Ask questions about Kavia AI features, troubleshooting, and implementation."""
    try:
        user_id = current_user.get('cognito:username', 'unknown')
        logger.info(f"Kavia AI chat request from {user_id}: {request.query}")
        
        chat_service = ChatService(user_id)
        response = await chat_service.process_chat(request.query)
        
        sources_count = len(response.get('sources_scraped', []))
        logger.info(f"Response sent: {len(response['response'])} chars, {sources_count} sources")
        return response
        
    except Exception as e:
        logger.error(f"Kavia AI chat error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-kavia-scraping")
async def test_kavia_scraping(url: str = "https://kavia.ai/docs"):
    """Test endpoint for Kavia AI documentation scraping."""
    try:
        service = FirecrawlService()
        result = await service.scrape_url(url)
        
        return {
            "test": "kavia_docs_scrape",
            "url": url,
            "success": result["success"],
            "content_length": len(result.get("content", "")),
            "title": result.get("title", ""),
            "error": result.get("error")
        }
    except Exception as e:
        return {"test": "kavia_docs_scrape", "error": str(e)}


@router.get("/kavia-resources")
async def get_kavia_resources():
    """Get the two available documentation URLs for reference."""
    return {
        "message": "Available documentation resources for the assistant to scrape",
        "resources": KAVIA_RESOURCES
    }