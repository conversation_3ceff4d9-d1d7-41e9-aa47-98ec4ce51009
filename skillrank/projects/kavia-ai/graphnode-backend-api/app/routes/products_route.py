import logging
from fastapi import APIRouter, HTTPException
from app.core.Settings import settings
import stripe
from app.utils.product_utils import format_price
from app.models.product_model import ProductListResponse
from typing import List, Optional, Dict, Any
from decimal import Decimal

stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)

SHOW_NAME = "products"
router = APIRouter(
    prefix=f"/{SHOW_NAME}",
    tags=[SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

# Constants for better maintainability
METADATA_KEYS = {
    'credits': ['Credits', 'credits', 'CREDITS', 'Credit', 'credit'],
    'default_credits': ['Default Credits', 'default_credits', 'DEFAULT_CREDITS', 'DefaultCredits', 'default credits'],
    'display_credits': ['Display Credits', 'display_credits', 'DISPLAY_CREDITS', 'DisplayCredits', 'display credits'],
    'visibility': ['Visibility', 'visibility', 'VISIBILITY', 'visible']
}
MAX_PRODUCTS_LIMIT = 100


def extract_metadata_fields(metadata: Dict[str, Any], product_id: str) -> Dict[str, Any]:
    """
    Extract relevant fields from product metadata.
    
    Args:
        metadata: Product metadata dictionary
        product_id: Product ID for logging purposes
        
    Returns:
        Dictionary with extracted metadata fields
    """
    result = {"credits": None}
    
    if not metadata:
        return result
    
    # Extract credits
    result["credits"] = _extract_credits(metadata, product_id)
    
    # Example: Extract other metadata fields if needed
    # result["category"] = metadata.get("category")
    # result["features"] = metadata.get("features", "").split(",") if metadata.get("features") else []
    # result["popular"] = metadata.get("popular", "").lower() == "true"
    
    return result


def extract_product_metadata(metadata: Dict[str, Any], product_id: str) -> Dict[str, Optional[int]]:
    """
    Extract all relevant metadata fields from product metadata.
    
    Args:
        metadata: Product metadata dictionary
        product_id: Product ID for logging purposes
        
    Returns:
        Dictionary with all extracted metadata fields
    """
    result = {
        'credits': None,
        'default_credits': None,
        'display_credits': None,
        'visibility': None
    }
    
    if not metadata:
        logger.debug(f"No metadata found for product {product_id}")
        return result
    
    # Extract each metadata field
    for field_name, possible_keys in METADATA_KEYS.items():
        result[field_name] = _extract_metadata_field(metadata, possible_keys, product_id, field_name)
    
    logger.debug(f"Extracted metadata for {product_id}: {result}")
    return result


def _extract_metadata_field(metadata: Dict[str, Any], possible_keys: List[str], product_id: str, field_name: str) -> Optional[int]:
    """
    Extract a specific metadata field using multiple possible key variations.
    
    Args:
        metadata: Product metadata dictionary
        possible_keys: List of possible key names to try
        product_id: Product ID for logging
        field_name: Field name for logging
        
    Returns:
        Parsed field value or None if not found/invalid
    """
    # Strategy 1: Try exact key matches
    for key in possible_keys:
        if key in metadata:
            value = _parse_integer_value(metadata[key], product_id, key, field_name)
            if value is not None:
                return value
    
    # Strategy 2: Try partial key matches (for flexible naming)
    search_term = field_name.replace('_', '').lower()
    for key, value in metadata.items():
        key_normalized = key.replace(' ', '').replace('_', '').lower()
        if search_term in key_normalized:
            parsed_value = _parse_integer_value(value, product_id, key, field_name)
            if parsed_value is not None:
                return parsed_value
    
    logger.debug(f"No {field_name} found in metadata for product {product_id}")
    return None


def _parse_integer_value(value: Any, product_id: str, key: str, field_name: str) -> Optional[int]:
    """
    Parse integer value from metadata, handling commas and type conversion.
    
    Args:
        value: Raw metadata value
        product_id: Product ID for logging
        key: Metadata key for logging
        field_name: Field name for logging
        
    Returns:
        Parsed integer value or None if invalid
    """
    try:
        # Handle None or empty values
        if value is None or str(value).strip() == '':
            return None
            
        # Remove commas and whitespace, then convert to int
        value_str = str(value).replace(',', '').strip()
        parsed_value = int(value_str)
        
        # Validate reasonable range for credits (optional)
        if field_name in ['credits', 'default_credits', 'display_credits'] and parsed_value < 0:
            logger.warning(f"Negative {field_name} value for {product_id}: {parsed_value}")
            return None
        
        # Validate visibility range (0 or 1 typically)
        if field_name == 'visibility' and parsed_value not in [0, 1]:
            logger.warning(f"Unusual visibility value for {product_id}: {parsed_value} (expected 0 or 1)")
            # Don't return None here as it might be intentional
            
        logger.debug(f"Parsed {field_name} for {product_id} using key '{key}': {parsed_value}")
        return parsed_value
        
    except (ValueError, TypeError) as e:
        logger.warning(f"Invalid {field_name} value for {product_id} with key '{key}': {value} - {e}")
        return None


def create_product_response(product: stripe.Product) -> Optional[ProductListResponse]:
    """
    Create ProductListResponse from Stripe product data.
    
    Args:
        product: Stripe product object
        
    Returns:
        ProductListResponse object or None if product is invalid
    """
    try:
        if not product.default_price:
            logger.warning(f"Product {product.id} has no default price, skipping")
            return None

        default_price = product.default_price
        
        # Extract all metadata fields
        metadata_fields = extract_product_metadata(product.metadata, product.id)
        
        # Handle recurring price information
        is_recurring = bool(default_price.recurring)
        recurring_interval = default_price.recurring.interval if is_recurring else None
        recurring_interval_count = default_price.recurring.interval_count if is_recurring else None
        
        return ProductListResponse(
            product_id=product.id,
            product_name=product.name,
            product_description=product.description or "",
            price_id=default_price.id,
            currency=default_price.currency,
            price=format_price(default_price.unit_amount_decimal),
            is_recurring=is_recurring,
            recurring_interval=recurring_interval,
            recurring_interval_count=recurring_interval_count,
            # All metadata fields
            credits=metadata_fields['credits'],
            default_credits=metadata_fields['default_credits'],
            display_credits=metadata_fields['display_credits'],
            visibility=metadata_fields['visibility']
        )
        
    except Exception as e:
        logger.error(f"Error processing product {product.id}: {str(e)}")
        return None


def get_price_for_sorting(product_response: ProductListResponse) -> int:
    """
    Extract price value for sorting purposes.
    
    Args:
        product_response: Product response object
        
    Returns:
        Price in cents as integer for sorting
    """
    try:
        # Convert formatted price back to cents for sorting
        # This assumes format_price returns a decimal string
        return int(Decimal(product_response.price) * 100)
    except (ValueError, TypeError, ArithmeticError):
        logger.warning(f"Could not parse price for sorting: {product_response.price}")
        return 0  # Default to 0 for sorting if price parsing fails


@router.get("/list", response_model=List[ProductListResponse])
async def get_products() -> List[ProductListResponse]:
    """
    Retrieve all active Stripe products with pricing information and credits metadata.
    
    Returns:
        List of products sorted by price (ascending).
        
    Raises:
        HTTPException: 500 if Stripe API fails or unexpected error occurs.
    """
    try:
        # Fetch products from Stripe with expanded price data
        products = stripe.Product.list(
            active=True,
            expand=["data.default_price"],
            limit=MAX_PRODUCTS_LIMIT
        )
        
        logger.info(f"Retrieved {len(products.data)} products from Stripe")
        
        # Process products and filter out invalid ones
        product_responses = []
        for product in products.data:
            product_response = create_product_response(product)
            if product_response:
                product_responses.append(product_response)
        
        # Sort by price (ascending)
        product_responses.sort(key=get_price_for_sorting)
        
        logger.info(f"Successfully processed {len(product_responses)} valid products")
        return product_responses
        
    except stripe.error.StripeError as e:
        logger.error(f"Stripe API error: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch products from Stripe: {e.user_message or 'Unknown error'}"
        )
    except Exception as e:
        logger.error(f"Unexpected error in get_products: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")