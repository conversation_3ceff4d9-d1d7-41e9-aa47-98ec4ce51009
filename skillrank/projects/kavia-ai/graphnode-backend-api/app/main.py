
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.responses import  J<PERSON>NResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from app.utils.auth_userpool_utils import cognito_manager as cognito_auth_manager
import uvicorn, jwt
from app.core.Settings import settings
from app.utils.auth_utils import verify_token_and_get_user_details
from app.routes import (
    notification_route, github_oauth_route, kg_route, task_route, node_route, base_route, chat_route, configure_route,
    discussion_route, users_route, authentication_route, agent_route,
    architecture_route, repository_route,  requirement_route, 
    batch_route, graph_route, file_route, figma_route, deployment_route, documentation_route, group_manager_route, permissions_route, scm_route,code_query,docs_query, announcement_route, health_route,testcase_route, payment_route, products_route, project_guidance_route
)
from app.routes.organization import organization_route, super_route
from app.connection.establish_db_connection import connect_node_db, connect_vector_db
from app.utils.aws.cognito_main import tenant_service
from app.telemetry.logger_config import get_logger
from app.connection.tenant_middleware import TenantMiddleware, tenant_context, set_opentopublic
from app.connection.establish_db_connection import get_mongo_db
from app.repository.mongodb.client import connect_db
from app.routes.organization import settings_route
from app.utils.cost_utils import check_free_credit_limits_crossed
from app.models.organization_models import Organization

from app.telemetry.otel_config import setup_telemetry, instrument_app
from app.telemetry.location_tracking_middleware import LocationTrackingMiddleware
from app.routes import enhanced_dashboard_route, supabase_route
import os
from app.routes.help_chat_route import router as help_chat_router

USE_API_TRACKING = not bool(os.getenv("STOP_API_TRACKING"))
if USE_API_TRACKING:
    # Initialize OpenTelemetry
    tracer = setup_telemetry()

NON_GATED_PATHS = [
    '/api/node/list_projects/',
    "/api/ws",
    "/api/auth",
    "/api/oauth/github/callback",
    "/api/health",
    "/api/products/list",
    "/api/manage",
    "/api/payment",
    "/api/supabase/auth/supabase/callback"
]

class CORSJSONResponse(JSONResponse): #to bypass the CORS issues while blocking POST requests after credits limit reached
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.headers.update({
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "*",
            "Access-Control-Allow-Headers": "*"
        })

logger = get_logger(__name__)
logger.info("Logging setup completed")


security = HTTPBearer()
dependencies = [Depends(security)]


def get_application():
    """Create a new FastAPI application."""
    
    _app = FastAPI(
        debug=False,
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,
            "tagsSorter": "alpha",
        },
        openapi_schema={
            "openapi": "3.0.2",
            "info": {
                "title": "Kavia AI API",
                "version": "1.0.0"
            },
            "components": {
                "securitySchemes": {
                    "BearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    }
                }
            }
        }
    )

    origins = ['*']
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add session middleware
    _app.add_middleware(SessionMiddleware, secret_key="your-secret-key")  # Add your secure key here
    _app.add_middleware(TenantMiddleware)
    if USE_API_TRACKING and not settings.LOCAL_DEBUG:
        _app.add_middleware(LocationTrackingMiddleware)
    
    @_app.middleware("http")
    async def auth_middleware(request: Request, call_next):

        # Set opentopublic context based on organization settings
        try: 
            organization = await Organization.get(settings.KAVIA_SUPER_TENANT_ID)
            if organization and 'opentopublic' in organization:
                request.state.opentopublic = organization['opentopublic']
                set_opentopublic(organization['opentopublic'])
            else:
                set_opentopublic(False)
        except Exception as e:
            logger.error(f"Error setting opentopublic context: {str(e)}")
            set_opentopublic(False)

        if request.method == "OPTIONS" or any(
        request.url.path.startswith(path) for path in [
            "/api/ws",
            "/api/auth",
            "/api/oauth/github/callback",
            "/api/health",
            "/api/products/list",
            "/api/supabase/auth/supabase/callback"
        ]):
            return await call_next(request)
        
        
        
        if request.url.path.startswith("/api"):
            request.state.user = {"cognito:username": "admin", "sub": "admin", "email": "<EMAIL>"}  # default user
            authorization_header = request.headers.get("Authorization")

            # First try to get tenant_id from JWT token if authorization header exists
            tenant_id = None
            if authorization_header:
                try:
                    # Remove 'Bearer ' prefix if present
                    token = authorization_header.replace('Bearer ', '') if authorization_header.startswith('Bearer ') else authorization_header
                    # Decode JWT without verification to extract custom:tenant_id
                    decoded = jwt.decode(token, options={"verify_signature": False})
                    tenant_id = decoded.get('custom:tenant_id')
                except:
                    pass
            
            # Fallback to other methods if tenant_id not found in JWT
            tenant_id = tenant_id or request.headers.get("X-Tenant-Id") or request.cookies.get('X-Tenant-Id') or settings.KAVIA_ROOT_TENANT_ID
            
            try:
                if tenant_id == settings.KAVIA_ROOT_TENANT_ID:
                    claims = verify_token_and_get_user_details(authorization_header)
                else:
                    tenant_cred = await tenant_service.get_tenant_cred(tenant_id)
                    claims = cognito_auth_manager.verify_token_and_get_user_details(authorization_header, tenant_cred['user_pool_id'])
            except Exception as e:
                return JSONResponse(status_code=401, content={"message": str(e)})
            claims['tenant_id'] = tenant_id  # tenant_id is already set above with default "T0000"
            tenant_context.set(tenant_id)

                
            request.state.user = claims  
                    
            print("main.py- tenant_id: ", tenant_id)
            if authorization_header:
                
                if any(request.url.path.startswith(path) for path in [ #unblocking any super route with credits check. Put any route that doesn't need credits check here
                    "/api/manage",
                    "/api/payment"
                ]):
                    response = await call_next(request)
                    return response
                else:
                    
                    if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
                    # For B2C tenants, we need to check limits per user
                        if request.method == "GET" or request.headers.get('Bypass-Credit-Check') == "true":
                            return await call_next(request)
                            
                        if await check_free_credit_limits_crossed(tenant_id, claims):
                            return CORSJSONResponse(
                                status_code=402,
                                content={"detail": "Credit limit reached, please upgrade the plans."}
                            )
                    elif tenant_id != settings.KAVIA_B2C_CLIENT_ID and not tenant_id.startswith("default"):
                        # For regular tenants, use the existing logic
                        if request.method == "GET" or request.headers.get('Bypass-Credit-Check') == "true":
                            return await call_next(request)
                            
                        if await check_free_credit_limits_crossed(tenant_id):
                            return CORSJSONResponse(
                                status_code=402,
                                content={"detail": "Credit limit reached, please upgrade the plans"}
                            )

                    if not any(request.url.path.startswith(path) for path in NON_GATED_PATHS) and request.headers.get('is_public_selected') == 'true' and request.method == "POST" :
                        # Handling Shared project
                        selected_tenant_id = request.headers.get('selected_tenant_id')
                        if selected_tenant_id.startswith("default"):
                            selected_tenant_id = settings.KAVIA_B2C_CLIENT_ID
                            
                        if tenant_id.startswith("default"):
                            _tenant_id = settings.KAVIA_B2C_CLIENT_ID
                        else:
                            _tenant_id = tenant_id

                        if selected_tenant_id != _tenant_id:
                            return CORSJSONResponse(
                                    status_code=402,
                                    content = {"detail": "You are not authorized to access this resource."}
                                )
                        else:
                            if _tenant_id == settings.KAVIA_B2C_CLIENT_ID:
                                if request.headers.get('selected_project_creator_email') != claims['email']:
                                    return CORSJSONResponse(
                                        status_code=402,
                                        content = {"detail": "You are not authorized to access this resource."}
                                    )

                    response = await call_next(request)
                    return response
            else:
                return JSONResponse(status_code=401, content={"message": "Authorization header is missing."})
        else:
            response = await call_next(request)
            return response


    # Include routers
    _app.include_router(base_route.router)
    _app.include_router(authentication_route.router, prefix="/api")
    _app.include_router(node_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(configure_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(chat_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(discussion_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(users_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(task_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(agent_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(architecture_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(repository_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(requirement_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(batch_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(graph_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(file_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(figma_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(github_oauth_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(deployment_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(documentation_route.router, prefix='/api', dependencies=dependencies)
    _app.include_router(group_manager_route.router, prefix="/api",dependencies=dependencies)
    _app.include_router(permissions_route.router, prefix="/api",dependencies=dependencies)
    _app.include_router(organization_route.router, prefix="/api",dependencies=dependencies)
    _app.include_router(super_route.router, prefix="/api",dependencies=dependencies)
    _app.include_router(kg_route.router, prefix="/api/v1", dependencies=dependencies, tags=["knowledge-graph"])
    _app.include_router(code_query.router, prefix="/api", dependencies=dependencies),
    _app.include_router(docs_query.router, prefix="/api", dependencies=dependencies)
    _app.include_router(notification_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(scm_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(settings_route.router, prefix="/api/org", dependencies=dependencies)
    _app.include_router(announcement_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(testcase_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(payment_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(project_guidance_route.router, prefix="/api", dependencies=dependencies)
    if not settings.LOCAL_DEBUG:
        _app.include_router(enhanced_dashboard_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(code_query.router, prefix="/api", dependencies=dependencies),
    _app.include_router(supabase_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(supabase_route.public_auth_router, prefix="/api/supabase")  
    _app.include_router(help_chat_router, prefix="/api", dependencies=dependencies)


    #Product listing route- no auth required
    _app.include_router(products_route.router, prefix="/api", dependencies=None)
    # Health check routes - no auth required for Kubernetes probes
    _app.include_router(health_route.router, prefix="/api")
    if USE_API_TRACKING or not settings.LOCAL_DEBUG:
        instrument_app(_app)
    return _app

def get_complete_application():
    """In production need to start and close db connection and some middleware."""
    _app = get_application()
    # _app.add_event_handler("startup", connect_llm)
    _app.add_event_handler("startup", connect_db)
    _app.add_event_handler("startup", connect_node_db)
    _app.add_event_handler("startup", connect_vector_db)
    _app.add_event_handler("startup", lambda: get_mongo_db(collection_name='tasks'))
    logger.info("Complete application setup finished")
    return _app

app = get_complete_application()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, )
    logger.info("Uvicorn server started") 

