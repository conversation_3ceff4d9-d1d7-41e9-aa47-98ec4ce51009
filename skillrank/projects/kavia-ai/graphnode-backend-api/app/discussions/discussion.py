import json
import asyncio
from openai import Stream
from typing import Dict, Any, AsyncGenerator,List
import re
import uuid
from app.utils.kg_inspect.kg_tool import KgTools
import os
# from app.routes.users_route import list_users, ListUsersQuery
from app.connection.establish_db_connection import get_node_db, get_vector_db, get_mongo_db
from app.connection.llm_init import get_llm_interface
from datetime import datetime
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
from app.core.function_schema_generator import get_function_schema
import logging
from llm_wrapper.utils.base_tools import DynamicToolFactory, ToolRegistry
from llm_wrapper.core.llm_interface import LLMInterface
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.logs_utils import get_path
from app.core import data_model_helper
from app.core.data_model_helper import data_model_helper
from app.utils.conversation_utils  import ConversationUtils
from app.models.chat_model import Chat<PERSON>ontext
from app.models.discussion_model import FileAttachment
from app.telemetry.logger_config import get_logger, set_discussion_id
from app.connection.establish_db_connection import get_mongo_db
from app.utils.prodefn.projdefn_utils import ProjectDefinitionManager
from copy import deepcopy
from app.telemetry.discussion_timer.timer import DiscussionTimer
from app.utils.prodefn.docs_session_manager import get_ingest_session
import boto3
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.core.Settings import settings
from app.utils.logs_utils import get_path  # Import settings
import asyncio
import inspect
from app.utils.prodefn.docs_session_manager import get_or_create_session
from app.core.data_model_helper import data_model
from app.utils.node_version_manager import NodeVersionManager
from app.discussions.discussion_context import auto_config_session_context
from app.celery_app import celery_task_id
import logging
import os
from datetime import datetime
from app.utils.datetime_utils import generate_timestamp
from app.utils.prodefn.docs_tool import DocsTools
from app.models.user_model import LLMModel


# tools methods
registry = ToolRegistry()
registry.register_tool("DiscussionTools", DiscussionTools)

factory = DynamicToolFactory(registry)
exec_agent = factory.create_dynamic_tool(["DiscussionTools"])


base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
discussion_config_path = os.path.join(base_dir, 'app', 'discussions', 'prompt_templates', 'discussion_configurations.json')

class Discussion:
    def __init__(self, discussion_type, node_id, discussion_id=None, title=None, description=None,node_type=None,invocation_type=None):

        # self.model_name = None
        self.model_name = LLMModel.gpt_4_1.value
        self.explicit_model_name = False  # Track if model was explicitly set

        # Basic setup, no async calls
        self.node_type = node_type
        self.update_logger = get_logger(__name__)
        self.llm_interface = get_llm_interface()
        self.db = get_node_db()
        self.vector_db = get_vector_db()
        self.discussion_type = discussion_type
        self.node_id = node_id
        self.discussion_id = discussion_id
        if discussion_id:
            set_discussion_id(discussion_id)
        self.title = title
        self.description = description
        self.retrieved_info = {}
        self.discussion_so_far = []
        self.modifications = {}
        self.file_attachments = []
        self.has_image_urls = False
        self.timer = DiscussionTimer()
        self.function_schemas =[]
        self.doc_session_id = None
        self.session_id = auto_config_session_context.get()
        self.version_manager = NodeVersionManager()

        # Set invocation type if provided in constructor
        if invocation_type:
            self.invocation_type = invocation_type
        else:
            self.invocation_type = 'interactive'  # Default value


        self.s3_client = boto3.client('s3',
                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                          region_name=settings.AWS_REGION
                          )
        self.doc_tools = None
        self.discussion_tools = None 
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False

        # Define the discussion search criteria mapping
       
        # self.utils = ConversationUtils(current_user)

        #print(f'initializing discussion type {self.discussion_type}')
        with open(discussion_config_path, 'r') as f:
            self.discussion_configurations = json.load(f)
        self.current_configuration = self.discussion_configurations.get(f"{self.node_type}Configuration", {})
        self.template_name = None
        self.function_schema_type = None
        self.function_schema = None
        self.invocation_type = invocation_type
        self.update_logger.info(f"expected node_type {self.node_type}, discussion_type {self.discussion_type}")
    # hooks = [get_function_schema(.node_type, True)]
        self.supervisor = None  # Add supervisor attribute
        self.is_flagged_for_reconfig = False
        self.reconfig_reason = None
        self.flagged_at = None
        self.triggering_parent_id = None
        
    def set_supervisor(self, supervisor):
        """Set the supervisor for this discussion"""
        self.supervisor = supervisor

    def get_strict_session_id(self, context="general"):
        """
        STRICT SESSION ID RESOLVER

        This method enforces the rule that auto-configuration discussions
        MUST use Celery task_id as session ID, with NO exceptions.

        Args:
            context (str): Context for debugging (e.g., "streaming", "knowledge", "main")

        Returns:
            str: The session ID to use for LLM interface

        Raises:
            ValueError: If auto-config discussion has no Celery task_id
        """
        invocation_type = getattr(self, 'invocation_type', 'interactive')
        celery_task_id_value = celery_task_id.get()

        if invocation_type == "autoconfig":
            # STRICT ENFORCEMENT: Auto-config MUST have Celery task_id
            if not celery_task_id_value:
                error_msg = f"CRITICAL ERROR: Auto-config discussion {self.discussion_id} in {context} has no Celery task_id!"
                raise ValueError(error_msg)

            # FORCE use of Celery task_id - NO FALLBACK
            session_id = celery_task_id_value
        else:
            # Interactive discussions use discussion_id
            session_id = self.discussion_id

        return session_id





    def set_invocation_type(self, invocation_type):
        self.invocation_type = invocation_type
        self.update_logger.info(f"set_invocation : type {self.invocation_type}")

    def set_current_user(self, current_user):
        self.current_user = current_user
        
    def set_file_attachments(self, file_attachments):
        self.file_attachments = file_attachments

    def set_model_name(self, model_name):
        """Directly set the model name for this discussion from the request"""
        self.model_name = model_name
        self.explicit_model_name = True  # Mark that model was explicitly set
        self.update_logger.info(f"Model name explicitly set to: {model_name}")

    async def set_default_model_name(self):
        """Set default model name from project if not explicitly set"""
        # Only set default if no explicit model was provided
        if self.explicit_model_name:
            self.update_logger.info(f"Using explicitly set model: {self.model_name}")
            return
            
        try:
            project_node = await self.db.get_root_node(self.node_id)
            if project_node:
                project_model_name = project_node.get('properties', {}).get('model_name')
                if project_model_name:
                    self.model_name = project_model_name
                    self.update_logger.info(f"Inherited model from project: {project_model_name}")
                    return
            # Fallback to system default if no project model found
            # self.model_name = LLMModel.gpt_4_1.value
            self.update_logger.info(f"Using system default model: {self.model_name}")
        except Exception as e:
            self.update_logger.error(f"Error setting model name: {str(e)}")
            # self.model_name = LLMModel.gpt_4_1.value
            
    async def async_initialize(self):
        # Always call set_default_model_name, but it will respect explicit setting
        await self.set_default_model_name()
        
        if self.discussion_id is None:
            if self.node_id is None:
                raise ValueError("Parent ID is required to create a new discussion.")
            self.discussion_node = await self.create_discussion_node(self.node_id, self.discussion_type, self.title,
                                                                     self.description, self.model_name)
            self.discussion_id = self.discussion_node['id']
            set_discussion_id(self.discussion_id)
            self.current_step_index = 0
            self.timer.start_discussion(self.discussion_id, self.node_type, self.discussion_type)
        else:
            self.discussion_node = await self.db.get_node_by_id(self.discussion_id)
            self.discussion_type = self.discussion_node['properties'].get('discussion_type', self.discussion_type)
            
            # Only use stored model if no explicit model was set
            # if not self.explicit_model_name:
            #     stored_model = self.discussion_node['properties'].get('model_name')
            #     if stored_model:
            #         self.model_name = stored_model   
         
            self.discussion_so_far = json.loads(
                self.discussion_node['properties'].get('discussion_so_far', '[]'))
            
            if not self.discussion_node:
                raise ValueError("Discussion node not found.")
            self.current_step_index = self.discussion_node['properties'].get('current_step_index', 0)

        # Further initialization steps
        self.modifications_history = self.discussion_node['properties'].get(
            'modifications_history', [])
        if self.modifications_history:
            self.modifications_history = json.loads(self.modifications_history)
        self.modifications = self.discussion_node['properties'].get(
            'modifications', {})
        if self.modifications:
            self.modifications = json.loads(self.modifications)
        self.steps = self.define_steps()
        self.node = await self.db.get_parent_node(self.discussion_id)
        self.node_id = self.node['id']
        self.node_type = Discussion.get_specific_node_type(self.node['labels'])
        self.root_node_type = Discussion.get_root_node_from_node_types(
            self.node['labels'])
        self.root_node = await self.db.get_root_node(self.node_id)
        self.root_node_id = self.root_node['id'] if self.root_node else self.node_id
        self.project_id = self.root_node_id
        self.version_manager.project_id =  self.root_node_id
        
 
        try:
            from app.services.session_tracker import get_session_tracker

            tracker = get_session_tracker()
            print("Project_idd", self.node_id, self.project_id)

            # Determine invocation type
            invocation_type = getattr(self, 'invocation_type', 'interactive')
            celery_task_id_value = celery_task_id.get()



            # Debug logging
            if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                self.session_debug_logger.info("SESSION TRACKING INITIALIZATION")
                self.session_debug_logger.info(f"Invocation Type: {invocation_type}")
                self.session_debug_logger.info(f"Celery Task ID: {celery_task_id_value}")
                self.session_debug_logger.info(f"Discussion ID: {self.discussion_id}")
                self.session_debug_logger.info(f"Project ID: {self.project_id}")
                self.session_debug_logger.info(f"Current User: {self.current_user}")

            if invocation_type == "autoconfig":
                # For auto-configuration, don't create a new session
                # The session is already created in the celery task (app/tasks.py)
                # Just log that we're using the existing session
                print(f"🔗 Auto-configuration discussion {self.discussion_id} using existing celery task session")

                if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                    self.session_debug_logger.info("AUTO-CONFIG: Skipping session creation")
                    self.session_debug_logger.info(f"AUTO-CONFIG: Using existing celery task session: {celery_task_id_value}")
            else:
                # For interactive configuration, create individual session
                service_type = "interactive-configuration"
                session_name = self.title or "Interactive Configuration"

                if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                    self.session_debug_logger.info("INTERACTIVE: Creating new session")
                    self.session_debug_logger.info(f"INTERACTIVE: Service Type: {service_type}")
                    self.session_debug_logger.info(f"INTERACTIVE: Session Name: {session_name}")

                # Initialize session with discussion-specific data
                session_result = await tracker.initialize_session(
                    task_id=str(self.discussion_id),
                    tenant_id=get_tenant_id(),
                    user_id=self.current_user,
                    project_id=self.project_id,
                    service_type=service_type,
                    container_id=None,
                    session_data={
                        "session_name": session_name,
                        "description": self.description or "",
                        "discussion_type": self.discussion_type,
                        "node_type": self.node_type,
                        "node_id": self.node_id,
                        "invocation_type": invocation_type
                    }
                )
                print("Session_result", self.discussion_type)
                if session_result["success"]:
                    print(f"✅ Session tracking initialized for discussion {self.discussion_id}")
                    if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                        self.session_debug_logger.info("INTERACTIVE: Session creation successful")
                else:
                    print(f"⚠️ Failed to initialize session tracking: {session_result.get('error', 'Unknown error')}")
                    if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                        self.session_debug_logger.error(f"INTERACTIVE: Session creation failed: {session_result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"⚠️ Error initializing session tracking: {e}")
            if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                self.session_debug_logger.error(f"SESSION TRACKING ERROR: {e}")

        # existing_session = await get_ingest_session(self.current_user, self.root_node)
        # self.doc_session_id = existing_session.get("session_id")
    
    async def create_discussion_node(self, node_id, discussion_type, title, description, model_name='None'):
        # Placeholder for node creation logic

        properties = {
            'Title': title if title else f"{discussion_type} Discussion",
            'Description': description if description else f'This is an automated {discussion_type} discussion',
            'discussion_type': f'{discussion_type}',
            'created_at': generate_timestamp(),
            'created_by': self.current_user,
            'model_name': model_name,
        }

        discussion_node = await self.db.create_node(["Discussion"], properties, node_id)
        return discussion_node

    @staticmethod
    def get_specific_node_type(node_types):
        if 'RequirementRoot' in node_types:
            return 'RequirementRoot'
        if 'Deployment' in node_types:
            return 'Deployment'
        if 'Epic' in node_types:
            return 'Epic'
        if 'UserStory' in node_types:
            return 'UserStory'
        if 'TechRequirement' in node_types:
            return 'TechRequirement'
        if 'WorkItem' in node_types:
            return 'WorkItem'
        if 'Project' in node_types:
            return 'Project'
        if 'Architecture' in node_types:
            return 'Architecture'
        if 'Interface' in node_types:
            return 'Interface'
        if 'Design' in node_types:
            return 'Design'
        if 'ArchitecturalRequirement' in node_types:
            return 'ArchitecturalRequirement'
        if 'SystemContext' in node_types:
            return 'SystemContext'
        if 'Container' in node_types:
            return 'Container'
        if 'DatabaseModel' in node_types:
            return 'DatabaseModel'
        if 'Database' in node_types:
            return 'Database'
        
       

    @staticmethod
    def get_root_node_from_node_types(node_types):
        if 'Requirement' in node_types:
            return 'Project'
        if 'Architecture' in node_types:
            return 'Project'
        if 'Project' in node_types:
            return 'Project'
        if 'Interface' in node_types:
            return 'Project'
        if 'Design' in node_types:
            return 'Project'
        else:
            return 'Project'

    def define_steps(self):
        # Define the sequence of steps for the discussion
        return [
            {'name': 'initialize', 'response': {'is_streaming': False, 'format': 'json'}, 'hidden': False,
             'interactive': False},
            {'name': 'retrieve_info', 'response': {'is_streaming': False, 'format': 'json'}, 'hidden': True,
             'interactive': False},
            {'name': 'main_discussion', 'response': {'is_streaming': True, 'format': 'text/event-stream'},
             'display': 'Send', 'hidden': False, 'interactive': True},
            # {'name': 'get_modifications_from_discussion', 'response': { 'is_streaming':False, 'format':'json'},'display': 'Capture',  'hidden': False, 'interactive': True},
            {'name': 'merge_captured_items', 'response': {'is_streaming': False, 'format': 'json'}, 'display': 'Merge',
             'hidden': False, 'interactive': False},
            {'name': 'finalize', 'response': {'is_streaming': False, 'format': 'json'}, 'display': 'Close',
             'hidden': False, 'interactive': False}
        ]

    async def initialize(self):
        # This is a dummy function that would allow the next step to be hidden. The first step cannot be hidden.
        pass

    def get_visible_steps(self):
        # Filter steps to only non-hidden ones. This could be used for displaying steps in the UI.
        return [step for step in self.steps if not step['hidden']]


    def find_index_of_step(self, target_step_name):
        for i, step in enumerate(self.steps):
            if step['name'] == target_step_name:
                return i
        return None

    async def execute_to_step(self, target_step_name, user_comment="",  streaming=False):
        if target_step_name not in [step['name'] for step in self.steps]:
            raise ValueError(f"Invalid target step name: {target_step_name}")

        # Ensure that the current_step_index does not get updated before reaching the target step in case it is called multiple times
        target_step_index = self.find_index_of_step(target_step_name)
        print(f"Target step index: {target_step_index}")
        while self.current_step_index < target_step_index:
            step = self.steps[self.current_step_index]
            method = getattr(self, step['name'], None)

            if callable(method):
                print(f"Executing step {step['name']}")
                result = await method()
                self.current_step_index += 1
            
            # Update current step index
            await self.db.update_node_by_id(self.discussion_id, {'current_step_index': self.current_step_index})

        # Handle target step execution (potentially streaming)
        if self.current_step_index == target_step_index:
            step = self.steps[self.current_step_index]
            if streaming:
                method_name = f"{step['name']}"
                method = getattr(self, method_name, None)
                if callable(method):
                    return method(user_comment, True)
                else:
                    raise ValueError(f"No streaming method defined for step: {step['name']}")
            else:
                method = getattr(self, step['name'], None)
                if callable(method):
                    if step['name'] == 'main_discussion':
                        async for response in method(user_comment, False):
                           pass
                    else:
                        result = await method()
                    self.current_step_index += 1
                    # Update current step index
                    await self.db.update_node_by_id(self.discussion_id, {'current_step_index': self.current_step_index})

        next_step_name = self.steps[self.current_step_index]['name'] if self.current_step_index < len(self.steps) else None
        return next_step_name, result

    async def repeat_step(self, step_name, user_comment=None, streaming=False):

        if step_name not in [step['name'] for step in self.steps]:
            raise ValueError(f"Invalid step name: {step_name}")

        self.current_step_index = self.find_index_of_step(step_name)
        
        method_name = f"{step_name}"

        method = getattr(self, method_name, None)
        if not callable(method):
            raise ValueError(f"No method defined for step: {step_name} (streaming={streaming})")

        # Update the current step in the database (before executing the method). This could potentially be placed elsewhere.
        await self.db.update_node_by_id(self.discussion_id, {'current_step_index': self.current_step_index + 1})

        if streaming:  # Streaming case
            return method(user_comment, True)
        else:  # Regular case
            result = await method(user_comment) if user_comment else await method()
            next_step_name = self.steps[self.current_step_index + 1]['name'] if self.current_step_index + 1 < len(self.steps) else None
            return next_step_name, result

    # Helper method to get node information
    async def get_nodes_info(self):
        nodes_in_path = await self.db.get_nodes_in_path(self.node_id, self.root_node_type)
        
        if not nodes_in_path:
            # Case: Current node is the root node
            current_node = await self.db.get_node_by_id(self.node_id)
            if current_node is None:
                raise ValueError(f"No node found with ID {self.node_id}")
            return current_node, current_node, None
        elif len(nodes_in_path) == 1:
            # Case: Only one node in the path
            return nodes_in_path[0], nodes_in_path[0], nodes_in_path
        else:
            # Case: Multiple nodes in the path
            root_node = nodes_in_path[0]
            current_node = nodes_in_path[-1]
            return root_node, current_node, nodes_in_path[1:-1]

    # Helper method to get child nodes
    async def get_child_nodes(self):
        # Get child node types from the data model
        child_node_types = data_model_helper.get_child_node_types(self.node_type)

        child_nodes = []
        for child_type in child_node_types:
            child_nodes.extend(await self.db.get_child_nodes(self.node_id, child_type))
        return child_nodes

    # Helper method to get relevant nodes using vector search
    async def get_relevant_nodes(self, root_node_id, current_node):
        if not root_node_id or not current_node:
            return []
        
        relevant_node_ids = await self.vector_db.find_similar_nodes(
            current_node['id'],
            current_node,
            root_node_id,
            max_results=10
        )
        possible_relevant_nodes = await self.db.get_nodes_by_ids(relevant_node_ids)
        return [node for node in possible_relevant_nodes if self.node_type in node['labels']]
    
    async def get_architectural_requirements(self, root_node_id):
        result = await self.db.get_child_nodes(root_node_id, "ArchitecturalRequirement")
        architectural_requirements_node = result[0] if result else None
        return {
            'architectural_requirements': architectural_requirements_node['properties'].get('architectural_requirements') if architectural_requirements_node else None,
            'functional_requirements': architectural_requirements_node['properties'].get('functional_requirements') if architectural_requirements_node else None
        }

    async def get_interface_information(self, node_id):
        interfaces = await self.db.get_relationships_involving_node(node_id, "INTERFACES_WITH")
        for interface in interfaces:
            if interface['target'] == node_id:
                interface['source_node'] = await self.db.get_node_by_id(interface['source'])
            if interface['source'] == node_id:
                interface['target_node'] = await self.db.get_node_by_id(interface['target'])
            
            interface_node_id = interface['properties'].get('interface_node_id')
            if interface_node_id:
                interface['interface_node'] = await self.db.get_node_by_id(interface_node_id)
        
        return interfaces
    
    async def get_architecture_root(self, project_id):
        """
        Gets ArchitectureRoot using project ID.
        
        Args:
            project_id: ID of the project node
        Returns:
            Architecture root node if found, None otherwise
        """
        try:
            arch_roots = await self.db.get_child_nodes(project_id, "ArchitectureRoot")
            return arch_roots[0] if arch_roots else None
        except Exception as e:
            self.update_logger.error(f"Error getting architecture root: {str(e)}")
            return None

    async def get_architectural_node_info(self, architecture_root_id, architecture_node_id):
        """
        Retrieve common architectural node information.
        
        :param architecture_root_id: ID of the root architecture node
        :param architecture_node_id: ID of the current architecture node
        :return: Dictionary containing additional architectural information
        """
        info = {}

        # Get architecture node 
        arch_node = await self.db.get_node_by_id(architecture_node_id)
        if arch_node:
            # Create sanitized copy without ID
            sanitized_arch = arch_node.copy()
            if 'id' in sanitized_arch:
                del sanitized_arch['id']
            info['architecture_node'] = sanitized_arch

        # Get architectural requirements
        requirements = await self.get_architectural_requirements(architecture_root_id)
        info.update(requirements)

        # Get top levels
        info['top_levels'] = await self.db.get_descendant_nodes(
            architecture_root_id, ["Architecture"], ["Title", "Description", "Functionality"], 3
        )

        # Get relationships
        child_relationships = await self.db.get_all_relationships(self.retrieved_info['root_node']['id'], "HAS_CHILD", "Architecture", 3)
        interface_relationships = await self.db.get_all_relationships(architecture_root_id, "INTERFACES_WITH", "Architecture", 3)
        info['relationships'] = child_relationships + interface_relationships

        # Get interface information
        info['direct_interfaces'] = await self.get_interface_information(architecture_node_id)

        return info
    
    async def retrieve_info(self):
        self.retrieved_info = {}

        # Step 1: Retrieve basic node information
        root_node, current_node, nodes_in_path = await self.get_nodes_info()
        self.retrieved_info.update({
            'root_node': root_node,
            'current_node': current_node,
            'nodes_in_path': nodes_in_path
        })

        # Step 2: Retrieve child and sibling nodes
        self.retrieved_info['child_nodes'] = await self.get_child_nodes()
        self.retrieved_info['sibling_nodes'] = await self.db.get_sibling_nodes(self.node_id, self.node_type)

        # Step 3: Retrieve other relevant nodes using vector search
        self.retrieved_info['other_relevant_nodes'] = await self.get_relevant_nodes(root_node['id'], current_node)

        # Step 4: Retrieve top levels of the tree
        if nodes_in_path:
            self.retrieved_info['top_levels'] = await self.db.get_descendant_nodes(
                root_node['id'], [self.node_type], ["Title"], 2
            )
        else:
            self.retrieved_info['top_levels'] = None
        
        print("self.retrieve_info", self.retrieved_info)
        
        await self.check_reconfiguration_needs()


        return self.retrieved_info


    

    def get_converted_discussion_type(self, discussion_type, node_type):
        # get this from a lookup table

        converted_discussion_type = discussion_type
        if discussion_type == "Requirement":
            converted_discussion_type = node_type

        return converted_discussion_type


    
    def get_config_state(self, discussion_type, node_type, node):
        if discussion_type == "design_details" and node_type == "Architecture":
            return node.get('properties', {}).get('design_detail_state', 'not_configured')
        
        elif discussion_type == "design_details" and node_type == "Interface":
            return node.get('properties', {}).get('design_details_state', 'not_configured')
        
        elif discussion_type == "definition" and node_type == "Interface":
            return node.get('properties', {}).get('definition_state', 'not_configured')
        
        elif discussion_type == "behavior" and node_type == "Design":
            return node.get('properties', {}).get('behavior_config_state', 'not_configured')
        
        elif discussion_type == "component_interactions" and node_type == "Design":
            return node.get('properties', {}).get('component_interactions_config_state', 'not_configured')
        
        elif discussion_type == "testcases" and node_type == "Design":
            return node.get('properties', {}).get('test_cases_config_state', 'not_configured')
        
        elif discussion_type == "classdiagram" and node_type == "Design":
            return node.get('properties', {}).get('class_diagrams_config_state', 'not_configured')
        
        else:
            return node.get('properties', {}).get('configuration_state', 'not_configured')

    def prompt_for_starting_discussion(self):
        print("Template name: ", self.template_name)
        if not self.template_name:
            raise ValueError("template_name must be set before calling prompt_for_starting_discussion")
        
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        templates_path = os.path.join(base_dir, 'discussions', 'prompt_templates')
        
        if getattr(self, 'is_flagged_for_reconfig', False):
            reconfig_template_name = f"{self.template_name.split('.')[0]}_reconfig.prompt"
            try:
                # Just check if the template exists
                with open(os.path.join(templates_path, reconfig_template_name), 'r') as f:
                    # If we get here, template exists
                    self.template_name = reconfig_template_name
                    self.update_logger.info(f"Using reconfiguration template: {self.template_name}")
            except FileNotFoundError:
                # Continue with normal template if reconfig template doesn't exist
                pass


        env = Environment(loader=FileSystemLoader(templates_path))
        agents_config_path = os.path.join(base_dir, 'agents', 'agents.json')
        with open(agents_config_path, 'r') as file:
            ai_agents = json.load(file)

        template = env.get_template(self.template_name)
        
        config = self.current_configuration
        
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)

        function_schema = get_function_schema(self.function_schema_type)

        context = {
            "prompt_type": "user",
            "current_node": self.retrieved_info["current_node"]["labels"],
            "details_for_discussion": self.retrieved_info,
            "root_node_type": self.root_node_type,
            "config": config,
            "ai_agents": ai_agents,
            "config_state": config_state,
            "invocation_type": self.invocation_type,
            "function_schema": function_schema,
            "discussion_type": self.discussion_type,
            "node_type": self.node_type,
            "project_knowledge": self.project_knowledge,
            "session_id":self.session_id,
            "reconfig_flags": getattr(self, 'retrieved_info', {}).get('reconfig_flags', None),
            "triggering_parent": getattr(self, 'retrieved_info', {}).get('triggering_parent', None),
            "model_name": self.model_name
        }

        user_prompt = template.render(context)
        system_prompt = template.render(dict(context, prompt_type="system"))
        return user_prompt, system_prompt, function_schema


    def get_node_structure(self, node_type):

        if node_type not in data_model['model']:
            raise ValueError(f"Node type '{node_type}' not found in data model")

        node_info = data_model['model'][node_type]
        attributes = node_info['attributes']

        structure = {attr.lower(): f"Current or updated {attr}" for attr in attributes}
        return structure

    def generate_function_instruction(self, node_type):

        instruction = f"""
        Interact with the user to get the required field updates and understand the changes clearly
       
       
        """
        return instruction
    
    def load_file_attachments_for_messages(self, messages):
        """
        Load file attachments for a list of messages.
        Returns a new copy of messages with file attachments loaded and formatted.
        """
        loaded_messages = deepcopy(messages)
        
        for message in loaded_messages:
            if 'file_attachments' in message:
                new_message_content = []
                new_message_content.append({"type": "text", "text": message['content']})
                has_image_urls = False
                
                for file_dict in message['file_attachments']:
                    file = FileAttachment(**file_dict)  # Convert dict to FileAttachment object
                    if file.file_type.startswith('image/'):
                        has_image_urls = True
                        # For image files, add an image_url entry
                        new_message_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": file.original_url,  # Assuming image_url is stored in FileAttachment
                            }
                        })
                    else:
                        # For non-image files, append the content to the text
                        new_message_content[0]["text"] += f"\n\nFile: {file.file_name}\nType: {file.file_type}\nSize: {file.file_size} bytes\nContents:\n{file.extracted_content}\n\n"
                
                if has_image_urls:       
                    message['content'] = new_message_content
                else:
                    message['content'] = new_message_content[0]["text"]
                
                # Remove the file_attachments key from the message
                del message['file_attachments']

        return loaded_messages
    
    def validate_response_old(stream,data):
        # print("Verification..")
        """
        This function validates if the response from the orchestrator is valid.
        If we get invalid parameters we give a chance to the orchestrator to correct them.

        return bool, string - bool is whether the response is valid, string is the error message
        """
        required_keys = ['field_for_testing']
        
        try:
            if stream:
                response = json.loads(data)
            else:
                response = json.loads(data.choices[0].message.content)
        except json.JSONDecodeError:
            return False, "Response is not a valid JSON"

        # Check if all required keys are in the response
        missing_keys = [key for key in required_keys if key not in response]
        if missing_keys:
            return False, f"Required keys are missing: {', '.join(missing_keys)}"

        status = response["status"]
        project = response["project"]

        # Validate status
        if not isinstance(status, str):
            return False, "Status must be a string"

        # Validate project
        if not isinstance(project, dict):
            return False, "Project must be a dictionary"

        # You can add more specific validations for the project structure here
        # For example:
        # required_project_keys = ['name', 'description']
        # missing_project_keys = [key for key in required_project_keys if key not in project]
        # if missing_project_keys:
        #     return False, f"Required project keys are missing: {', '.join(missing_project_keys)}"

        # print("Verification..")
        # All good
        return True, ""

    def validate_response(self,stream, data_str):
        # Define the restricted keys (all keys except modified_node)
        restricted_keys = {
            "new_child_nodes",
            "modified_children",
            "modified_siblings",
            "modified_similar_nodes",
            "new_relationships",
            "modified_relationships",
            "new_relationship_types",
            "created_at"
        }
        
        try:
            # Extract the modified_node content
            modified_node = data_str["content"]["modifications"][0]["modified_node"]
            
            # Get all keys from modified_node (including nested ones)
            def get_all_keys(obj):
                keys = set()
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        keys.add(key)
                        # Recursively get keys from nested dictionaries
                        keys.update(get_all_keys(value))
                elif isinstance(obj, list):
                    # Handle lists by checking their elements
                    for item in obj:
                        keys.update(get_all_keys(item))
                return keys
            
            all_modified_node_keys = get_all_keys(modified_node)
            
            # Find any restricted keys that appear in modified_node
            invalid_keys = restricted_keys.intersection(all_modified_node_keys)
            print("Checking validation conditions")
            
            if invalid_keys:
                print(f"Found restricted keys in modified_node: {invalid_keys}")
                return False, f"Found restricted keys in modified_node: {invalid_keys}"
            else:
                print("No restricted keys found in modified_node")
                return True, "No restricted keys found in modified_node"
                
        except Exception as e:
            return False, f"Error processing data: {str(e)}"

    def validate_llm_response(self, schema, llm_arguments, options):
        try:
            """
            Validate if the data types of each key inside modified_node match the schema,
            and optionally check if all required fields are present.
            First validates that modified_node itself is an object type, then validates its contents.
            Works with both old and new schema formats.
            
            Args:
                schema (list or dict): The schema definition containing type information and required fields
                output_data (dict): The output data to validate
                required_field_check (bool): Flag to check if all required fields are present
                
            Returns:
                dict: Validation result with success status and any errors
            """

            required_fields_check = False
            
            if isinstance(schema, list) and schema:
                schema = schema[0]

            if options and 'required_fields_check' in options:
                required_fields_check = options['required_fields_check']

            result = {
                "is_valid": True,  # Initialize as True and set to False on any error
                "errors": []
            }
            
            # Extract the actual schema from list or direct format
            schema_obj = None
            if isinstance(schema, list) and len(schema) > 0:
                # New format - schema is a list of objects
                for item in schema:
                    if "function" in item and "parameters" in item["function"]:
                        schema_obj = item
                        break
            elif isinstance(schema, dict) and "function" in schema:
                # Old format - schema is a direct object
                schema_obj = schema
            
            if not schema_obj:
                result["is_valid"] = False
                result["errors"].append({
                    "path": "",
                    "message": "Invalid schema format or empty schema"
                })
                return result
            
            # Now extract the actual parameters schema
            params_schema = schema_obj["function"]["parameters"] if "function" in schema_obj else None
            if not params_schema:
                result["is_valid"] = False
                result["errors"].append({
                    "path": "",
                    "message": "Could not find parameters schema in the provided schema"
                })
                return result
            
            def check_type(value, expected_type):
                """
                Check if the value matches the expected type
                
                Args:
                    value: The value to check
                    expected_type (str): The expected type as a string
                    
                Returns:
                    bool: True if the type matches, False otherwise
                """
                if expected_type == "string":
                    return isinstance(value, str)
                elif expected_type == "number":
                    return isinstance(value, (int, float))
                elif expected_type == "integer":
                    return isinstance(value, int)
                elif expected_type == "boolean":
                    return isinstance(value, bool)
                elif expected_type == "array":
                    return isinstance(value, list)
                elif expected_type == "object":
                    return isinstance(value, dict)
                elif expected_type == "null":
                    return value is None
                return True  # Unknown type, assume valid
            
            def validate_object(schema_props, data_obj, path=""):
                """
                Recursively validate an object's properties against the schema
                
                Args:
                    schema_props (dict): Schema properties definition
                    data_obj (dict): The object to validate
                    path (str): Current path for error reporting
                """
                # If there's no data_obj but we expect an object, that's an error
                if data_obj is None:
                    result["is_valid"] = False
                    result["errors"].append({
                        "path": path,
                        "message": f"Expected object at {path}, but got None"
                    })
                    return
                
                # Check if schema has properties defined
                if "properties" not in schema_props:
                    return
                    
                # Check required fields if needed
                if required_fields_check and "required" in schema_props:
                    for field in schema_props["required"]:
                        if field not in data_obj:
                            result["is_valid"] = False
                            result["errors"].append({
                                "path": f"{path}.{field}" if path else field,
                                "message": f"Required field '{field}' is missing at {path}"
                            })
                
                # Check each property defined in the schema
                for key, prop_schema in schema_props["properties"].items():
                    prop_path = f"{path}.{key}" if path else key
                    
                    # Skip if property doesn't exist in data
                    if key not in data_obj:
                        continue
                        
                    value = data_obj[key]
                    
                    # Validate type
                    if "type" in prop_schema and not check_type(value, prop_schema["type"]):
                        result["is_valid"] = False
                        result["errors"].append({
                            "path": prop_path,
                            "message": f"Invalid type for {prop_path}. Expected {prop_schema['type']}, got {type(value).__name__}"
                        })
                    
                    # If this is an object, recursively validate its properties
                    if "type" in prop_schema and prop_schema["type"] == "object" and "properties" in prop_schema:
                        validate_object(prop_schema, value, prop_path)
            
            # Extract modified_node schema
            if "properties" in params_schema and "modified_node" in params_schema["properties"]:
                modified_node_schema = params_schema["properties"]["modified_node"]
            else:
                result["is_valid"] = False
                result["errors"].append({
                    "path": "",
                    "message": "Could not find modified_node schema in the parameters schema"
                })
                return result
            
            # Find and validate the modified_node within the output data structure
            found_modified_node = False
            
            # Structure 1: Complex nested structure
            if "content" in llm_arguments and "modifications" in llm_arguments["content"]:
                for mod in llm_arguments["content"]["modifications"]:
                    if "modified_node" in mod:
                        found_modified_node = True
                        # Check that modified_node itself is an object type
                        if not isinstance(mod["modified_node"], dict):
                            result["is_valid"] = False
                            result["errors"].append({
                                "path": "modified_node",
                                "message": f"Invalid type for modified_node. Expected object, got {type(mod['modified_node']).__name__}"
                            })
                        else:
                            # Validate the structure of modified_node
                            validate_object(modified_node_schema, mod["modified_node"], "modified_node")
                        break
            
            # Structure 2: Direct modified_node and reason_for_this_call
            elif "modified_node" in llm_arguments:
                found_modified_node = True
                # Check that modified_node itself is an object type
                if not isinstance(llm_arguments["modified_node"], dict):
                    result["is_valid"] = False
                    result["errors"].append({
                        "path": "modified_node",
                        "message": f"Invalid type for modified_node. Expected object, got {type(llm_arguments['modified_node']).__name__}"
                    })
                else:
                    # Validate the structure of modified_node
                    validate_object(modified_node_schema, llm_arguments["modified_node"], "modified_node")
                
                # Validate all top-level fields from the schema (not just modified_node)
                for field_name, field_schema in params_schema["properties"].items():
                    if field_name != "modified_node" and field_name in llm_arguments:
                        # Check top-level field types directly
                        if "type" in field_schema and not check_type(llm_arguments[field_name], field_schema["type"]):
                            result["is_valid"] = False
                            result["errors"].append({
                                "path": field_name,
                                "message": f"Invalid type for {field_name}. Expected {field_schema['type']}, got {type(llm_arguments[field_name]).__name__}"
                            })
                        # For objects with properties, also validate their structure
                        if field_schema.get("type") == "object" and "properties" in field_schema:
                            validate_object(field_schema, llm_arguments[field_name], field_name)
            
            # If we never found the modified_node, that's an error
            if not found_modified_node:
                result["is_valid"] = False
                result["errors"].append({
                    "path": "",
                    "message": "Could not find 'modified_node' in the output data structure"
                })
            
            # Check if all required top-level fields from the schema are present in the output
            if required_fields_check and "required" in params_schema:
                for required_field in params_schema["required"]:
                    # For modified_node, we've already checked its presence
                    if required_field == "modified_node":
                        continue
                        
                    # For direct structure, check if field exists at top level
                    if not (required_field in llm_arguments or 
                        (required_field == "modified_node" and found_modified_node)):
                        result["is_valid"] = False
                        result["errors"].append({
                            "path": required_field,
                            "message": f"Required top-level field '{required_field}' is missing"
                        })
            
            print(result)
            return result.get('is_valid', False), json.dumps(result.get('errors', '[]'))
        except Exception as e:
            import traceback
            return False, f"Validation error: {str(e)},\n {''.join(traceback.format_exc())}"


    def serialize_message(self,message):
        """Convert message objects to JSON serializable format"""
        if isinstance(message, dict):
            return {k: self.serialize_message(v) for k, v in message.items()}
        elif isinstance(message, list):
            return [self.serialize_message(item) for item in list(message)]
        elif hasattr(message, 'model_dump'):  # For Pydantic models
            return message.model_dump()
        elif hasattr(message, '__dict__'):  # For custom objects
            return {k: self.serialize_message(v) for k, v in message.__dict__.items() 
                    if not k.startswith('_')}
        elif isinstance(message, (str, int, float, bool, type(None))):
            return message
        else:
            return str(message)  # Fallback for other types


    async def main_discussion(self, user_comment=None, stream=False):
        """
        Router method that selects between main_discussion_with_knowledge and 
        main_discussion_capture_only based on self.project_knowledge value.
        
        Args:
            user_comment (str, optional): The user's input message
            stream (bool, default=False): Whether to stream the response
            
        Yields:
            Response chunks (for both streaming and non-streaming cases)
        """
        # Check if retrieved_info is empty and call retrieve_info if needed
        if not hasattr(self, 'retrieved_info') or not self.retrieved_info:
            self.update_logger.info("Retrieved info is empty, calling retrieve_info")
            await self.retrieve_info()
            
        # Get search criteria for current discussion type
        self.project_knowledge = None

        self.tenant_id = get_tenant_id()

        # Construct S3 key for project definition
        s3_key = f"extracted-docs-{self.tenant_id}/project-{self.project_id}/digested-files"
        print(f"Checking S3 path: {s3_key}")

        bucket_name = get_tenant_bucket(self.tenant_id)
        try:
            # List objects in the directory
            response = self.s3_client.list_objects_v2(
                Bucket=bucket_name,
                Prefix=s3_key
            )

            # Check if any files start with doc_
            if 'Contents' in response:
                for item in response['Contents']:
                    file_name = os.path.basename(item['Key'])
                    if file_name.startswith('project_definition.json'):
                        self.project_knowledge = True
                        if getattr(self, 'invocation_type', 'interactive') == "autoconfig" :
                            self.model_name = LLMModel.gpt_4_1.value
                        else:
                            self.model_name = 'claude-3-5-sonnet-20241022'
                        print(f"Found project_definition file in S3: {file_name}")
                        break
                
                if not self.project_knowledge:
                    print("No doc_ files found in the directory")
            else:
                self.project_knowledge = False
                print("No files found in the directory")

        except Exception as e:
            self.project_knowledge = False
            print(f"Error checking S3: {str(e)}")
            
        # Select appropriate discussion method
        if self.project_knowledge:
            selected_method = self.main_discussion_with_knowledge
        elif self.session_id:
            selected_method = self.main_discussion_with_codebase
        else:
            selected_method = self.main_discussion_capture_only



        async for chunk in selected_method(user_comment, stream):
            yield chunk

    async def main_discussion_with_knowledge(self, user_comment=None, stream=False):
        """
        Main discussion method with robust tool call handling and streaming response.
        
        Args:
            user_comment (str, optional): User's input message
            stream (bool, default=False): Whether to stream the response
            
        Yields:
            Streaming response chunks
        """
        try:
            # Use strict session ID resolver for streaming
            session_id_for_llm = self.get_strict_session_id("streaming")

            # Retain existing LLM interface initialization
            self.llm = LLMInterface(get_path(), 'discussion', self.current_user, self.root_node_id,
                                self.discussion_type,mongo_handler=get_mongo_db(), session_id=session_id_for_llm)

            # Existing discussion initialization logic
            if not self.discussion_so_far:  # If starting a new discussion
                user_prompt, system_prompt, self.function_schema = self.prompt_for_starting_discussion()
                messages = []
                user_message = {'role': 'user', 'content': user_prompt}
                if user_message not in messages :
                    messages.append(user_message)
                messages.append({'role': 'system', 'content': system_prompt})
            else:  # Continue existing discussion
                messages = self.discussion_so_far
                user_prompt = user_comment
                system_prompt = None
                self.function_schema = get_function_schema(self.function_schema_type)
                user_message = {'role': 'user', 'content': user_prompt}
            
            # File attachments handling
            if self.file_attachments:
                new_message_content = []
                new_message_content.append({"type": "text", "text": user_comment})
                
                for file in self.file_attachments:
                    if file.file_type.startswith('image/'):
                        self.has_image_urls = True
                        new_message_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": file.original_url,
                            }
                        })
                    else:
                        new_message_content[0]["text"] += f"\n\nFile: {file.file_name}\nType: {file.file_type}\nContents:\n{file.extracted_content}\n\n"
                
                if self.has_image_urls:       
                    user_message['content'] = new_message_content
                else:
                    user_message['content'] = new_message_content[0]["text"]

            # Prepare messages
            loaded_messages = self.load_file_attachments_for_messages(self.discussion_so_far)
            if user_message not in loaded_messages:
                loaded_messages.append(user_message) 
            if user_message not in messages:
                messages.append(user_message)        
            
            # Logging
            self.update_logger.info(f"Nodetype: {self.node_type}, Discussion type: {self.discussion_type}, invocation_type: {self.invocation_type}")
            for message in loaded_messages:
                self.update_logger.info(f"Message: {message}")
            
            schema_display = json.dumps(self.function_schema, indent=4)
            self.update_logger.info(f"Function schema: {schema_display}")

            # Initialize response tracking
            llm_response = {
                'role': 'assistant',
                'content': '',
                'is_stream_complete': False
            }

            # Prepare LLM call
            await self.initialize_tools_and_schemas()
            schemas = self.get_function_schemas()
            if not schemas:
                raise ValueError("Function schemas failed to initialize")

            cp = messages.copy()
            cleaned_cp = [msg for msg in cp if msg.get('content') not in [None, '']]
            # Make LLM call
            response = await self.llm.llm_interaction_wrapper(
                messages=cleaned_cp,
                user_prompt=None,
                system_prompt=system_prompt,
                response_format={'type': 'text'},
                model=self.model_name,
                stream=stream,
                function_schemas=schemas,
                function_executor=self.get_function_executors(),
                validation_function=self.validate_llm_response,
                tools_for_validation=['capture_discussion_output']
            )

            if stream:
                content_buffer = ""
                tool_call_buffer = None
                llm_response = {
                    'role': 'assistant',
                    'content': '',
                    'is_stream_complete': False
                }

                async for chunk in response:
                    try:
                        # Handle dictionary chunks (usually tool calls or special messages)
                        if isinstance(chunk, dict):
                            print(f"DEBUG - Received chunk type: {type(chunk)}")
                            print(f"DEBUG - Chunk content: {chunk}")
                            
                            # Case 1: Function call
                            if 'function_call' in chunk:
                                # Keep original structure
                                tool_call_buffer = {
                                    'id': str(chunk['function_call'].get('id')),
                                    'type': 'function',
                                    'function': {
                                        'name': chunk['function_call'].get('name'),
                                        'arguments': chunk['function_call'].get('arguments')
                                    }
                                }
                                print(f"DEBUG - Tool call buffer: {tool_call_buffer}")
                                yield f"data: {json.dumps({'function_call': tool_call_buffer})}\n\n"
                                
                            # Case 2: Function response
                            elif 'content' in chunk and isinstance(chunk['content'], dict):
                                yield f"data: {json.dumps(chunk['content'])}\n\n"
                                content_buffer = '\n\n**If you are okay with the modifications, please proceed with merge.**\n\n'
                                llm_response['content'] = llm_response['content'] + content_buffer
                                
                            # Case 3: Other dictionary formats
                            else:
                                yield f"data: {json.dumps(chunk)}\n\n"
                                
                        # Handle string chunks (normal content streaming)
                        else:
                            content_buffer += chunk
                            llm_response['content'] = content_buffer
                            yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                            
                    except Exception as e:
                        print(f"DEBUG - Error in streaming loop: {str(e)}")
                        yield f"data: {json.dumps({'error': str(e)})}\n\n"

                # Stream is complete
                llm_response['is_stream_complete'] = True
                
                # Format messages for history
                try:
                    print("DEBUG - Starting message formatting")
                    
                    # Handle last user message
                    last_message = messages.pop()
                    print(f"DEBUG - Last user message: {last_message}")
                    
                    if self.file_attachments:
                        last_message['file_attachments'] = [file.model_dump() for file in self.file_attachments]
                        last_message['content'] = user_prompt
                    
                    # Create assistant message
                    assistant_message = {
                        'role': 'assistant',
                        'created_at': generate_timestamp(),
                        'content': content_buffer
                    }
                    
                    # Process all messages to ensure tool calls are properly formatted
                    print("DEBUG - Processing messages for tool calls")
                    for msg in messages:
                        if 'tool_calls' in msg:
                            print(f"DEBUG - Found tool calls: {msg['tool_calls']}")
                            msg['tool_calls'] = [
                                {
                                    'id': str(tool_call.get('id')),
                                    'type': tool_call.get('type', 'function'),
                                    'function': {
                                        'name': tool_call.get('function', {}).get('name'),
                                        'arguments': tool_call.get('function', {}).get('arguments')
                                    }
                                } if isinstance(tool_call, dict) else {
                                    'id': str(tool_call.id) if hasattr(tool_call, 'id') else None,
                                    'type': str(tool_call.type) if hasattr(tool_call, 'type') else 'function',
                                    'function': {
                                        'name': str(tool_call.function.name) if hasattr(tool_call.function, 'name') else None,
                                        'arguments': str(tool_call.function.arguments) if hasattr(tool_call.function, 'arguments') else None
                                    }
                                }
                                for tool_call in msg['tool_calls']
                            ]
                            print(f"DEBUG - Processed tool calls: {msg['tool_calls']}")
                    
                    # Update message history
                    messages.append(last_message)
                    if tool_call_buffer:
                        assistant_message['tool_calls'] = [tool_call_buffer]
                    messages.append(assistant_message)
                    
                    print("DEBUG - Final messages structure:")
                    for msg in messages:
                        print(f"Message type: {type(msg)}")
                        print(f"Message keys: {msg.keys()}")
                    
                    self.discussion_so_far = messages
                    # In the main_discussion method, modify the message handling:
                    messages = [self.serialize_message(msg) for msg in messages]
                    # Update discussion node
                    formatted_output_text = f"LLM response:\n {content_buffer}"
                    await self.update_discussion_node(
                        self.discussion_id,
                        formatted_output_text=formatted_output_text,
                        updated_discussion=[self.serialize_message(msg) for msg in self.discussion_so_far]
                    )
                    
                    yield f"data: {json.dumps(llm_response)}\n\n"
                    
                except Exception as e:
                    print(f"DEBUG - Error finalizing stream response: {str(e)}")
                    print(f"DEBUG - Full traceback:")
                    import traceback
                    traceback.print_exc()
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"

            else:
                # Non-streaming response handling
                llm_response['content'] = response
                messages.append({
                    'role': 'assistant',
                    'created_at': generate_timestamp(),
                    'content': response
                })
                
                self.discussion_so_far = messages
                formatted_output_text = f"LLM response:\n {response}"
                
                await self.update_discussion_node(
                        self.discussion_id,
                        formatted_output_text=formatted_output_text,
                        updated_discussion=[self.serialize_message(msg) for msg in self.discussion_so_far]
                    )
                
                yield response

        except Exception as e:
            error_msg = f"Error in main_discussion: {str(e)}"
            self.update_logger.error(error_msg)
            import traceback
            traceback.print_exc()
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
            raise
       
       
    async def main_discussion_with_codebase(self, user_comment=None, stream=False):
        """
        Main discussion method for codebase knowledge when session_id is available.
        
        Args:
            user_comment (str, optional): User's input message
            stream (bool, default=False): Whether to stream the response
            
        Yields:
            Streaming response chunks
        """
        print("Initializing LLM Wrapper with codebase knowledge")
                                           
        if self.session_id:
            session_id = self.session_id
            self.update_logger.info(f"session id +++ :  {session_id}")
            self.update_logger.info(f"Session initialized : ")
            self.codebase_knowledge = True
            print(f"\n🔑 Using session: {session_id}")

            # Use strict session ID resolver for knowledge queries
            session_id_for_llm = self.get_strict_session_id("knowledge_query")

            self.llm = LLMInterface(
                str(get_path()),
                'knowledge',
                self.current_user,
                int(self.project_id),
                'code_query',
                mongo_handler=get_mongo_db(),
                session_id=session_id_for_llm
            )
            print("after self.llm")

            if not self.discussion_so_far:
                messages = []
                user_prompt, system_prompt, self.function_schema = self.prompt_for_starting_discussion()
                user_message = {'role': 'user', 'content': user_prompt}
                if user_message not in messages:
                    messages.append(user_message)
                messages.append({
                    'role': 'system', 
                    'content': "All analysis and responses will be based strictly on actual codebase content"
                })
                self.update_logger.info(f"Message: {messages}")
                user_message = {'role': 'user', 'content': user_prompt}
                self.update_logger.info(f"user_message : {user_message}")
            else:
                messages = self.discussion_so_far
                user_prompt = user_comment
                system_prompt = None
                user_message = {'role': 'user', 'content': user_prompt}
                if user_message not in messages:
                    messages.append(user_message)
                
                self.update_logger.info(f"Nodetype: {self.node_type}, Discussion type: {self.discussion_type}, invocation_type: {self.invocation_type}")
                self.update_logger.info(f"Message: {messages}")
                user_message = {'role': 'user', 'content': user_prompt}
                self.update_logger.info(f"user_message : {user_message}")
                
                

            # Initialize both tool registries
            general_registry = ToolRegistry()
            general_registry.register_tool("KgTools", KgTools)
            general_registry.register_tool("DiscussionTools", DiscussionTools)
            general_factory = DynamicToolFactory(general_registry)
            
            # Create both tool instances
            kg_exec_agent = general_factory.create_dynamic_tool(["KgTools"])
            discussion_exec_agent = general_factory.create_dynamic_tool(["DiscussionTools"])
            
            kg_tool = kg_exec_agent('/test', logger=None, user_id=session_id)
            discussion_tool = discussion_exec_agent('/test', logger=None, discussion=self)

            # Combine schemas
            combined_schemas = kg_tool.function_schemas + discussion_tool.function_schemas
            self.update_logger.info(f"combined_schemas : {combined_schemas}")

            # Get function mappings from both tools
            kg_functions = kg_tool.function_mapping
            discussion_functions = discussion_tool.function_mapping

            # Combine function mappings
            combined_functions = {**kg_functions, **discussion_functions}

            async def combined_executor(function_name, function_args):
                print(f"Executing function: {function_name}")
                print(f"With arguments: {function_args}")
                
                try:
                    if function_name in combined_functions:
                        func = combined_functions[function_name]
                        try:
                            result = func(**function_args)
                            
                            # Handle async functions
                            if inspect.iscoroutine(result):
                                result = await result
                                
                            if isinstance(result, dict):
                                return result
                            elif result is None:
                                return {"status": "success", "value": None}
                            else:
                                return {"status": "success", "value": str(result)}
                                
                        except Exception as exec_error:
                            print(f"Error executing function {function_name}: {str(exec_error)}")
                            return {
                                "status": "ERROR",
                                "error": str(exec_error),
                                "function": function_name
                            }
                    else:
                        raise ValueError(f"Unknown function: {function_name}")
                        
                except Exception as e:
                    print(f"Error in combined executor: {str(e)}")
                    return {
                        "status": "ERROR",
                        "error": str(e),
                        "function": function_name
                    }

            try:
                print(f"Available functions: {list(combined_functions.keys())}")
                self.update_logger.info(f"Available functions: {list(combined_functions.keys())}")
                cleaned_message = [msg for msg in messages.copy() if msg.get('content') not in [None, '']]
                response = await self.llm.llm_interaction_wrapper(
                    messages=cleaned_message,
                    user_prompt=None,
                    system_prompt=system_prompt,
                    response_format={'type': 'text'},
                    # model=self.model_name,
                    model= 'claude-3-5-sonnet-20241022',
                    stream=stream,
                    function_schemas=combined_schemas,
                    function_executor=combined_executor,
                    validation_function=self.validate_llm_response,
                    tools_for_validation=['capture_discussion_output']
                )
                print("after llm call")

                llm_response = {
                    'role': 'assistant',
                    'content': '',
                    'is_stream_complete': False
                }
                if stream:
                    content_buffer = ""
                    tool_call_buffer = None
                    llm_response = {
                        'role': 'assistant',
                        'content': '',
                        'is_stream_complete': False
                    }

                    async for chunk in response:
                        try:
                            if isinstance(chunk, dict):
                                print(f"DEBUG - Received chunk type: {type(chunk)}")
                                print(f"DEBUG - Chunk content: {chunk}")
                                
                                # Case 1: Function call
                                if 'function_call' in chunk:
                                    tool_call_buffer = {
                                        'id': str(chunk['function_call'].get('id')),
                                        'type': 'function',
                                        'function': {
                                            'name': chunk['function_call'].get('name'),
                                            'arguments': chunk['function_call'].get('arguments')
                                        }
                                    }
                                    yield f"data: {json.dumps({'function_call': tool_call_buffer})}\n\n"
                                
                                # Case 2: Function response
                                elif 'content' in chunk and isinstance(chunk['content'], dict):
                                    if 'modifications' in chunk['content']:
                                        yield f"data: {json.dumps({'modifications': chunk['content']['modifications']})}\n\n"
                                    else:
                                        yield f"data: {json.dumps(chunk['content'])}\n\n"
                                    content_buffer = '\n\n**If you are okay with the modifications, please proceed with merge.**\n\n'
                                    llm_response['content'] = llm_response['content'] + content_buffer
                                
                                # Case 3: Other dictionary formats
                                else:
                                    yield f"data: {json.dumps(chunk)}\n\n"
                            
                            # Handle string chunks
                            else:
                                content_buffer += str(chunk)
                                llm_response['content'] = content_buffer
                                yield f"data: {json.dumps({'content': content_buffer})}\n\n"
                                
                        except Exception as e:
                            print(f"DEBUG - Error in streaming loop: {str(e)}")
                            yield f"data: {json.dumps({'error': str(e)})}\n\n"

                    # Stream is complete
                    llm_response['is_stream_complete'] = True
                    
                    try:
                        print("DEBUG - Starting message formatting")
                        
                        # Handle last user message
                        last_message = messages.pop()
                        print(f"DEBUG - Last user message: {last_message}")
                        
                        if self.file_attachments:
                            last_message['file_attachments'] = [file.model_dump() for file in self.file_attachments]
                            last_message['content'] = user_prompt
                        
                        # Create assistant message
                        assistant_message = {
                            'role': 'assistant',
                            'created_at': generate_timestamp(),
                            'content': content_buffer
                        }
                        
                        # Process messages for tool calls
                        for msg in messages:
                            if 'tool_calls' in msg:
                                msg['tool_calls'] = [
                                    {
                                        'id': str(tool_call.get('id')),
                                        'type': tool_call.get('type', 'function'),
                                        'function': {
                                            'name': tool_call.get('function', {}).get('name'),
                                            'arguments': tool_call.get('function', {}).get('arguments')
                                        }
                                    } if isinstance(tool_call, dict) else {
                                        'id': str(tool_call.id) if hasattr(tool_call, 'id') else None,
                                        'type': str(tool_call.type) if hasattr(tool_call, 'type') else 'function',
                                        'function': {
                                            'name': str(tool_call.function.name) if hasattr(tool_call.function, 'name') else None,
                                            'arguments': str(tool_call.function.arguments) if hasattr(tool_call.function, 'arguments') else None
                                        }
                                    }
                                    for tool_call in msg['tool_calls']
                                ]
                        
                        # Update message history
                        messages.append(last_message)
                        if tool_call_buffer:
                            assistant_message['tool_calls'] = [tool_call_buffer]
                        messages.append(assistant_message)
                        
                        self.discussion_so_far = messages
                        # Update discussion node
                        formatted_output_text = f"LLM response:\n {content_buffer}"
                        await self.update_discussion_node(
                            self.discussion_id,
                            formatted_output_text=formatted_output_text,
                            updated_discussion=[self.serialize_message(msg) for msg in self.discussion_so_far]
                        )
                        
                        yield f"data: {json.dumps(llm_response)}\n\n"
                        
                    except Exception as e:
                        print(f"DEBUG - Error finalizing stream response: {str(e)}")
                        print(f"DEBUG - Full traceback:")
                        import traceback
                        traceback.print_exc()
                        yield f"data: {json.dumps({'error': str(e)})}\n\n"

            #     if stream:
            #         content_buffer = ""
            #         async for chunk in response:
            #             print("chunk type", type(chunk))
            #             print(f"Processing chunk: {chunk}")  # Debug log
            #             try:
            #                 if isinstance(chunk, dict):
            #                     print("inside instance dictionary block---")
            #                     # Handle function calls or special messages
            #                     if 'function_call' in chunk:
            #                         yield f"data: {json.dumps(chunk)}\n\n"
            #                     elif 'content' in chunk:
            #                         print("content block")
            #                         content_buffer += str(chunk['content'])
            #                         yield f"data: {json.dumps({'content': content_buffer})}\n\n"
            #                     else:
            #                         print("elsesss block")
            #                         # Handle other dictionary types
            #                         yield f"data: {json.dumps(chunk)}\n\n"
            #                 else:
            #                     print("string chunk block of")
            #                     # Handle string chunks
            #                     content_buffer += str(chunk)
            #                     yield f"data: {json.dumps({'content': content_buffer})}\n\n"
            #             except Exception as e:
            #                 print(f"Error processing chunk: {e}")
            #                 continue

            #         # Update discussion history
            #         messages.append({
            #             'role': 'assistant',
            #             'created_at': generate_timestamp(),
            #             'content': content_buffer
            #         })
            #         self.discussion_so_far = messages

            #         # Update discussion node
            #         await self.update_discussion_node(
            #             self.discussion_id,
            #             formatted_output_text=f"LLM response:\n {content_buffer}",
            #             updated_discussion=self.discussion_so_far
            #         )

            #     else:
            #         # Non-streaming response handling
            #         print("Non-streaming response received")
            #         print(f"Response content: {response}")
            #         yield response

            except Exception as e:
                error_msg = f"Error in code query discussion: {str(e)}"
                print(error_msg)
                yield f"data: {json.dumps({'error': error_msg})}\n\n"
                return
                    
               

    async def main_discussion_capture_only(self, user_comment=None, stream=False):
        print("Initialising llm Wrapper")

        # Use strict session ID resolver
        session_id_for_llm = self.get_strict_session_id("main_discussion_capture_only")

        self.llm = LLMInterface(get_path(), 'discussion', self.current_user, self.root_node_id, self.discussion_type,mongo_handler=get_mongo_db(), session_id = session_id_for_llm)
        if not self.discussion_so_far:  # If starting a new discussion
            user_prompt, system_prompt, self.function_schema = self.prompt_for_starting_discussion()
            messages = []
            user_message = {'role': 'user', 'content': user_prompt}
            if user_message not in messages:
                messages.append( user_message )
            messages.append({'role': 'system', 'content': system_prompt})
        else:  # Continue existing discussion
            messages = self.discussion_so_far
            user_prompt = user_comment
            system_prompt = None
            self.function_schema = get_function_schema(self.function_schema_type)
            user_message = {'role': 'user', 'content': user_prompt}
            
        print(self.discussion_type)
        
        
        if self.file_attachments:
            # Prepare the new message content
            new_message_content = []
            new_message_content.append({"type": "text", "text": user_comment})
            
            # Process file attachments
        
            for file in self.file_attachments:
                if file.file_type.startswith('image/'):
                    self.has_image_urls = True
                    # For image files, add an image_url entry
                    new_message_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": file.original_url,
                        }
                    })
                else:
                    # For non-image files, append the content to the text
                    new_message_content[0]["text"] += f" Therse\n\nFile: {file.file_name}\nType: {file.file_type}\nContents:\n{file.extracted_content}\n\n"
            
            if  self.has_image_urls:       
                user_message['content'] = new_message_content
            else:
                user_message['content'] = new_message_content[0]["text"]
            # Add the new message to the messages list
            

        loaded_messages = self.load_file_attachments_for_messages(self.discussion_so_far)
        if user_message not in loaded_messages:
            loaded_messages.append(user_message) 
        if user_message not in messages:    
            messages.append(user_message)        
        self.update_logger.info(f"Nodetype: {self.node_type}, Discussion type: {self.discussion_type}, invocation_type: {self.invocation_type}")
        for message in loaded_messages:
            self.update_logger.info(f"Message: {message}")
        
        schema_display = json.dumps(self.function_schema, indent=4)
        self.update_logger.info(f"Function schema: {schema_display}")

        if self.function_schema:
            self.tool = exec_agent('/test', logger=None, discussion=self)

        llm_response = {'role': 'assistant', 'content': '', 'is_stream_complete': False}
        cleaned_loaded_messages = [msg for msg in loaded_messages if msg.get('content') not in [None, '']]
        try:
            response = await self.llm.llm_interaction_wrapper(
                messages=cleaned_loaded_messages,
                user_prompt=None,
                system_prompt=system_prompt,
                response_format={'type': 'text'},
                model=self.model_name,
                stream=stream,
                function_schemas=self.tool.function_schemas if self.function_schema else None,
                function_executor=self.tool.function_executor if self.function_schema else None,
                validation_function=self.validate_llm_response,
                validation_function_options={"required_fields_check":False},
                tools_for_validation=['capture_discussion_output']
            )
            
            if stream:
                async for chunk in response:
                    if isinstance(chunk, dict):
                        # if 'content' in chunk and 'modifications' in chunk['content']:
                        #     # Log validation result for debugging
                        #     is_valid, error_msg = self.validate_llm_response(True, chunk)
                        #     if not is_valid:
                        #         self.update_logger.warning(f"Diagram validation failed: {error_msg}")
                        # Function call result: yield individual messages
                        yield f"data: {json.dumps(chunk['content'])}\n\n"
                        llm_response['content'] = llm_response['content'] + '\n\n' + '**If you are okay with the result, please do merge it.**' 
                    else:
                        # Streaming text: accumulate and yield
                        llm_response['content'] += chunk
                        yield f"data: {json.dumps(llm_response)}\n\n"

                # Stream is complete
                llm_response['is_stream_complete'] = True
                
                #Need to format the last message
                last_message = messages.pop()
                
                if self.file_attachments:
                    last_message['file_attachments'] = [file.model_dump() for file in self.file_attachments]
                    last_message['content'] = user_prompt     
                        
                messages.append(last_message)
                messages.append({'role': 'assistant', 'created_at': generate_timestamp(), 'content': llm_response['content']})
                self.discussion_so_far = messages
                formatted_output_text = f"LLM response:\n {llm_response['content']}"
                
                await self.update_discussion_node(self.discussion_id, formatted_output_text=formatted_output_text, updated_discussion=self.discussion_so_far)
                # await self.utils._ask_for_title(self.discussion_id, self.discussion_so_far.copy())
                yield f"data: {json.dumps(llm_response)}\n\n"
            else:
                # Non-streaming response
                llm_response['content'] = response
                messages.append({'role': 'assistant', 'created_at': generate_timestamp(), 'content': response})
                self.discussion_so_far = messages
                formatted_output_text = f"LLM response:\n {response}"
                await self.update_discussion_node(self.discussion_id, formatted_output_text=formatted_output_text, updated_discussion=self.discussion_so_far)
                # is_valid, error_msg = self.validate_response_old(False, response)
                # if not is_valid :
                #     self.update_logger.warning(f"Diagram validation failed: {error_msg}")
                yield response
        
        except Exception as e:  # Exception handling for both modes
            print(f"Error in main_discussion: {e}")
            self.update_logger.error(f"Error in main_discussion: {e}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
        return


    async def capture_discussion_output(self, modifications):
        # print(f'Saving discussion node {modifications}')
        modified_node = modifications.get('modified_node', {})
        result = []
        result.append(self.get_modifications_from_discussion(modified_node))
        return {'modifications': result}

    def serialize_tool_call(self,tool_call):
        """Convert a ChatCompletionMessageToolCall object to a dict."""
        try:
            return {
                'id': str(tool_call.id) if hasattr(tool_call, 'id') else None,
                'type': str(tool_call.type) if hasattr(tool_call, 'type') else None,
                'function': {
                    'name': str(tool_call.function.name) if hasattr(tool_call.function, 'name') else None,
                    'arguments': str(tool_call.function.arguments) if hasattr(tool_call.function, 'arguments') else None
                }
            }
        except Exception as e:
            # Fallback - convert to string if serialization fails
            return {'error': str(e), 'string_value': str(tool_call)}
        
    async def initialize_tools_and_schemas(self):
        """Initialize tools and schemas used by discussions"""
        try:
            # Determine base path
            base_path = os.getcwd()
            base_path = "/tmp/doc_ingestion/dir"  # Common fallback path used in most implementations
            if not base_path:
                raise ValueError("Could not determine base path for tools")
            
            # Get tenant information 
            self.tenant_id = get_tenant_id()
            bucket_name = get_tenant_bucket(self.tenant_id)
            
            # Initialize S3 configuration - consistent across implementations
            s3_config = {
                "use_s3": True if self.tenant_id else False,
                "s3_bucket": bucket_name,
                "s3_base_path": f"extracted-docs-{self.tenant_id}" if self.tenant_id else None,
                "project_id": self.project_id,
                "s3_client": self.s3_client
            }
            
            self.update_logger.info(f"Using S3 configuration: {s3_config}")

            # Initialize DocsTools with S3 configuration
            self.doc_tools = DocsTools(
                base_path=base_path,
                logger=None,
                s3_config=s3_config,
                project_id=self.project_id,
                tenant_id=self.tenant_id
            )

            # Initialize standard DiscussionTools - consistent across implementations
            self.discussion_tools = DiscussionTools(
                base_path=None,
                logger=None,
                user_id=self.current_user,
                discussion=self
            )

            # Get schemas from both tools
            doc_schemas = self.doc_tools.function_schemas
            discussion_schemas = self.discussion_tools.function_schemas
            
            if getattr(self, 'invocation_type', 'interactive') == "autoconfig" :
                functions_to_remove = {'get_keys', 'get_key_values', 'find_relevant_keys', 'find_relevant_document_images', 'get_document_image'}
                doc_schemas = [
                    schema for schema in doc_schemas 
                    if schema['function']['name'] not in functions_to_remove
                ]

            # Combine schemas
            self.function_schemas = doc_schemas + discussion_schemas

            # Set up function mappings based on project knowledge
            if getattr(self, 'project_knowledge', False):
                self.function_mapping = {
                    **self.doc_tools.function_mapping,  # Knowledge tool functions
                    **self.discussion_tools.function_mapping  # Discussion tool functions
                }
            else:
                # Default case used in most implementations
                self.function_mapping = {
                    **self.discussion_tools.function_mapping  # Discussion tool functions only
                }

            # Define the combined executor - handles all variations
            def combined_executor(function_name, function_args):
                """Enhanced function executor with comprehensive error handling"""
                try:
                    # Debug logging (found in ProjectConfiguration and SystemContextConfiguration)
                    if self.update_logger.level <= 10:  # DEBUG level
                        self.update_logger.debug(f"Executing function: {function_name}")
                        self.update_logger.debug(f"Function arguments: {json.dumps(function_args, indent=2)}")
                    
                    # Argument validation
                    if not isinstance(function_args, dict):
                        raise TypeError(f"Function arguments must be a dictionary, got {type(function_args)}")
                    
                    # Check in doc_tools mapping
                    if function_name in self.doc_tools.function_mapping:
                        func = self.doc_tools.function_mapping[function_name]
                        
                        # Extra debug info for some implementations
                        if self.update_logger.level <= 10:
                            self.update_logger.debug(f"Function found in doc_tools mapping")
                            if hasattr(func, '__code__'):
                                self.update_logger.debug(f"Function signature: {func.__code__.co_varnames[:func.__code__.co_argcount]}")
                        
                        # Execute the function
                        result = func(**function_args)
                        return result
                    
                    # Check in discussion_tools mapping
                    elif function_name in self.discussion_tools.function_mapping:
                        func = self.discussion_tools.function_mapping[function_name]
                        
                        # Extra debug info for some implementations
                        if self.update_logger.level <= 10:
                            self.update_logger.debug(f"Function found in discussion_tools mapping")
                            if hasattr(func, '__code__'):
                                self.update_logger.debug(f"Function signature: {func.__code__.co_varnames[:func.__code__.co_argcount]}")
                        
                        # Execute the function
                        result = func(**function_args)
                        return result
                    
                    else:
                        # Create detailed error for unknown functions (consistent with most implementations)
                        available_functions = list(self.doc_tools.function_mapping.keys()) + \
                                            list(self.discussion_tools.function_mapping.keys())
                        
                        error_msg = (
                            f"Unknown function: {function_name}. Available functions: {available_functions}"
                        )
                        
                        self.update_logger.error(error_msg)
                        raise ValueError(error_msg)
                
                except Exception as e:
                    # Comprehensive error handling (found in most implementations)
                    self.update_logger.error(f"Error in function execution: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    raise

            self.function_executor = combined_executor
            self.schemas_initialized = True
            
            # Log the results (found in some implementations)
            if self.update_logger.level <= 20:  # INFO level or below
                self.update_logger.info(f"Tools and schemas initialized successfully")

        except Exception as e:
            self.update_logger.error(f"Failed to initialize tools and schemas: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def get_function_schemas(self) -> list:
        """Get the combined function schemas for tools"""
        if not getattr(self, 'schemas_initialized', False):
            self.update_logger.warning("Attempting to get schemas before initialization")
            return []
        
        if not getattr(self, 'function_schemas', None):
            self.update_logger.error("Function schemas are empty after initialization")
            return []
            
        return self.function_schemas

    def get_function_executors(self):
        """Return an async function executor that wraps the synchronous executor"""
        async def function_executor(function_name, function_args):
            try:
                # Execute the function
                result = self.function_executor(function_name, function_args)
                
                # Handle coroutines
                if inspect.iscoroutine(result):
                    result = await result
                
                # Ensure JSON serializable response
                if isinstance(result, dict):
                    return result
                elif result is None:
                    return {"status": "success", "value": None}
                else:
                    return {"status": "success", "value": str(result)}
                    
            except Exception as e:
                return {
                    "status": "ERROR", 
                    "error": str(e),
                    "function": function_name
                }
                
        return function_executor

    def validate_function_call(self, function_name: str, function_args: dict) -> bool:
        """Validate function calls before execution"""
        try:
            if not hasattr(self, 'function_mapping') or function_name not in self.function_mapping:
                self.update_logger.error(f"Unknown function: {function_name}")
                return False
            return True
        except Exception as e:
            self.update_logger.error(f"Error validating function call: {str(e)}")
            return False

    def convert_function_args_to_old_format(self, function_args, discussion_type):
        old_format = {}
        config = self.discussion_configurations.get(f"{discussion_type}Configuration", {})

        if 'modified_node' in function_args:
            old_format['modified_node'] = {
                'Title': function_args['modified_node'].get('Title', ''),
                'Description': function_args['modified_node'].get('Description', '')
            }

        if config.get('new_children_type'):
            for child_type in config['new_children_type']:
                key = f"{child_type}s"
                if key in function_args:
                    old_format[key] = function_args[key]

        if config.get('modified_children'):
            if 'modified_child_nodes' in function_args:
                old_format['modified_child_nodes'] = function_args['modified_child_nodes']

        if config.get('modified_siblings'):
            if 'Modified-Sibling-nodes' in function_args:
                old_format['Modified-Sibling-nodes'] = function_args['Modified-Sibling-nodes']

        if config.get('modified_other_nodes'):
            if 'Modified-Other-nodes' in function_args:
                old_format['Modified-Other-nodes'] = function_args['Modified-Other-nodes']

        if config.get('new_relationship_types'):
            if 'New-Relationships' in function_args:
                old_format['New-Relationships'] = function_args['New-Relationships']

        if config.get('modified_relationship_types'):
            if 'modified_relationships' in function_args:
                old_format['modified_relationships'] = function_args['modified_relationships']

        return old_format

    async def get_modifications_from_discussion(self, function_args):
        self.modifications = self.get_modifications_from_llm_output(self.node_type, self.root_node_type,
                                                                    self.discussion_type, function_args)

        self.modifications['created_at'] = generate_timestamp()

        self.modifications_history.append(self.modifications)

        await self.update_discussion_node(self.discussion_id, modifications=self.modifications,
                                          modifications_history=self.modifications_history)

        return self.modifications  # Return the current modifications, not the history

    def get_modifications_from_llm_output(self):
       pass

    def flatten_dict(self, d, parent_key='', sep='_'):
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self.flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)


    def set_config_state(self, node_properties):
        if self.discussion_type == "design_details" and self.node_type == "Architecture":
            node_properties.setdefault('design_detail_state', 'configured')
            return node_properties
            
        if self.discussion_type == "design_details" and self.node_type == "Interface":
            node_properties.setdefault('design_details_state', 'configured')
            return node_properties

        elif self.discussion_type == "definition" and self.node_type == "Interface":
            node_properties.setdefault('definition_state', 'configured')
            return node_properties

        elif self.discussion_type == "behavior" and self.node_type == "Design":
            node_properties.setdefault('behavior_config_state', 'configured')
            return node_properties
        
        elif self.discussion_type == "component_interactions" and self.node_type == "Design":
            node_properties.setdefault('component_interactions_config_state', 'configured')
            return node_properties
        
        elif self.discussion_type == "testcases" and self.node_type == "Design":
            node_properties.setdefault('test_cases_config_state', 'configured')
            return node_properties
        
        elif self.discussion_type == "classdiagram" and self.node_type == "Design":
            node_properties.setdefault('class_diagrams_config_state', 'configured')
            return node_properties
        
        else:
            node_properties.setdefault('configuration_state', 'configured')
            return node_properties


    async def merge_captured_items(
            self):  # this function has to be changed in the future to handle recursive merging of captured items

        modifications = self.modifications
        #print(f'merge_captured_items: {modifications}')

        if modifications.get("delete_node_check",{}) and modifications.get("delete_node_check").get("to_delete",False) :
            node_id_list = modifications.get("delete_node_check").get("node_id",[])
            if node_id_list :
                await self.db.mark_node_unused(node_id_list, self.project_id, mongo_handler=get_mongo_db())

        modified_node = modifications.get('modified_node')
        modified_children = modifications.get('modified_children', [])
        modified_siblings = modifications.get('modified_siblings', [])
        new_child_nodes = modifications.get('new_child_nodes', [])
        new_child_nodes_with_types = modifications.get('new_child_nodes_with_types', [])
        modified_similar_nodes = modifications.get('modified_similar_nodes', [])
        child_node_types = modifications.get('child_node_types')

        #log modifications to update_logger
        self.update_logger.info(f"Modified node: {modified_node}")
        self.update_logger.info(f"Modified children: {modified_children}")
        self.update_logger.info(f"Modified siblings: {modified_siblings}")
        self.update_logger.info(f"New child nodes: {new_child_nodes}")
        self.update_logger.info(f"New child nodes with types: {new_child_nodes_with_types}")
        self.update_logger.info(f"Modified similar nodes: {modified_similar_nodes}")
        self.update_logger.info(f"Child node types: {child_node_types}")
        
        node_id = self.node_id
        node_type = self.node_type
        
        if modified_node:
            # print(f'modified_node: {modified_node}')
            #print(f'modified_node.get("configuration_state"): {modified_node.get("configuration_state")}')
            modified_node = self.set_config_state(modified_node)

            await self.db.update_node_by_id(node_id, modified_node, node_type)

            self.update_logger.info("Updated node {} with {}".format(node_id, json.dumps(modified_node, indent=2)))
            
            # update the node in the vector database
            await self.vector_db.update_node_in_vector_db(node_id, modified_node)

        # concatenate all the nodes to modify into a single list
        nodes_to_modify = []

        if modified_children:
            nodes_to_modify.extend(modified_children)
            self.update_logger.info(
                "Updated children of node {} with {}".format(node_id, json.dumps(modified_children, indent=2)))
        if modified_siblings:
            nodes_to_modify.extend(modified_siblings)
            self.update_logger.info(
                "Updated siblings of node {} with {}".format(node_id, json.dumps(modified_siblings, indent=2)))
        if modified_similar_nodes:
            nodes_to_modify.extend(modified_similar_nodes)
            self.update_logger.info("Updated similar nodes of node {} with {}".format(node_id,
                                                                                      json.dumps(modified_similar_nodes,
                                                                                                 indent=2)))

        if nodes_to_modify and len(nodes_to_modify) > 0:
            # update the nodes
            await self.db.update_nodes_by_id(nodes_to_modify)
            # update the nodes in the vector database
            for node in nodes_to_modify:
                await self.vector_db.update_node_in_vector_db(node['id'], node, node.get("Type"))

        # now create the new child nodes
        created_nodes = []
        if new_child_nodes and len(new_child_nodes):
            if isinstance(new_child_nodes,str):
                print("NOTE: new_child_nodes is a string")
                self.update_logger.info("NOTE: new_child_nodes is a string")
                def truncate_on_brackets(s):
                    start = s.find("[")
                    if start == -1:
                        return s
                    stack = 1
                    for i in range(start + 1, len(s)):
                        if s[i] == "[":
                            stack += 1
                        elif s[i] == "]":
                            stack -= 1
                            if stack == 0:
                                return s[:i + 1]
                    return s
                new_child_nodes = truncate_on_brackets(new_child_nodes)
                new_child_nodes = json.loads(new_child_nodes)
            for child_node in new_child_nodes:
                if not isinstance(child_node, str):
                    child_node_type = child_node.get('Type')
                    if child_node_type in self.modifications['child_node_types']:                
                        # Create the new child node under respective parent
                        new_child_node = await self.db.create_node_with_type(child_node_type, child_node, self.node_id)
                        created_nodes.append(new_child_node)
                
                    
        # now create the new child nodes
        if new_child_nodes_with_types and len(new_child_nodes_with_types):
            created_nodes = await self.db.create_nodes_with_types(new_child_nodes_with_types, node_id)
            self.update_logger.info("Created child nodes with types of node {} with {}".format(node_id, json.dumps(
                new_child_nodes_with_types, indent=2)))

        # add the newly created nodes to the vector database
        for child_node in created_nodes:
            if self.root_node:
                await self.vector_db.add_node_to_vector_db(child_node['id'], child_node, self.root_node['id'],
                                                           node_type=child_node['labels'][0])

        self.modifications['created_nodes'] = created_nodes  # store the created nodes for future references or for sub-classes

        # Next, update the DiscussionNode to indicated that the items from the discussion have been implemented
        await self.db.update_node_by_id(self.discussion_id, {
            'status': 'implemented'}, "Discussion")
        
        if getattr(self, 'is_flagged_for_reconfig', False):
            try:
                await self.version_manager.clear_reconfig_flag(self.node_id)
                self.update_logger.info(f"Cleared reconfiguration flag for node {self.node_id}")
                
                # Check if our changes should trigger new flags
                if modified_node and modified_node.get('changes_needed', False):
                    change_reason = modified_node.get('change_reason', f"{self.node_type} was modified")
                    await self.version_manager.flag_downstream_nodes(self.node_id, change_reason)
            except Exception as e:
                self.update_logger.error(f"Error handling reconfiguration flags: {str(e)}")

        return True
    
    # Add this new method
    async def check_reconfiguration_needs(self):
        """Check if reconfiguration is needed due to parent changes"""
        
        # Get the node from Neo4j to check reconfig_needed flag
        node = await self.db.get_node_by_id(self.node_id)
        
        if node and node.get('properties', {}).get('reconfig_needed', False):
            # Node is flagged for reconfiguration in Neo4j
            
            # Get detailed status from MongoDB via NodeVersionManager
            node_status = await self.version_manager.get_node_reconfig_status(self.node_id)
            
            if node_status:
                # Set flags and context for use in templates and processing
                self.is_flagged_for_reconfig = True
                self.reconfig_reason = node_status.get("reconfig_reason", "Parent node was modified")
                self.flagged_at = node_status.get("flagged_at")
                self.triggering_parent_id = node_status.get("parent_id")
                
                # Consider using more powerful model for complex reconfigurations
                # self.model_name = LLMModel.gpt_4_1.value
                
                # Add this information to retrieved_info for template rendering
                self.retrieved_info['reconfig_flags'] = {
                    'flagged': True,
                    'reason': self.reconfig_reason,
                    'flagged_at': self.flagged_at,
                    'parent_id': self.triggering_parent_id
                }
                
                # If there's a parent node that triggered this, add its info
                if self.triggering_parent_id:
                    parent_node = await self.db.get_node_by_id(self.triggering_parent_id)
                    if parent_node:
                        self.retrieved_info['triggering_parent'] = {
                            'id': self.triggering_parent_id,
                            'type': parent_node.get('labels', ["Unknown"])[0],
                            'properties': parent_node.get('properties', {})
                        }
        
        return self.is_flagged_for_reconfig

    async def finalize(self):
        # Update the DiscussionNode to indicate that the discussion has been finalized

        await self.update_discussion_node(self.discussion_id, status='finalized')
        self.timer.end_discussion(self.discussion_id)

        # End session tracking
        try:
            # Check invocation type - only end session for interactive discussions
            invocation_type = getattr(self, 'invocation_type', 'interactive')
            celery_task_id_value = celery_task_id.get()

            # Debug logging
            if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                self.session_debug_logger.info("SESSION FINALIZATION")
                self.session_debug_logger.info(f"Invocation Type: {invocation_type}")
                self.session_debug_logger.info(f"Celery Task ID: {celery_task_id_value}")
                self.session_debug_logger.info(f"Discussion ID: {self.discussion_id}")

            if invocation_type == "autoconfig":
                # For auto-configuration, don't end the session here
                # The session will be ended in the celery task (app/tasks.py)
                print(f"🔗 Auto-configuration discussion {self.discussion_id} session managed by celery task")
                print(f"✅ Session tracking end skipped for auto-config discussion {self.discussion_id}")

                if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                    self.session_debug_logger.info("AUTO-CONFIG: Skipping session end")
                    self.session_debug_logger.info(f"AUTO-CONFIG: Session managed by celery task: {celery_task_id_value}")
            else:
                # For interactive configuration, end the individual session
                from app.services.session_tracker import get_session_tracker
                tracker = get_session_tracker()

                if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                    self.session_debug_logger.info("INTERACTIVE: Ending session")
                    self.session_debug_logger.info(f"INTERACTIVE: Ending session for discussion: {self.discussion_id}")

                end_result = await tracker.end_session(str(self.discussion_id), "completed")
                if end_result["success"]:
                    if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                        self.session_debug_logger.info("INTERACTIVE: Session end successful")
                else:
                    print(f"⚠️ Failed to end session tracking: {end_result.get('error', 'Unknown error')}")
                    if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                        self.session_debug_logger.error(f"INTERACTIVE: Session end failed: {end_result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"⚠️ Error ending session tracking: {e}")
            if hasattr(self, 'session_debug_logger') and self.session_debug_logger:
                self.session_debug_logger.error(f"SESSION FINALIZATION ERROR: {e}")

        # Get and log the timing summary
        if hasattr(self, 'supervisor') and self.supervisor:
            await self.supervisor.record_discussion_timing(self.timer.get_timing_summary())
        return True

    async def update_discussion_node(self, discussion_id, formatted_output_text=None, updated_discussion=None,
                                     discussion_type=None, action=None, modifications=None, status=None,
                                     modifications_history=None):
        # Initialize an empty dictionary for the parameters to update
        update_params = {}

        # Check each parameter and add it to the dictionary if not None
        if formatted_output_text is not None:
            update_params['formatted_output_text'] = formatted_output_text

        if updated_discussion is not None:
            # Convert to JSON for storage as text, as the updated_discussion is in LLM messages format and is a list
            update_params['discussion_so_far'] = json.dumps(updated_discussion)

        if discussion_type is not None:
            update_params['discussion_type'] = discussion_type

        if action is not None:
            update_params['action'] = action
        # print(f'update_node {modifications}')
        if modifications is not None:
            update_params['modifications'] = json.dumps(modifications)

        if modifications_history is not None:
            update_params['modifications_history'] = json.dumps(
                modifications_history)

        if status is not None:
            update_params['status'] = status

        # Proceed with the update only if there are parameters to update
        if update_params:
            update_params['updated_at'] = generate_timestamp()
            await self.db.update_node_by_id(discussion_id, update_params, "Discussion")

        return
    

    def get_search_criteria_for_discussion(self):
        """
        Get search criteria based on discussion type and node type.
        Allows for specialized search criteria based on both discussion and node type.
        
        Returns:
            dict: Contains 'search_terms' list and 'and_search' boolean
        """
        # Try to get criteria specific to this discussion type and node type combination
        combined_key = f"{self.discussion_type.lower()}_{self.node_type.lower()}" if self.node_type else self.discussion_type.lower()
        criteria = self._discussion_search_criteria.get(combined_key)
        
        if not criteria:
            # Fall back to just discussion type if no combined criteria exists
            criteria = self._discussion_search_criteria.get(self.discussion_type.lower())
        
        # Use default if no specific criteria found
        if not criteria:
            criteria = self._discussion_search_criteria['default']
            
        return criteria

    def add_search_criteria(self, discussion_type, search_terms, and_search=False, node_type=None):
        """
        Add or update search criteria for a discussion type.
        
        Args:
            discussion_type (str): Type of discussion
            search_terms (list): List of search terms to use
            and_search (bool, optional): Whether to use AND search. Defaults to False
            node_type (str, optional): If provided, creates criteria specific to this node type
        """
        key = f"{discussion_type.lower()}_{node_type.lower()}" if node_type else discussion_type.lower()
        self._discussion_search_criteria[key] = {
            'search_terms': search_terms,
            'and_search': and_search
        }