from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import json
from app.core.function_schema_generator import get_function_schema
from app.discussions.tools.discussion_tool import DiscussionTools
from app.utils.auth_utils import get_current_user
import os
from app.utils.prodefn.docs_tool import DocsTools
from app.utils.prodefn.docs_session_manager import get_or_create_session
import inspect
from app.utils.file_utils.upload_utils import upload_and_process,get_tenant_bucket
from app.connection.tenant_middleware import get_tenant_id
from app.utils.datetime_utils import generate_timestamp
import logging

class InterfaceDefinition(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Interface")
        self.template_name = "interface_definition.prompt"
        self.function_schema_type = "InterfaceDefinition"
        self.project_id = None
        self.doc_tools = None
        self.discussion_tools = None
        self.function_mapping = {}
        self.function_executor = None
        self.function_schemas = []
        self.schemas_initialized = False
        self.project_knowledge = False
        self.update_logger = logging.getLogger(__name__)

    async def async_initialize(self):
        await super().async_initialize()
        self.current_configuration = self.discussion_configurations.get(f"{self.node_type}Configuration", {})

        
    async def retrieve_info(self):
        await super().retrieve_info()

        # Get all interface properties from the Interface node
        if 'current_node' in self.retrieved_info and 'properties' in self.retrieved_info['current_node']:
            interface_properties = {
                k: v for k, v in self.retrieved_info['current_node']['properties'].items() 
                if k.startswith('interface_')
            }
            
            # Safely parse interface properties
            self.retrieved_info['incoming_interfaces'] = []
            for v in interface_properties.values():
                try:
                    if v:
                        parsed_interface = json.loads(v)
                        self.retrieved_info['incoming_interfaces'].append(parsed_interface)
                except json.JSONDecodeError:
                    self.update_logger.warning(f"Failed to parse interface property: {v}")
                    continue
        # Get project node and details
        project_node = self.retrieved_info['root_node']
        
        # Get the parent architecture node
        parent_node = await self.db.get_parent_node(self.node_id)
        if parent_node:
            self.retrieved_info['parent_architecture_node'] = parent_node

        # Get parent component information 
        parent_component = await self.db.get_parent_node(self.node_id)
        if parent_component:
            self.retrieved_info['parent_component'] = {
                'id': parent_component['id'],
                'title': parent_component['properties'].get('Title', ''),
                'description': parent_component['properties'].get('Description', ''),
                'technology': parent_component['properties'].get('Technology', '')
            }

            # Get parent container
            parent_container = await self.db.get_parent_node(parent_component['id'])
            if parent_container:
                self.retrieved_info['parent_container'] = {
                    'id': parent_container['id'],
                    'properties': parent_container['properties'],
                    'tech_stack': parent_container['properties'].get('Selected_Tech_Stack', ''),
                    'Platform': parent_container['properties'].get('Selected_Platform', '')
 
                }

        # Get incoming interface details
        if 'current_node' in self.retrieved_info and 'properties' in self.retrieved_info['current_node']:
            # Get source components for interfaces
            incoming_interfaces = json.loads(
                self.retrieved_info['current_node']['properties'].get('incoming_interfaces', '[]')
            )
            
            # Enrich with source component details
            enriched_interfaces = []
            for interface in incoming_interfaces:
                source_id = interface.get('source_component_id')
                if source_id:
                    source_component = await self.db.get_node_by_id(source_id)
                    if source_component:
                        interface['source_component'] = {
                            'id': source_id,
                            'title': source_component['properties'].get('Title', ''),
                            'technology': source_component['properties'].get('Technology', '')
                        }
                enriched_interfaces.append(interface)
                
            self.retrieved_info['incoming_interfaces'] = enriched_interfaces

        # Get architectural requirements
        architecture_root = await self.db.get_child_nodes(project_node['id'], "ArchitectureRoot")
        if architecture_root:
            arch_req_node = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
            if arch_req_node:
                # Get full requirement nodes
                functional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "FunctionalRequirement")
                nonfunctional_reqs = await self.db.get_child_nodes(arch_req_node[0]['id'], "NonFunctionalRequirement")
                
                architectural_reqs = {
                    'functional_requirements': functional_reqs,
                    'architectural_requirements': nonfunctional_reqs
                }

        # Structure current background info
        current_background_info = {
            'project_context': {
                'node_id': project_node['id'],
                'properties': project_node['properties']
            },
            'component_context': {
                'component': self.retrieved_info.get('parent_component', {}),
                'container': self.retrieved_info.get('parent_container', {}),
                'interfaces': self.retrieved_info.get('incoming_interfaces', [])
            },
            'requirements_context': {
                'functional_requirements': architectural_reqs['functional_requirements'],
                'architectural_requirements': architectural_reqs['architectural_requirements'],
                'parent_node': arch_req_node[0] if arch_req_node else None
            }
        }
        # Handle reconfiguration
        if self.is_reconfig():
            stored_background_info = await self.version_manager._get_context(self.node_id)
            
            if stored_background_info:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': stored_background_info.get('background_info', current_background_info),
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
                # Add safety check
                if not self.retrieved_info['background_info']:
                    self.retrieved_info['background_info'] = current_background_info
            else:
                self.retrieved_info.update({
                    'original_node': self.node,
                    'background_info': current_background_info,
                    'user_interaction': {
                        'input': self.node['properties'].get('user_inputs', '[]')
                    },
                    'change_log': self.node['properties'].get('change_log', []),
                    'change_reason': self.node['properties'].get('change_reason', '')
                })
            
            self.retrieved_info['new_background'] = current_background_info
        else:
            # For initial configuration
            self.retrieved_info['background_info'] = current_background_info
            self.background_info = current_background_info  # Set class variable
            self.retrieved_info['new_background'] = current_background_info

        return self.retrieved_info

    def get_output_format_for_discussion(self, key):
        key = "InterfaceDefinition"
        base_path = os.path.dirname(os.path.abspath(__file__))
        templates_path = os.path.join(base_path, 'discussions','prompt_templates')
        with open(f"{templates_path}/output_formats.txt", "r") as file:
            content = file.read()
            output_formats = content.split("\n\n")
            
            for output_format in output_formats:
                if output_format.startswith(key + ":"):
                    output = output_format.split(":", 1)[1].strip()
                    return ('{' + output + '}')
        return ""

    def get_modifications_from_llm_output(self):
        """Process modifications from LLM output while preserving the correct structure"""
        try:
          
            # Set child node types for interface definition based on data model
            self.modifications['child_node_types'] = ['Route', 'Method', 'DataContract', 'Protocol']

            # For autoconfig case where incoming_interfaces is used
            if 'incoming_interfaces' in self.modifications:
                incoming_interfaces = self.modifications.get('incoming_interfaces', [])
                existing_interfaces = json.loads(
                    self.retrieved_info['current_node']['properties'].get('incoming_interfaces', '[]')
                )

                # Update existing interfaces
                for new_interface in incoming_interfaces:
                    existing_index = next(
                        (i for i, existing in enumerate(existing_interfaces) 
                         if existing.get('source_node_id') == new_interface.get('source_node_id')),
                        None
                    )
                    if existing_index is not None:
                        existing_interfaces[existing_index] = new_interface
                    else:
                        existing_interfaces.append(new_interface)

                # Store the updated interfaces
                self.modifications['modified_node']['incoming_interfaces'] = json.dumps(existing_interfaces)


            return self.modifications

        except Exception as e:
            self.update_logger.error(f"Error in get_modifications_from_llm_output: {str(e)}")
            return {
                'modified_node': {},
                'new_child_nodes': [],
                'modified_children': [],
                'reason_for_this_call': {'reason': 'Error processing modifications'}
            }


    async def merge_captured_items(self):
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
         # Get background info by calling retrieve_info again
        await self.retrieve_info()

        try:
            if self.is_reconfig():
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()

                    # Handle interface-specific merging
                    interface_nodes = await self.db.get_child_nodes(self.node_id, "Interface")
                    if interface_nodes:
                        interface_node = interface_nodes[0]
                        if 'updated_interfaces' in self.modifications:
                            for interface in self.modifications['updated_interfaces']:
                                if interface and isinstance(interface, dict):
                                    await self.db.update_node_by_id(interface_node['id'], interface)

                    self.update_logger.info("Updated Interface node with new interface definitions")
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': self.modifications['modified_node'].get('user_inputs', ''),
                            'reason': self.modifications['modified_node'].get('change_reason', '')
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
            else:
                await super().merge_captured_items()

                # Handle interface-specific merging
                interface_nodes = await self.db.get_child_nodes(self.node_id, "Interface")
                if interface_nodes:
                    interface_node = interface_nodes[0]
                    if 'updated_interfaces' in self.modifications:
                        for interface in self.modifications['updated_interfaces']:
                            if interface and isinstance(interface, dict):
                                await self.db.update_node_by_id(interface_node['id'], interface)

                self.update_logger.info("Updated Interface node with new interface definitions")
                # Save version info
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'initial_config',
                        'input': self.modifications['modified_node'].get('user_inputs', ''),
                        'reason': self.modifications['modified_node'].get('change_reason', '')
                    }
                }
                await self.version_manager.save_node_info(save_data)

        except Exception as e:
            self.update_logger.error(f"Error saving changes to node {self.node_id}: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'
    
# Register the updated class
DiscussionFactory.register('definition', InterfaceDefinition, "Interface")