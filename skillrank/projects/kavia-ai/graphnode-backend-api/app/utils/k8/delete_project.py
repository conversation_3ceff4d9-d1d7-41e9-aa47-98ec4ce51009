from kubernetes import client, config
from kubernetes.client.rest import ApiException
import time
from app.utils.aws.ssm_loader import load_ssm_dev_param, load_ssm_qa_param, load_ssm_pre_prod_param
from app.core.k8_settings import k8_settings
import os

# TTL Configuration Constants
DEFAULT_JOB_TTL_SECONDS = 300  # 5 minutes
FAILED_JOB_TTL_SECONDS = 600   # 10 minutes for failed jobs (longer for debugging)


def delete_config_map(project_id, env_name):
    """
    Delete the ConfigMap associated with a project and environment.
    
    Args:
        project_id (str): Project ID
        env_name (str): Environment name
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Initialize Kubernetes client
        try:
            config.load_incluster_config()
        except config.ConfigException:
            try:
                config.load_kube_config()
            except config.ConfigException:
                if env_name.lower() == 'dev':
                    kubeconfig = load_ssm_dev_param()
                elif env_name.lower() == 'qa':
                    kubeconfig = load_ssm_qa_param()
                elif env_name.lower() in ['pre-prod', 'preprod']:
                    kubeconfig = load_ssm_pre_prod_param()
                else:
                    kubeconfig = load_ssm_dev_param()  # Default fallback
                config.load_kube_config_from_dict(kubeconfig)
        
        # Create Kubernetes API client
        core_v1 = client.CoreV1Api()
        
        # Construct ConfigMap name and namespace
        project_id_lower = str(project_id).lower()
        env_suffix = env_name.lower()
        configmap_name = f"pod-status-{project_id_lower}-{env_suffix}"
        namespace = k8_settings.NAMESPACE
        
        print(f"Attempting to delete ConfigMap: {configmap_name} in namespace: {namespace}")
        
        # Delete the ConfigMap
        core_v1.delete_namespaced_config_map(
            name=configmap_name,
            namespace=namespace
        )
        
        print(f"ConfigMap {configmap_name} deleted successfully")
        return True
        
    except ApiException as e:
        if e.status == 404:
            print(f"ConfigMap {configmap_name} not found (already deleted)")
            return True
        else:
            print(f"Error deleting ConfigMap: API error code {e.status}, reason: {e.reason}")
            return False
    except Exception as e:
        print(f"Unexpected error deleting ConfigMap: {str(e)}")
        return False


def delete_kubernetes_deployment(project_id=None, env_name=None, kubeconfig_dict=None, k8_config_override=None):
    """
    Delete a Kubernetes deployment based on project_id and env_name.
    This version is decoupled from nginx configuration and will delete resources regardless.
    
    Args:
        project_id (str): Project ID for the deployment to delete
        env_name (str): Environment name
        kubeconfig_dict (dict, optional): Kubernetes config dictionary
        k8_config_override (dict, optional): Config overrides
        
    Raises:
        ValueError: If project_id or env_name is not provided
        ApiException: If Kubernetes API operations fail
    """
    # Override settings if provided
    if k8_config_override:
        k8_settings.override(**k8_config_override)
    
    # Use provided values or fallback to k8_settings
    if project_id:
        k8_settings.PROJECT_ID = project_id
    if env_name:
        k8_settings.ENVIRONMENT = env_name

    action = "delete"

    # Load Kubernetes configuration
    if kubeconfig_dict:
        try:
            config.load_kube_config_from_dict(config_dict=kubeconfig_dict)
        except config.ConfigException:
            try:
                config.load_incluster_config()
            except config.ConfigException:
                config.load_kube_config()
    else:
        try:
            config.load_incluster_config()
        except config.ConfigException:
            config.load_kube_config()

    # Create Kubernetes API client
    batch_v1 = client.BatchV1Api()

    # Use settings for job name and namespace
    job_name = k8_settings.get_job_name(action)
    namespace = k8_settings.NAMESPACE

    # Construct deployment name directly from project_id and env_name
    project_id_lower = str(project_id).lower()
    env_suffix = env_name.lower()
    deployment_name = f"{project_id_lower}-{env_suffix}"
    
    # Define kubectl command that doesn't depend on nginx config
    kubectl_command = f"""
        set -e  # Exit on any error
        echo "=== Starting deletion for project {project_id} in environment {env_name} ==="
        
        # Direct deployment deletion approach
        deployment_name="{deployment_name}"
        service_name="service-$deployment_name"
        service_clusterip_name="internal-clusterip-$deployment_name"
        pvc_name="pvc-$deployment_name"
        
        echo "Target resources:"
        echo "- Deployment: $deployment_name"
        echo "- Service: $service_name"
        echo "- ClusterIP Service: $service_clusterip_name"
        echo "- PVC: $pvc_name"
        
        # Check what exists before deletion
        echo "=== Checking existing resources ==="
        kubectl get deployments -n {namespace} | grep "$deployment_name" || echo "No deployment found with name: $deployment_name"
        kubectl get services -n {namespace} | grep "$deployment_name" || echo "No services found with pattern: $deployment_name"
        kubectl get pvc -n {namespace} | grep "$deployment_name" || echo "No PVC found with pattern: $deployment_name"
        kubectl get pods -n {namespace} | grep "{project_id}" || echo "No pods found with project ID: {project_id}"
        
        # Delete resources (ignore not found errors)
        echo "=== Starting resource deletion ==="
        
        echo "Deleting deployment..."
        kubectl delete deployment "$deployment_name" -n {namespace} --ignore-not-found=true && echo "✓ Deployment deleted" || echo "✗ Deployment deletion failed"
        
        echo "Deleting services..."
        kubectl delete service "$service_name" -n {namespace} --ignore-not-found=true && echo "✓ Service deleted" || echo "✗ Service deletion failed"
        kubectl delete service "$service_clusterip_name" -n {namespace} --ignore-not-found=true && echo "✓ ClusterIP service deleted" || echo "✗ ClusterIP service deletion failed"
        
        echo "Deleting PVC..."
        kubectl delete pvc "$pvc_name" -n {namespace} --ignore-not-found=true && echo "✓ PVC deleted" || echo "✗ PVC deletion failed"
        
        # Try to clean up nginx config if possible (but don't fail if it doesn't work)
        echo "=== Nginx config cleanup (non-blocking) ==="
        nginx_pod=$(kubectl get pods -n {namespace} -l app=nginx -o name | head -1 | cut -d'/' -f2)
        if [ -n "$nginx_pod" ]; then
            echo "Using nginx pod: $nginx_pod"
            kubectl exec -n {namespace} "$nginx_pod" -- rm -f /etc/nginx/conf.d/custom_{k8_settings.PROJECT_ID}.conf && echo "✓ Nginx config removed" || echo "✗ Nginx config cleanup failed or file not found - continuing anyway"
        else
            echo "No nginx pod found - skipping nginx config cleanup"
        fi
        
        # Additional cleanup: try to find and delete any pods with matching labels
        echo "=== Pod cleanup by labels ==="
        pod_count=$(kubectl get pods -n {namespace} -l "app=$deployment_name" --no-headers 2>/dev/null | wc -l)
        if [ "$pod_count" -gt 0 ]; then
            echo "Found $pod_count pods with label app=$deployment_name"
            kubectl get pods -n {namespace} -l "app=$deployment_name" -o name | while read pod; do
                if [ -n "$pod" ]; then
                    echo "Force deleting pod: $pod"
                    kubectl delete "$pod" -n {namespace} --force --grace-period=0 && echo "✓ Pod deleted" || echo "✗ Pod deletion failed"
                fi
            done
        else
            echo "No pods found with label app=$deployment_name"
        fi
        
        # Check for any pods with project ID in the name and delete them
        echo "=== Pod cleanup by name pattern ==="
        pod_names=$(kubectl get pods -n {namespace} -l "app=$deployment_name" --no-headers 2>/dev/null | grep "{project_id}" | awk '{{print $1}}' || true)
        if [ -n "$pod_names" ]; then
            echo "Found pods with project ID {project_id} in name:"
            echo "$pod_names" | while read pod_name; do
                if [ -n "$pod_name" ]; then
                    echo "Force deleting pod by name pattern: $pod_name"
                    kubectl delete pod "$pod_name" -n {namespace} --force --grace-period=0 && echo "✓ Pod $pod_name deleted" || echo "✗ Pod $pod_name deletion failed"
                fi
            done
        else
            echo "No pods found with project ID {project_id} in name"
        fi
        
        echo "=== Final verification ==="
        remaining_resources=0
        
        if kubectl get deployment "$deployment_name" -n {namespace} >/dev/null 2>&1; then
            echo "⚠ Deployment $deployment_name still exists"
            remaining_resources=$((remaining_resources + 1))
        fi
        
        if kubectl get service "$service_name" -n {namespace} >/dev/null 2>&1; then
            echo "⚠ Service $service_name still exists"
            remaining_resources=$((remaining_resources + 1))
        fi
        
        if kubectl get pvc "$pvc_name" -n {namespace} >/dev/null 2>&1; then
            echo "⚠ PVC $pvc_name still exists"
            remaining_resources=$((remaining_resources + 1))
        fi
        
        pod_count=$(kubectl get pods -n {namespace} --no-headers 2>/dev/null | grep "{project_id}" | wc -l || echo "0")
        if [ "$pod_count" -gt 0 ]; then
            echo "⚠ $pod_count pods with project ID {project_id} still exist"
            remaining_resources=$((remaining_resources + pod_count))
        fi
        
        if [ "$remaining_resources" -eq 0 ]; then
            echo "=== ✓ Deployment deletion completed successfully - all resources cleaned up ==="
        else
            echo "=== ⚠ Deployment deletion completed with $remaining_resources remaining resources ==="
            echo "This may be normal if resources were already deleted or never existed"
        fi
        """

    # Define the job manifest using k8_settings with TTL
    job_manifest = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": job_name,
            "namespace": namespace,
            "labels": {
                "app": job_name,
                "owner": "duploservices",
                "tenantname": namespace,
            },
        },
        "spec": {
            "parallelism": 1,
            "completions": 1,
            "backoffLimit": 6,
            "ttlSecondsAfterFinished": getattr(k8_settings, 'JOB_TTL_SECONDS', 300),  # 5 minutes default
            "template": {
                "metadata": {
                    "labels": {
                        "app": job_name,
                        "owner": "duploservices",
                        "tenantname": namespace,
                    },
                },
                "spec": {
                    "serviceAccountName": k8_settings.SERVICE_ACCOUNT_NAME,
                    "restartPolicy": "Never",
                    "containers": [
                        {
                            "name": "duplocloudcodegen",
                            "image": "************.dkr.ecr.us-west-2.amazonaws.com/kubectl:latest",
                            "command": ["/bin/sh", "-c"],
                            "args": [kubectl_command],
                            "env": [
                                {"name": "ACTION", "value": action},
                                {"name": "PROJECT_ID", "value": str(project_id)},
                                {"name": "ENV_NAME", "value": k8_settings.ENVIRONMENT},
                            ],
                            "volumeMounts": [
                                {"name": k8_settings.VOLUME_MOUNT, "mountPath": "/app"},
                                {"name": k8_settings.INGRESS_NAME, "mountPath": "/app/ingress"},
                                {"name": "codegenpvc", "mountPath": "/app/pvc"},
                                {"name": "nginx", "mountPath": "/app/nginx"},
                            ],
                        }
                    ],
                    "volumes": [
                        {
                            "name": k8_settings.VOLUME_MOUNT,
                            "configMap": {"name": k8_settings.CONFIG_MAP_NAME},
                        },
                        {
                            "name": k8_settings.INGRESS_NAME,
                            "configMap": {"name": k8_settings.INGRESS_NAME},
                        },
                        {
                            "name": "codegenpvc",
                            "configMap": {"name": "codegenpvc"},
                        },
                        {
                            "name": "nginx",
                            "configMap": {"name": "nginx"},
                        },
                    ],
                },
            },
        },
    }

    def wait_for_job_completion(job_name, namespace):
        """Wait for job completion with TTL-aware handling."""
        max_iterations = k8_settings.JOB_COMPLETION_TIMEOUT // k8_settings.POLLING_INTERVAL
        iterations = 0
        
        while iterations < max_iterations:
            try:
                job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
                if job.status.succeeded:
                    print(f"Job {job_name} completed successfully - TTL will auto-cleanup in {getattr(k8_settings, 'JOB_TTL_SECONDS', 300)} seconds")
                    return True
                elif job.status.failed:
                    print(f"Job {job_name} failed - fetching logs for debugging:")
                    get_job_logs(job_name, namespace)
                    print(f"Job will auto-cleanup via TTL in {getattr(k8_settings, 'JOB_TTL_SECONDS', 300)} seconds")
                    raise RuntimeError(f"Job {job_name} failed to complete")
            except ApiException as e:
                if e.status == 404:
                    print(f"Job {job_name} not found - may have been auto-cleaned by TTL")
                    return True
                raise
                
            time.sleep(k8_settings.POLLING_INTERVAL)
            iterations += 1
        
        raise TimeoutError(f"Job {job_name} timed out after {k8_settings.JOB_COMPLETION_TIMEOUT} seconds")

    def get_job_logs(job_name, namespace):
        """Retrieve and print job logs for debugging."""
        try:
            core_v1 = client.CoreV1Api()
            
            # Get pods for this job
            pods = core_v1.list_namespaced_pod(
                namespace=namespace,
                label_selector=f"job-name={job_name}"
            )
            
            if not pods.items:
                print("No pods found for job")
                return
                
            for pod in pods.items:
                print(f"\n--- Logs from pod {pod.metadata.name} ---")
                try:
                    logs = core_v1.read_namespaced_pod_log(
                        name=pod.metadata.name,
                        namespace=namespace,
                        tail_lines=50
                    )
                    print(logs)
                except ApiException as e:
                    print(f"Failed to get logs from pod {pod.metadata.name}: {e}")
                print("--- End logs ---\n")
                
        except Exception as e:
            print(f"Failed to retrieve job logs: {e}")

    def cleanup_existing_job(job_name, namespace):
        """Clean up existing job if it exists."""
        try:
            existing_job = batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
            print(f"Job {job_name} already exists, deleting it first...")
            batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=namespace,
                body=client.V1DeleteOptions(propagation_policy='Foreground', grace_period_seconds=0),
            )
            
            # Wait for the job to be fully deleted
            retry_count = 0
            max_retries = 10
            while retry_count < max_retries:
                try:
                    batch_v1.read_namespaced_job(name=job_name, namespace=namespace)
                    print(f"Waiting for job {job_name} to be deleted...")
                    time.sleep(k8_settings.POLLING_INTERVAL)
                    retry_count += 1
                except ApiException as e:
                    if e.status == 404:
                        print(f"Job {job_name} has been deleted successfully")
                        return
                    else:
                        raise
            
            if retry_count >= max_retries:
                print(f"Warning: Timed out waiting for job {job_name} to be deleted")
        
        except ApiException as e:
            if e.status == 404:  # 404 means job doesn't exist, which is fine
                print(f"Job {job_name} does not exist, proceeding with creation")
            else:
                raise

    # Main execution
    try:
        # Clean up any existing job
        cleanup_existing_job(job_name, namespace)
        
        # Create the new deletion job
        response = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
        print(f"Job created: {response.metadata.name}")
        
        # Delete associated ConfigMap (non-blocking)
        print("Deleting associated ConfigMap...")
        delete_config_map(project_id, k8_settings.ENVIRONMENT)
        
        # Wait for job completion
        wait_for_job_completion(job_name, namespace)
        
        print(f"Successfully completed deletion for project {project_id} in environment {env_name}")

    except ApiException as e:
        print(f"Kubernetes API error occurred: {e}")
        raise
    except Exception as e:
        print(f"Unexpected error occurred: {e}")
        raise


# Example usage
if __name__ == "__main__":
    # Method 1: Use environment variables
    project_id = os.getenv("PROJECT_ID")
    env_name = os.getenv("ENVIRONMENT")
    
    if not project_id or not env_name:
        raise ValueError("PROJECT_ID and ENVIRONMENT environment variables must be set")
    
    delete_kubernetes_deployment(project_id=project_id, env_name=env_name)