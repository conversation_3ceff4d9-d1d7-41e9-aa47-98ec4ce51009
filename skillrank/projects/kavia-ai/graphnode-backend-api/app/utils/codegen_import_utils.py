"""
Utility functions for codebase import operations in code generation workflows.
Handles both SCM and non-SCM repository import cases.
"""

import json
from fastapi import HTTPException

from app.connection.establish_db_connection import get_node_db, NodeDB
from app.connection.tenant_middleware import get_user_id
from app.utils.datetime_utils import generate_timestamp
from app.utils.node_utils import get_node_type

async def _get_repository_details_from_nodedb(project_id: int, db: NodeDB):
    """Internal helper function to get repository details from node database."""
    # Get project details
    project = await db.get_node_by_label_id(project_id, "Project")
    if not project:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")

    project_details = project.get("properties", {})

    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")

    # Load project repositories from the project details
    project_repositories = json.loads(project_details.get("repositories", "{}"))

    if not project_repositories:
        print(generate_timestamp(), f"No repositories found for project {project_id}")
        return {
            "project_id": project_id,
            "project_name": project_details.get("Title", "Unknown"),
            "repositories": [],
            "total_repositories": 0
        }

    # Get containers for this project
    containers = await db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)

    repository_list = []

    # Process each repository
    for container_id, repo_data in project_repositories.items():
        try:
            # Find corresponding container details
            container_details = None
            for container in containers:
                if str(container.get("id")) == container_id:
                    container_details = container.get("properties", {})
                    break

            # Extract repository information
            repo_name = repo_data.get("repositoryName", "")
            organization = repo_data.get("organization", "")

            # Format repository name as organization/repository
            if organization and repo_name:
                full_repo_name = f"{organization}/{repo_name}"
            else:
                full_repo_name = repo_name

            # Handle encrypted_scm_id properly - only include if it exists and is not empty
            encrypted_scm_id = repo_data.get("encrypted_scm_id")
            if not encrypted_scm_id or encrypted_scm_id.strip() == "":
                encrypted_scm_id = None  # Don't pass empty strings, use None instead

            # Create repository data
            repo_request_data = {
                "repo_name": full_repo_name,
                "branch_name": repo_data.get("default_branch", "kavia-main"),
                "repo_type": "private" if repo_data.get("private", False) else "public",
                "repo_id": str(repo_data.get("repositoryId", container_id)),
                "associated": True,
                "encrypted_scm_id": encrypted_scm_id,  # None for non-SCM, actual value for SCM
                "container_id": container_id,
                "container_name": container_details.get("Title", "Unknown") if container_details else "Unknown"
            }

            repository_list.append(repo_request_data)

        except Exception as e:
            print(generate_timestamp(), f"Error processing repository for container {container_id}: {str(e)}")
            continue

    result = {
        "project_id": project_id,
        "project_name": project_details.get("Title", "Unknown"),
        "project_description": project_details.get("Description", ""),
        "repositories": repository_list,
        "total_repositories": len(repository_list)
    }

    print(generate_timestamp(), f"Found {len(repository_list)} repositories for project {project_id}")
    return result

async def _create_codebase_import_requests(project_id: int, repo_details: dict):
    """Internal helper function to create import requests."""
    if not repo_details["repositories"]:
        return None, []

    # Import required classes
    from app.routes.kg_route import CodebaseImportRequest, RepoBranchRequest

    # Create RepoBranchRequest objects
    repo_requests = []
    main_encrypted_scm_id = ""

    for repo_data in repo_details["repositories"]:
        # Handle encrypted_scm_id properly
        encrypted_scm_id = repo_data.get("encrypted_scm_id")
        if encrypted_scm_id and encrypted_scm_id.strip():
            if not main_encrypted_scm_id:
                main_encrypted_scm_id = encrypted_scm_id
        else:
            encrypted_scm_id = None  # Use None instead of empty string for non-SCM

        repo_request = RepoBranchRequest(
            repo_name=repo_data["repo_name"],
            branch_name=repo_data["branch_name"],
            repo_type=repo_data["repo_type"],
            repo_id=repo_data["repo_id"],
            associated=repo_data["associated"],
            encrypted_scm_id=encrypted_scm_id  # None for non-SCM, actual value for SCM
        )
        repo_requests.append(repo_request)

    # Create CodebaseImportRequest
    import_request = CodebaseImportRequest(
        project_id=project_id,
        repositories=repo_requests,
        encrypted_scm_id=main_encrypted_scm_id  # Use the main SCM ID or empty string
    )

    return import_request, repo_requests

async def import_codebase_for_project(project_id: int):
    """
    Single function to handle complete codebase import for a project.
    Only requires project_id - handles everything else automatically.

    Args:
        project_id: The project ID to import repositories for

    Returns:
        dict: Complete import results
    """
    try:
        print(generate_timestamp(), f"🚀 Starting codebase import for project {project_id}")

        # Get node database connection automatically
        db = get_node_db()

        # Get repository details from node database
        repo_details = await _get_repository_details_from_nodedb(project_id, db)

        if not repo_details["repositories"]:
            return {
                "status": "skipped",
                "message": "No repositories found for this project",
                "project_id": project_id,
                "project_name": repo_details.get("project_name", "Unknown"),
                "total_repositories": 0,
                "repositories": []
            }

        # Create import requests automatically
        import_request, repo_requests = await _create_codebase_import_requests(project_id, repo_details)

        # Auto-detect SCM mode
        repositories = repo_details.get('repositories', [])
        is_scm = any(
            repo.get('encrypted_scm_id') and repo.get('encrypted_scm_id').strip()
            for repo in repositories
        )

        # Import the import_codebase function
        from app.routes.kg_route import import_codebase

        print(generate_timestamp(), f"📦 Importing {repo_details['total_repositories']} repositories")
        print(generate_timestamp(), f"🔧 SCM mode: {is_scm}")
        print(generate_timestamp(), f"📋 Project: {repo_details['project_name']}")

        # Call import_codebase with auto-detected settings
        mock_current_user = {"cognito:username": get_user_id()}
        import_result = await import_codebase(
            request=import_request,
            upstream=False,
            scm=is_scm,
            current_user=mock_current_user
        )

        print(generate_timestamp(), f"✅ Import completed successfully")

        # Log each repository with SCM status
        for repo in repositories:
            scm_status = "SCM" if repo.get('encrypted_scm_id') else "Non-SCM"
            print(generate_timestamp(), f"📁 {repo['repo_name']} ({scm_status})")

        return {
            "status": "success",
            "message": f"Successfully imported {repo_details['total_repositories']} repositories",
            "project_id": project_id,
            "project_name": repo_details['project_name'],
            "project_description": repo_details.get('project_description', ''),
            "total_repositories": repo_details['total_repositories'],
            "is_scm": is_scm,
            "import_result": import_result,
            "repositories": [
                {
                    "repo_name": repo['repo_name'],
                    "container_id": repo['container_id'],
                    "container_name": repo['container_name'],
                    "is_scm": repo.get('encrypted_scm_id') is not None,
                    "encrypted_scm_id": repo.get('encrypted_scm_id')
                }
                for repo in repositories
            ]
        }

    except Exception as e:
        error_msg = f"Failed to import codebase for project {project_id}: {str(e)}"
        print(generate_timestamp(), f"❌ {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "project_id": project_id,
            "error": str(e)
        }
