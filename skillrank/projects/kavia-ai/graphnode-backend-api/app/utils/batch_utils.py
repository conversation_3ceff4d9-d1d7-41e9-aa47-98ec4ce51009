
#app.utils.batch_utils.py
import asyncio
import os
import json
import threading

from pymongo import MongoClient
from app.connection.establish_db_connection import get_node_db, get_mongo_db, NodeDB,MongoDBHandler
from app.utils.respository_utils import create_repository_in_workspace
from app.utils.datetime_utils import generate_timestamp
from app.utils.hash import decrypt_string
from app.utils.respository_utils import _run_clone_in_background
from app.models.scm import ACCESS_TOKEN_PATH, SCMType, SCMConfiguration
from app.utils.project_utils import name_to_slug
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_utils import get_node_type
from app.connection.tenant_middleware import get_user_id
from code_generation_core_agent.agents.project_welcome_page import  ContainerType
from app.utils.code_generation_utils import convert_manifest_to_yaml_string, get_container_type
from code_generation_core_agent.project_schemas import  dict_to_project_schema
from fastapi import HTTPException

async def get_repository_details_from_nodedb(project_id: int, db: NodeDB = None):
    """
    Get repository details from node database for a given project_id.

    Args:
        project_id: The project ID to get repository details for
        db: NodeDB instance (optional, will create one if not provided)

    Returns:
        dict: Repository details formatted for CodebaseImportRequest
    """
    try:
        if not db:
            db = get_node_db()

        # Get project details
        project = await db.get_node_by_label_id(project_id, "Project")
        if not project:
            raise HTTPException(status_code=404, detail=f"Project {project_id} not found")

        project_details = project.get("properties", {})

        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")

        # Load project repositories from the project details
        project_repositories = json.loads(project_details.get("repositories", "{}"))

        if not project_repositories:
            print(generate_timestamp(), f"No repositories found for project {project_id}")
            return {
                "project_id": project_id,
                "project_name": project_details.get("Title", "Unknown"),
                "repositories": [],
                "total_repositories": 0
            }

        # Get containers for this project
        containers = await db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)

        repository_list = []

        # Process each repository
        for container_id, repo_data in project_repositories.items():
            try:
                # Find corresponding container details
                container_details = None
                for container in containers:
                    if str(container.get("id")) == container_id:
                        container_details = container.get("properties", {})
                        break

                # Extract repository information
                repo_name = repo_data.get("repositoryName", "")
                organization = repo_data.get("organization", "")

                # Format repository name as organization/repository
                if organization and repo_name:
                    full_repo_name = f"{organization}/{repo_name}"
                else:
                    full_repo_name = repo_name

                # Create RepoBranchRequest compatible data
                # Handle encrypted_scm_id properly - only include if it exists and is not empty
                encrypted_scm_id = repo_data.get("encrypted_scm_id")
                if not encrypted_scm_id or encrypted_scm_id.strip() == "":
                    encrypted_scm_id = None  # Don't pass empty strings, use None instead

                repo_request_data = {
                    "repo_name": full_repo_name,
                    "branch_name": repo_data.get("default_branch", "kavia-main"),
                    "repo_type": "private" if repo_data.get("private", False) else "public",
                    "repo_id": str(repo_data.get("repositoryId", container_id)),
                    "associated": True,
                    "encrypted_scm_id": encrypted_scm_id,  # None for non-SCM, actual value for SCM
                    "container_id": container_id,
                    "container_name": container_details.get("Title", "Unknown") if container_details else "Unknown",
                    "repository_url": repo_data.get("clone_url", ""),
                    "ssh_url": repo_data.get("ssh_url", ""),
                    "access_token_path": repo_data.get("access_token_path", ""),
                    "scm_id": repo_data.get("scm_id", "")
                }

                repository_list.append(repo_request_data)

            except Exception as e:
                print(generate_timestamp(), f"Error processing repository for container {container_id}: {str(e)}")
                continue

        result = {
            "project_id": project_id,
            "project_name": project_details.get("Title", "Unknown"),
            "project_description": project_details.get("Description", ""),
            "repositories": repository_list,
            "total_repositories": len(repository_list),
            "raw_project_repositories": project_repositories  # Include raw data for debugging
        }

        print(generate_timestamp(), f"Found {len(repository_list)} repositories for project {project_id}")
        return result

    except Exception as e:
        print(generate_timestamp(), f"Error getting repository details for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get repository details: {str(e)}")

async def create_codebase_import_requests_from_nodedb(project_id: int, db: NodeDB = None):
    """
    Create CodebaseImportRequest objects from node database repository details.

    Args:
        project_id: The project ID to create import requests for
        db: NodeDB instance (optional)

    Returns:
        dict: Contains CodebaseImportRequest and individual RepoBranchRequest objects
    """
    try:
        # Get repository details
        repo_details = await get_repository_details_from_nodedb(project_id, db)

        if not repo_details["repositories"]:
            return {
                "import_request": None,
                "repo_requests": [],
                "message": "No repositories found for this project"
            }

        # Import required classes
        from app.routes.kg_route import CodebaseImportRequest, RepoBranchRequest

        # Create RepoBranchRequest objects
        repo_requests = []
        main_encrypted_scm_id = ""

        for repo_data in repo_details["repositories"]:
            # Handle encrypted_scm_id properly
            encrypted_scm_id = repo_data.get("encrypted_scm_id")
            if encrypted_scm_id and encrypted_scm_id.strip():
                if not main_encrypted_scm_id:
                    main_encrypted_scm_id = encrypted_scm_id
            else:
                encrypted_scm_id = None  # Use None instead of empty string for non-SCM

            repo_request = RepoBranchRequest(
                repo_name=repo_data["repo_name"],
                branch_name=repo_data["branch_name"],
                repo_type=repo_data["repo_type"],
                repo_id=repo_data["repo_id"],
                associated=repo_data["associated"],
                encrypted_scm_id=encrypted_scm_id  # None for non-SCM, actual value for SCM
            )
            repo_requests.append(repo_request)

        # Create CodebaseImportRequest
        import_request = CodebaseImportRequest(
            project_id=project_id,
            repositories=repo_requests,
            encrypted_scm_id=main_encrypted_scm_id  # Use the main SCM ID or empty string
        )

        return {
            "import_request": import_request,
            "repo_requests": repo_requests,
            "project_name": repo_details["project_name"],
            "total_repositories": repo_details["total_repositories"],
            "message": f"Created import request for {len(repo_requests)} repositories",
            "repo_details": repo_details  # Include full repo details for SCM detection
        }

    except Exception as e:
        print(generate_timestamp(), f"Error creating import requests for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create import requests: {str(e)}")

async def execute_codebase_import_from_nodedb(project_id: int, db: NodeDB = None, is_scm: bool = None):
    """
    Execute codebase import for all repositories in a project from node database.
    This function creates the import request and calls import_codebase.

    Args:
        project_id: The project ID to import repositories for
        db: NodeDB instance (optional)
        is_scm: Whether this is an SCM operation (auto-detected if None)

    Returns:
        dict: Import results for all repositories
    """
    try:
        # Create import request from node database
        import_data = await create_codebase_import_requests_from_nodedb(project_id, db)

        if not import_data['import_request']:
            return {
                "status": "skipped",
                "message": import_data['message'],
                "project_id": project_id,
                "results": []
            }

        # Auto-detect SCM if not specified
        if is_scm is None:
            # Check if any repository has encrypted_scm_id that is not empty
            repositories = import_data.get('repo_details', {}).get('repositories', [])
            is_scm = any(
                repo.get('encrypted_scm_id') and repo.get('encrypted_scm_id').strip()
                for repo in repositories
            )

        # Import the import_codebase function
        from app.routes.kg_route import import_codebase

        print(generate_timestamp(), f"🚀 Starting codebase import for project {project_id}")
        print(generate_timestamp(), f"📦 Importing {import_data['total_repositories']} repositories")
        print(generate_timestamp(), f"🔧 SCM mode: {is_scm}")

        # Call the import_codebase function
        mock_current_user = {"cognito:username": get_user_id()}
        import_result = await import_codebase(
            request=import_data['import_request'],
            upstream=False,
            ignore_build=True,
            scm=is_scm,
            current_user=mock_current_user
        )

        print(generate_timestamp(), f"✅ Import result: {import_result}")
        print(generate_timestamp(), f"🎯 Codebase import initiated for project: {import_data['project_name']}")

        # Log each repository
        for repo_req in import_data['repo_requests']:
            print(generate_timestamp(), f"📁 Repository imported: {repo_req.repo_name}")

        return {
            "status": "success",
            "message": f"Codebase import completed for {import_data['total_repositories']} repositories",
            "project_id": project_id,
            "project_name": import_data['project_name'],
            "total_repositories": import_data['total_repositories'],
            "import_result": import_result,
            "repositories": [repo_req.repo_name for repo_req in import_data['repo_requests']],
            "is_scm": is_scm
        }

    except Exception as e:
        print(generate_timestamp(), f"❌ Error executing codebase import for project {project_id}: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to execute codebase import: {str(e)}",
            "project_id": project_id,
            "error": str(e)
        }

async def create_and_import_repository(
    project_id: int,
    container_id: int,
    repository_response: dict,
    encrypted_scm_id: str = None,
    db: NodeDB = None
):
    """
    Create repository metadata and import it into the system.
    Handles both SCM and non-SCM cases.

    Args:
        project_id: Project ID
        container_id: Container ID
        repository_response: Repository response from GitHub/SCM
        encrypted_scm_id: Encrypted SCM ID (if SCM case)
        db: NodeDB instance

    Returns:
        dict: Import result
    """
    try:
        if not db:
            db = get_node_db()

        # Get project details
        project = await db.get_node_by_label_id(project_id, "Project")
        project_details = project.get("properties", {})

        # Create repository metadata
        repository_metadata = {
            'service': 'github',
            'repositoryName': repository_response['repositoryName'],
            'repositoryId': str(repository_response['repositoryId']),
            'cloneUrlHttp': repository_response.get('cloneUrlHttp', f"https://github.com/{repository_response['organization']}/{repository_response['repositoryName']}.git"),
            'cloneUrlSsh': repository_response.get('cloneUrlSsh', f"**************:{repository_response['organization']}/{repository_response['repositoryName']}.git"),
            'organization': repository_response['organization'],
            'repositoryStatus': 'initialized',
            'default_branch': repository_response.get('default_branch', 'kavia-main'),
            'container_id': str(container_id),
            'container_name': repository_response.get('container_name', f'container_{container_id}')
        }

        # Add SCM-specific or non-SCM specific fields
        if encrypted_scm_id:
            # SCM case
            repository_metadata['encrypted_scm_id'] = encrypted_scm_id
            is_scm = True
            print(generate_timestamp(), f"📦 Creating SCM repository metadata for {repository_response['repositoryName']}")
        else:
            # Non-SCM case (KAVIA managed)
            from app.models.scm import ACCESS_TOKEN_PATH
            repository_metadata['access_token_path'] = ACCESS_TOKEN_PATH.KAVIA_MANANGED.value
            is_scm = False
            print(generate_timestamp(), f"📦 Creating KAVIA-managed repository metadata for {repository_response['repositoryName']}")

        # Update project repositories in node database
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        project_repositories[str(container_id)] = repository_metadata

        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )

        print(generate_timestamp(), f"✅ Repository metadata stored in node database")

        # Import the repository using our utility function
        from app.routes.kg_route import CodebaseImportRequest, RepoBranchRequest, import_codebase

        # Handle encrypted_scm_id properly for import_codebase
        # Only pass encrypted_scm_id if it exists and is not empty
        scm_id_for_request = None
        if encrypted_scm_id and encrypted_scm_id.strip():
            scm_id_for_request = encrypted_scm_id

        # Prepare repository data for import_codebase
        repo_request = RepoBranchRequest(
            repo_name=f"{repository_response['organization']}/{repository_response['repositoryName']}",
            branch_name=repository_response.get('default_branch', 'kavia-main'),
            repo_type="public",
            repo_id=str(repository_response['repositoryId']),
            associated=True,
            encrypted_scm_id=scm_id_for_request  # None for non-SCM, actual value for SCM
        )

        # Create CodebaseImportRequest
        import_request = CodebaseImportRequest(
            project_id=project_id,
            repositories=[repo_request],
            encrypted_scm_id=encrypted_scm_id or ""  # Can be empty string for CodebaseImportRequest
        )

        # Call the import_codebase function
        mock_current_user = {"cognito:username": get_user_id()}
        import_result = await import_codebase(
            request=import_request,
            upstream=False,
            ignore_build=True,
            scm=is_scm,
            current_user=mock_current_user
        )

        print(generate_timestamp(), f"✅ Import result: {import_result}")
        print(generate_timestamp(), f"🎯 Codebase import initiated for repository: {repository_response['repositoryName']}")

        return {
            "status": "success",
            "message": f"Repository created and imported successfully",
            "project_id": project_id,
            "container_id": container_id,
            "repository_metadata": repository_metadata,
            "import_result": import_result,
            "is_scm": is_scm
        }

    except Exception as e:
        print(generate_timestamp(), f"❌ Error creating and importing repository: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to create and import repository: {str(e)}",
            "project_id": project_id,
            "container_id": container_id,
            "error": str(e)
        }

async def import_project_repositories_for_save_merge(project_id: int, is_scm: bool = None):
    """
    Import all project repositories during save and merge operation.
    This is the function to call from batch_route save and merge endpoint.

    Args:
        project_id: The project ID to import repositories for
        is_scm: Whether this is an SCM operation (auto-detected if None)

    Returns:
        dict: Import results
    """
    try:
        print(generate_timestamp(), f"🔄 Starting repository import for save/merge operation - Project ID: {project_id}")

        # Execute the codebase import with SCM detection
        import_result = await execute_codebase_import_from_nodedb(project_id, is_scm=is_scm)

        if import_result['status'] == 'success':
            print(generate_timestamp(), f"✅ Save/merge repository import completed successfully")
            print(generate_timestamp(), f"📊 Imported {import_result['total_repositories']} repositories")
            print(generate_timestamp(), f"🔧 SCM mode: {import_result.get('is_scm', 'unknown')}")
            for repo_name in import_result['repositories']:
                print(generate_timestamp(), f"  - {repo_name}")
        elif import_result['status'] == 'skipped':
            print(generate_timestamp(), f"⚠️ Save/merge repository import skipped: {import_result['message']}")
        else:
            print(generate_timestamp(), f"❌ Save/merge repository import failed: {import_result['message']}")

        return import_result

    except Exception as e:
        error_msg = f"Failed to import repositories for save/merge operation: {str(e)}"
        print(generate_timestamp(), f"❌ {error_msg}")
        return {
            "status": "error",
            "message": error_msg,
            "project_id": project_id,
            "error": str(e)
        }

async def task_execute_maintenance(project_id: int, db: NodeDB = None):
    if not db:
        db = get_node_db()
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    
    containers = await db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)
    
    work_items = {
        "project_id": project_id,
        "project_name": project_details.get("Title"),
        "containers": []
    }

    for container in containers:
        container_id = str(container.get("id"))
        
        # Handle repository creation/verification
        if container_id not in project_repositories:
            try:
                repository_response = await create_repository_in_workspace(
                    project_id=project_id,
                    container_id=int(container_id),
                    db=db
                )
                
                repository_details = repository_response.get("repository")
                project_repositories[container_id] = repository_details
                
            except Exception as e:
                print(generate_timestamp(),f"Error creating repository for container {container_id}: {str(e)}")
                continue
        
        components = await db.get_child_nodes(int(container_id), "Architecture")
        contanier_details = {
            "name": container.get("properties", {}).get("Title"),
            "description": container.get("properties", {}).get("Description"),
            "repository": project_repositories.get(container_id),
            "components": {}
        }


        for component in components:
            component_id = str(component.get("id"))
            component_info = await db.get_work_items(int(component_id))
            
            if component_info:
                contanier_details["components"][component_id] = {
                    "name": component_info.get("component_name"),
                    "description": component_info.get("description"),
                    "repository_name": component_info.get("repository_name"),
                    "root_folder": component_info.get("root_folder"),
                    "design": component_info.get("design", {}),
                    "algorithms": component_info.get("Algorithm", []),
                    "pseudocode": component_info.get("Pseudocode", [])
                }
        work_items["containers"].append(contanier_details)

    project_details["repositories"] = json.dumps(project_repositories)

    return project_details, work_items

async def task_execute(project_id: int, container_ids: list, db: NodeDB = None, test_case: bool = False, mongo_db: MongoClient = None):
    from app.routes.repository_route import create_github_repository, create_gitlab_repository
    if not db:
        db = get_node_db()
        
    if not mongo_db:
        mongo_db = get_mongo_db().db
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    


    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        print(generate_timestamp(),"Project not configured")
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Load project repositories
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    print(generate_timestamp(),"container_ids", container_ids)
    print(generate_timestamp(),project_repositories)
    project_details["current_repositories"] = []
    # Check if container repository exists, if not create one
    container_details_hash_map = {}
    async def process_container(container_id):
        container_node = await db.get_node_by_id(container_id)
        container_details = container_node.get("properties")
        
        container_repository = project_repositories.get(str(container_id))
        container_details_hash_map[str(container_id)] = container_details
        if not container_repository:
            try:
                if not os.getenv("encrypted_scm_id"):
                    repository_response = await create_repository_in_workspace(
                        project_id=project_id,
                        container_id=container_id,
                        db=db
                    )
                else: 


                    encrypted_scm_id = os.getenv("encrypted_scm_id")
                    scm_id = decrypt_string(encrypted_scm_id)
                    config = mongo_db["scm_configurations"].find_one({"scm_id": scm_id})
                    config['encrypted_scm_id'] = encrypted_scm_id
                    scm_config = SCMConfiguration(**config) if config else None
                    repository_name = name_to_slug(
                        f"{project_details.get('Title', project_details.get('Name'))}"
                    )
                    repository_name = f'{repository_name}-{container_id}'
                    if config.get("scm_type") == SCMType.GITLAB.value:
                        repository_response = await create_gitlab_repository(repository_name, config=scm_config, is_private=True)
                    else:
                        repository_response = await create_github_repository(repository_name, config=scm_config, is_private=False)
             
                        
                    print(generate_timestamp(),"Repository Response from SCM :", repository_response)
                   
                repository_response["container_id"] = str(container_id)
                repository_response["container_name"] = str(container_details.get("Title").replace(' ','').replace('-','_'))
                
                return {
                    "container_id": str(container_id),
                    "repository_data": repository_response,
                    "is_new": True
                }
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=str(e))
        else:
            container_repository["container_id"] = str(container_id)
            container_repository["container_name"] = str(container_details.get("Title"))
            repository_metadata = container_repository.copy()
                            
                # Handle access token based on token path
            if repository_metadata.get("access_token_path") == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
                access_token = repository_metadata.get("access_token")
                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                    access_token = settings.GITHUB_ACCESS_TOKEN
                repository_metadata["access_token"] = access_token

            elif repository_metadata.get("encrypted_scm_id") or repository_metadata.get("scm_id"):
                if repository_metadata.get("encrypted_scm_id"):
                    scm_id = repository_metadata.get("encrypted_scm_id")
                    decrypted_scm_id = decrypt_string(scm_id)
                    scm_id = decrypted_scm_id
                else:
                    decrypted_scm_id = repository_metadata.get("scm_id")
                    scm_id = decrypted_scm_id
                config = mongo_db["scm_configurations"].find_one({"scm_id": scm_id})
                config['encrypted_scm_id'] = repository_metadata.get("encrypted_scm_id")
                scm_config = SCMConfiguration(**config) if config else None
                if not config:
                    raise Exception(f"Failed to get SCM configuration for container {container_id}")
                access_token = scm_config.credentials.access_token
                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                    access_token = settings.GITHUB_ACCESS_TOKEN
                repository_metadata["access_token"] = access_token
                
            tenant_id = get_tenant_id()
            task_id = os.environ.get("task_id")
            clone_thread = threading.Thread(
                target=_run_clone_in_background,
                args=(
                    repository_metadata,
                    tenant_id,
                    task_id,
                    repository_metadata["repositoryName"]
                ),
                daemon=True
            )
            clone_thread.start()
            print(generate_timestamp(),generate_timestamp(), f"Started background clone thread for repository: {repository_metadata['repositoryName']}")

                
        
        container_details["repository"] = container_repository
        return {
            "container_id": str(container_id),
            "repository_data": container_repository,
            "is_new": False
        }

    # Process all containers in parallel
    container_results = await asyncio.gather(*[process_container(container_id) for container_id in container_ids])
    ports_to_map = {
        "frontend": settings.FRONTEND_PORT,
        "backend": settings.BACKEND_PORT,
        "database": settings.DATABASE_PORT,
    }
       
    def _get_container_dependancy(container_type, container_name):
        if container_type.lower() in 'backend':
            return [container_name+'_'+'database']
        if container_type.lower() in 'frontend':
            return [container_name+'_'+'backend']
        if container_type.lower() in 'database':
            return [container_name+'_'+'backend']
        return []
    
    def _def_interfaces(container_type):
        if container_type.lower() in 'backend':
            return "API endpoints based of the project description"
        if container_type.lower() in 'frontend':
            return "API requests to the backend container"
        if container_type.lower() in 'database':
            return "Database Accessible by the backend for data persistence"
        return ""
    
    
    def get_container_port(container):
        container_type = get_container_type(container)
        if container_type == ContainerType.FRONTEND.value:
            return settings.FRONTEND_PORT
        elif container_type == ContainerType.BACKEND.value:
            return settings.BACKEND_PORT
        elif container_type == ContainerType.DATABASE.value:
            return settings.DATABASE_PORT
        elif container_type == ContainerType.MOBILE.value:
            return None
        
        return ports_to_map.pop(ContainerType.FRONTEND.value, 0)
    
    if not project_details.get("Manifest") or project_details.get("Scope"):
        print(generate_timestamp(),"-- MANIFEST", container_details_hash_map)
        containers = []
        for container_id, container_details in container_details_hash_map.items():
            container_details["container_id"] = container_id
            container_name = container_details.get("Title").replace(' ','').replace('-','_')
            container_type = get_container_type(container_details)
            
            container = {
            "container_name": container_name,
            "description": container_details.get("Description", ""),
            "interfaces": _def_interfaces(container_type),
            "container_type": container_type,
            "dependent_containers": _get_container_dependancy(get_container_type(container_details), container_details.get("Title").replace(" ", "").replace('-','_')), 
            "workspace": "",
            "container_root":  "",
            "port": get_container_port(container_details),
            "framework": container_details.get("framework", ""),
            "type": container_details.get("type", ""),
            "buildCommand": container_details.get("buildCommand", ""),
            "startCommand": container_details.get("startCommand", ""),
            "installCommand": container_details.get("installCommand", ""),
            "lintCommand": container_details.get("lintCommand", ""),
            "working_dir": container_details.get("working_dir", ""),
            "container_details": {'features': container_details.get("UserInteractions","")},
            "lintConfig": container_details.get("lintConfig", ""),
            "routes": container_details.get("routes", []),
            "apiSpec": container_details.get("apiSpec", ""),
            "auth": container_details.get("auth"),
            "schema": container_details.get("schema", ""),
            "migrations": container_details.get("migrations", ""),
            "seed": container_details.get("seed", ""),
            "env": container_details.get("env", {}),
            "private": container_details.get("private", {})
            }
            if container['container_type'] == 'mobile':
                container.pop('port', None)
                
            containers.append(container)
        
        
        project_schema = {
            "overview": {
                "project_name": project_details.get("Title", ""),
                "description": project_details.get("Description", ""),
                "third_party_services": []
            },
            "containers": containers
        }
        
        print(generate_timestamp(),'Project Schema here', project_schema)
        project_schema = dict_to_project_schema(project_schema)
            
        manifest_string = convert_manifest_to_yaml_string(project_schema)
        project_details["Manifest"] = manifest_string
    # Update project_repositories and current_repositories based on results
    for result in container_results:
        container_id = result["container_id"]
        repository_data = result["repository_data"]
        is_new = result["is_new"]
        
        if is_new:
            project_repositories[container_id] = repository_data
        
        project_details["current_repositories"].append(repository_data)
            
    await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )

    if test_case:
        work_items = await db.get_work_items_for_testcase(project_id)
    else:
        work_items = await db.get_work_items_for_container(container_id=container_id)


    return project_details, work_items