import io
import pdfplumber
import re

def extract_text_from_pdf(content):
    """
    Extract text from PDF content with fallback methods for better reliability.
    """
    print("Starting PDF text extraction...")

    # Try PyMuPDF first (more reliable for many PDFs)
    pymupdf_result = _extract_text_with_pymupdf_fallback(content)
    if pymupdf_result and pymupdf_result.strip():
        print("Successfully extracted text using PyMuPDF")
        return pymupdf_result

    print("PyMuPDF extraction failed or returned empty, trying pdfplumber...")

    # Try pdfplumber as secondary method
    try:
        with pdfplumber.open(io.BytesIO(content)) as pdf_inv:
            no_of_pages = len(pdf_inv.pages)
            consolidated_text = ""
            successful_pages = 0

            for page_no in range(0, no_of_pages):
                page_text = None

                # Method 1: Standard extraction with safe parameters
                try:
                    data = pdf_inv.pages[page_no].extract_text(
                        x_tolerance=3,
                        y_tolerance=3,
                        layout=False,
                        x_density=7.25,
                        y_density=13
                    )
                    if data and data.strip():
                        page_text = data
                        print(f"Page {page_no + 1}: Standard extraction successful")
                except Exception as e:
                    print(f"Page {page_no + 1}: Standard extraction failed: {str(e)}")

                # Method 2: Bbox extraction fallback
                if not page_text:
                    try:
                        page_bbox = pdf_inv.pages[page_no].bbox
                        data = pdf_inv.pages[page_no].within_bbox(page_bbox).extract_text()
                        if data and data.strip():
                            page_text = data
                            print(f"Page {page_no + 1}: Bbox extraction successful")
                    except Exception as e:
                        print(f"Page {page_no + 1}: Bbox extraction failed: {str(e)}")

                # Method 3: Simple extraction without parameters
                if not page_text:
                    try:
                        data = pdf_inv.pages[page_no].extract_text()
                        if data and data.strip():
                            page_text = data
                            print(f"Page {page_no + 1}: Simple extraction successful")
                    except Exception as e:
                        print(f"Page {page_no + 1}: Simple extraction failed: {str(e)}")

                # Add page text if any method succeeded
                if page_text:
                    consolidated_text += f"Page {page_no + 1}:\n{page_text}\n\n"
                    successful_pages += 1
                else:
                    print(f"Page {page_no + 1}: All extraction methods failed")
                    consolidated_text += f"Page {page_no + 1}:\n[Text extraction failed for this page]\n\n"

            print(f"pdfplumber extraction completed: {successful_pages}/{no_of_pages} pages successful")

            if consolidated_text.strip():
                return consolidated_text.strip()
            else:
                print("No text extracted with pdfplumber")
                return None

    except Exception as e:
        print(f"Error with pdfplumber: {str(e)}")
        return None


def _extract_text_with_pymupdf_fallback(content):
    """
    Primary method using PyMuPDF (fitz) for PDF text extraction.
    """
    try:
        import fitz  # PyMuPDF
        print("Attempting PyMuPDF extraction...")

        with fitz.open(stream=content, filetype="pdf") as doc:
            consolidated_text = ""
            successful_pages = 0
            total_pages = len(doc)

            for page_num in range(total_pages):
                try:
                    page = doc[page_num]

                    # Method 1: Standard text extraction
                    text = page.get_text()
                    if text and text.strip():
                        consolidated_text += f"Page {page_num + 1}:\n{text}\n\n"
                        successful_pages += 1
                        continue

                    # Method 2: Try different text extraction options
                    text = page.get_text("text")  # Explicit text mode
                    if text and text.strip():
                        consolidated_text += f"Page {page_num + 1}:\n{text}\n\n"
                        successful_pages += 1
                        continue

                    # Method 3: Try blocks extraction for complex layouts
                    blocks = page.get_text("blocks")
                    if blocks:
                        page_text = ""
                        for block in blocks:
                            if len(block) >= 5 and isinstance(block[4], str):
                                page_text += block[4] + " "
                        if page_text.strip():
                            consolidated_text += f"Page {page_num + 1}:\n{page_text.strip()}\n\n"
                            successful_pages += 1
                            continue

                    print(f"PyMuPDF: No text found on page {page_num + 1}")

                except Exception as page_error:
                    print(f"PyMuPDF: Error extracting page {page_num + 1}: {str(page_error)}")

            print(f"PyMuPDF extraction completed: {successful_pages}/{total_pages} pages successful")

            if consolidated_text.strip():
                return consolidated_text.strip()
            else:
                print("PyMuPDF: No text extracted from any page")
                return None

    except ImportError:
        print("PyMuPDF (fitz) not available for PDF extraction")
        return None
    except Exception as e:
        print(f"PyMuPDF extraction error: {str(e)}")
        return None



