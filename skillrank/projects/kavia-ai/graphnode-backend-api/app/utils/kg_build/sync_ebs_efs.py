
#!/usr/bin/env python3
"""
Standalone script for syncing directories while respecting gitignore rules.
This script avoids the circular import issues with FastAPI.
"""

import os
import shutil
from pathlib import Path
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import get_tenant_id
from app.core.task_framework import Task
from app.routes.code_query import generate_random_prefix
from app.tasks import clone
from app.utils.kg_build.knowledge import  KnowledgeCodeBase

def get_gitignore_patterns(source_dir):
    """
    Get patterns from .gitignore file in the source directory
    
    Args:
        source_dir (str): Source directory path
        
    Returns:
        list: List of gitignore patterns
    """
    gitignore_path = os.path.join(source_dir, '.gitignore')
    patterns = []
    
    if os.path.exists(gitignore_path):
        try:
            with open(gitignore_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        patterns.append(line)
            print(f"Found {len(patterns)} patterns in .gitignore")
        except Exception as e:
            print(f"Error reading .gitignore: {str(e)}")
    
    return patterns

def is_ignored_by_gitignore(path, source_dir, patterns):
    """
    Check if a path matches any gitignore pattern
    
    Args:
        path (str): Path to check
        source_dir (str): Source directory path
        patterns (list): List of gitignore patterns
        
    Returns:
        bool: True if the path should be ignored, False otherwise
    """
    import os
    import fnmatch
    
    if not patterns:
        return False
    
    # Get relative path from source directory
    rel_path = os.path.relpath(path, source_dir)
    # Ensure forward slashes for consistency (gitignore uses forward slashes)
    rel_path = rel_path.replace('\\', '/')
    
    # Special handling for node_modules - always ignore any path containing node_modules
    if 'node_modules' in rel_path.split('/'):
        return True
        
    # Add trailing slash to directory paths for proper matching
    is_dir = os.path.isdir(path)
    if is_dir and not rel_path.endswith('/'):
        rel_path += '/'
    
    # Path segments for hierarchical matching
    path_parts = rel_path.split('/')
    
    for pattern in patterns:
        # Skip empty patterns or comments
        if not pattern or pattern.startswith('#'):
            continue
            
        # Normalize pattern
        pattern = pattern.strip().replace('\\', '/')
        
        # Remove leading ./ if present
        if pattern.startswith('./'):
            pattern = pattern[2:]
        
        # Negation pattern - skip it for now
        if pattern.startswith('!'):
            continue
            
        # Handle directory-specific patterns (trailing slash)
        if pattern.endswith('/'):
            # If checking a file, this pattern can't match
            if not is_dir:
                continue
                
            # Remove the trailing slash for matching
            pattern = pattern[:-1]
        
        # Special cases
        if pattern == 'node_modules' or pattern == 'node_modules/':
            if 'node_modules' in path_parts:
                return True
        
        # Exact match
        if pattern == rel_path:
            return True
            
        # Anchored pattern match (pattern with no wildcards)
        if '*' not in pattern and '?' not in pattern and '/' in pattern:
            if rel_path.startswith(pattern):
                return True
                
            # Check if pattern applies to any subdirectory
            if pattern in rel_path:
                pattern_parts = pattern.split('/')
                for i in range(len(path_parts) - len(pattern_parts) + 1):
                    if path_parts[i:i+len(pattern_parts)] == pattern_parts:
                        return True
                
        # Handle patterns with wildcards
        if '*' in pattern or '?' in pattern:
            # Path-anchored pattern with wildcards
            if '/' in pattern and not pattern.startswith('*'):
                if fnmatch.fnmatch(rel_path, pattern):
                    return True
            # Non-anchored pattern match (applies to any part of the path)
            else:
                # For non-anchored patterns, check each path segment
                base_name = os.path.basename(rel_path.rstrip('/'))
                if fnmatch.fnmatch(base_name, pattern):
                    return True
                    
                # Also check if pattern matches the full path
                if fnmatch.fnmatch(rel_path, f"*{pattern}*"):
                    return True
    
    # Apply negated patterns (patterns starting with !)
    for pattern in patterns:
        if pattern.startswith('!'):
            # Remove the negation mark
            negated = pattern[1:].strip()
            
            # If the path matches a negated pattern, it should NOT be ignored
            if is_ignored_by_gitignore(path, source_dir, [negated]):
                return False
    
    return False

def ensure_dir_permissions(dir_path):
    """
    Try to ensure a directory has write permissions
    
    Args:
        dir_path (str): Directory path
    """
    try:
        # Try to make the directory writable
        os.chmod(dir_path, 0o755)
    except Exception as e:
        print(f"Unable to set permissions on {dir_path}: {str(e)}")

def do_sync(source_project_path, destination_path):
    """
    Move all files from source to destination, respecting only .gitignore rules.
    Include all hidden files (.files).
    
    Args:
        source_project_path (str): Path to the source directory
        destination_path (str): Destination path for the files
    
    Returns:
        tuple: (moved_count, skipped_count)
    """
    try:
        # Convert paths to Path objects for better path handling
        source_path = Path(source_project_path)
        dest_path = Path(destination_path)
        
        # Verify source path exists
        if not source_path.exists() or not source_path.is_dir():
            print(f"Source path {source_project_path} does not exist or is not a directory.")
            return (0, 0)
        
        # Create the destination directory if it doesn't exist
        try:
            dest_path.mkdir(parents=True, exist_ok=True)
        except PermissionError as pe:
            print(f"Permission error creating destination directory: {str(pe)}")
            print("Attempting to continue anyway...")
        
        # Get gitignore patterns
        gitignore_patterns = get_gitignore_patterns(str(source_path))
        
        # Track moved and skipped files
        moved_count = 0
        skipped_count = 0
        error_count = 0
        
        # Process all files
        print(f"Moving files from {source_path} to {dest_path}...")
        
        # Walk through the directory structure
        for root, dirs, files in os.walk(source_path):
            # Get current directory path
            current_path = Path(root)
            
            # Check if the directory is ignored by gitignore
            if is_ignored_by_gitignore(str(current_path), str(source_path), gitignore_patterns):
                print(f"Skipping ignored directory: {current_path.relative_to(source_path)}")
                # Remove from dirs to prevent walking into it
                dirs[:] = []
                skipped_count += 1
                continue
            
            # Get relative path from source
            rel_path = current_path.relative_to(source_path)
            
            # Create corresponding directory in destination
            if str(rel_path) != '.':
                dest_dir = dest_path / rel_path
                try:
                    dest_dir.mkdir(parents=True, exist_ok=True)
                except PermissionError as pe:
                    print(f"Permission error creating directory {dest_dir}: {str(pe)}")
                    error_count += 1
                    # Skip this directory
                    continue
            else:
                dest_dir = dest_path
            
            # Include all files, including hidden ones
            all_files = []
            try:
                # Get visible files from os.walk
                all_files.extend(files)
                
                # Get hidden files that might not be included in the os.walk results
                for f in os.listdir(root):
                    f_path = os.path.join(root, f)
                    if os.path.isfile(f_path) and f not in files:
                        all_files.append(f)
            except PermissionError as pe:
                print(f"Permission error listing files in {root}: {str(pe)}")
                error_count += 1
                continue
            
            # Process each file
            for file in all_files:
                source_file = current_path / file
                
                # Skip if the file is ignored by gitignore
                if is_ignored_by_gitignore(str(source_file), str(source_path), gitignore_patterns):
                    print(f"Skipping ignored file: {source_file.relative_to(source_path)}")
                    skipped_count += 1
                    continue
                
                dest_file = dest_dir / file
                
                # Try to create parent directory with appropriate permissions if needed
                parent_dir = os.path.dirname(dest_file)
                if not os.path.exists(parent_dir):
                    try:
                        os.makedirs(parent_dir, exist_ok=True)
                    except PermissionError:
                        print(f"Cannot create directory {parent_dir} - permission denied")
                        error_count += 1
                        continue
                
                # Copy the file (use shutil.copy2 to preserve metadata)
                try:
                    shutil.copy2(source_file, dest_file)
                    moved_count += 1
                    print(f"Moved: {source_file.relative_to(source_path)}")
                except PermissionError as pe:
                    print(f"Permission error when copying {source_file.relative_to(source_path)}: {str(pe)}")
                    # Try to continue by making the target directory writable
                    try:
                        # Create parent directories with proper permissions
                        os.makedirs(os.path.dirname(dest_file), exist_ok=True)
                        # Try copying with less strict permissions
                        shutil.copy(source_file, dest_file)  # copy instead of copy2 (doesn't preserve all metadata)
                        print(f"Successfully copied file with reduced metadata: {source_file.relative_to(source_path)}")
                        moved_count += 1
                    except Exception as e2:
                        print(f"Still unable to copy {source_file.relative_to(source_path)}: {str(e2)}")
                        skipped_count += 1
                except Exception as e:
                    print(f"Error copying {source_file.relative_to(source_path)}: {str(e)}")
                    skipped_count += 1
        
        print(f"Sync completed: {moved_count} files moved, {skipped_count} files/directories skipped, {error_count} errors")
        return (moved_count, skipped_count)
            
    except Exception as e:
        print(f"Error during sync: {str(e)}")
        import traceback
        traceback.print_exc()
        return (0, 0)

async def get_project_paths(project_id: int, build_id: str = None, user_id: str = None):
    """
    Get source path and build path for a specific project using project_id.
    
    Args:
        project_id (int): The ID of the project
        build_id (str, optional): Specific build ID to filter by
        user_id (str, optional): User ID for database access
        
    Returns:
        dict: A dictionary containing source_path and build_path for each repository
    """
    try:
        from app.core.Settings import settings
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories',
            user_id=user_id
        )
            
        # Get project document from MongoDB
        filter_query = {'project_id': project_id}
        project_doc = await mongo_handler.get_one(
            filter=filter_query,
            db=mongo_handler.db
        )
        
        
        print("Get Project Paths")
        print(project_doc)
        if not project_doc:
            return {"error": f"No project found with ID: {project_id}"}
        
        paths_data = []
        codebases = []
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        for repo in project_doc.get('repositories', []):
            # For local files
            if repo.get('service') == "localFiles":
                if build_id and repo.get('builds', {}).get('build_id') != build_id:
                    continue
                    
                paths_data.append({
                    "repo_name": repo.get('repository_name'),
                    "repo_id": repo.get('repo_id'),
                    "source_path": repo.get('codegen_source_path', None),
                    "destination_path": repo.get('builds', {}).get('destination_path'),
                    "build_id": repo.get('builds', {}).get('build_id')
                })
            # For GitHub or other repos
            else:
                for branch in repo.get('branches', []):
                    
                    build_info = branch.get('builds', {})
                    build_id = build_info.get('build_id')
                    
                    session_id = generate_random_prefix()  + '-' + build_id
                    
                    codebases = [KnowledgeCodeBase(
                        build_info.get('path'),
                        repo.get('repository_name'),
                        'github'
                    )]
                    
                    # If build_id is specified, filter by it
                    if build_id and build_info.get('build_id') != build_id:
                        continue
                        
                    paths_data.append({
                        "repo_name": repo.get('repository_name'),
                        "repo_id": repo.get('repo_id'),
                        "branch_name": branch.get('name'),
                        "source_path": repo.get('codegen_source_path', None),
                        "destination_path": build_info.get('destination_path'),
                        "build_id": build_info.get('build_id'),
                        "kg_status": build_info.get('kg_creation_status')
                    })

                    tenant_id = get_tenant_id()
                    data_dir = os.path.join(root_dir, 'data', tenant_id, str(project_id))
                    mock_current_user = {"cognito:username": user_id} 
                    
                    Task.schedule_task(
                        clone,
                        project_id=project_id,
                        build_session_id=session_id,
                        build_id=build_id,
                        data_dir=data_dir,
                        repository=repo,  # Pass single repository instead of list
                        tenant_id=tenant_id,
                        current_user=user_id, 
                        upstream=False,
                        current_user_obj = mock_current_user
                    )

        return {"status": "success", "paths": paths_data}
        
    except Exception as e:
        return {"error": f"Failed to get project paths: {str(e)}"}
    