"""
Email Template Loader Utility

This module provides functions to load and format email templates
from external template files using Jinja2 templating engine.
"""

import os
from typing import Dict, Optional
from pathlib import Path

try:
    from jinja2 import Environment, FileSystemLoader, Template
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False
    print("Warning: Jinja2 not available, falling back to basic string formatting")


def get_template_path(template_name: str, template_type: str) -> str:
    """
    Get the full path to a template file.
    
    Args:
        template_name: Name of the template (e.g., 'welcome_email')
        template_type: Type of template ('html' or 'txt')
        
    Returns:
        Full path to the template file
    """
    current_dir = Path(__file__).parent
    return os.path.join(current_dir, f"{template_name}.{template_type}")


def load_template(template_name: str, template_type: str) -> str:
    """
    Load a template file and return its contents.
    
    Args:
        template_name: Name of the template (e.g., 'welcome_email')
        template_type: Type of template ('html' or 'txt')
        
    Returns:
        Template content as string
        
    Raises:
        FileNotFoundError: If template file doesn't exist
        IOError: If template file cannot be read
    """
    template_path = get_template_path(template_name, template_type)
    
    if not os.path.exists(template_path):
        raise FileNotFoundError(f"Template file not found: {template_path}")
    
    try:
        with open(template_path, 'r', encoding='utf-8') as file:
            return file.read()
    except IOError as e:
        raise IOError(f"Failed to read template file {template_path}: {str(e)}")


def format_template(template_content: str, variables: Dict[str, str]) -> str:
    """
    Format a template with the provided variables using Jinja2 or basic string formatting.
    
    Args:
        template_content: Raw template content with placeholders
        variables: Dictionary of variables to substitute in the template
        
    Returns:
        Formatted template string
    """
    try:
        if JINJA2_AVAILABLE:
            # Use Jinja2 templating
            template = Template(template_content)
            return template.render(**variables)
        else:
            # Fall back to basic string formatting
            return template_content.format(**variables)
    except Exception as e:
        raise ValueError(f"Template formatting error: {str(e)}")


def load_and_format_template(template_name: str,
                           template_type: str,
                           variables: Dict[str, str]) -> str:
    """
    Load a template file and format it with variables in one step.
    Uses Jinja2 FileSystemLoader if available for better performance.
    
    Args:
        template_name: Name of the template (e.g., 'welcome_email')
        template_type: Type of template ('html' or 'txt')
        variables: Dictionary of variables to substitute in the template
        
    Returns:
        Formatted template string
        
    Raises:
        FileNotFoundError: If template file doesn't exist
        IOError: If template file cannot be read
        KeyError: If required template variable is missing
        ValueError: If template formatting fails
    """
    if JINJA2_AVAILABLE:
        return _load_and_format_with_jinja2(template_name, template_type, variables)
    else:
        template_content = load_template(template_name, template_type)
        return format_template(template_content, variables)


def _load_and_format_with_jinja2(template_name: str,
                                template_type: str,
                                variables: Dict[str, str]) -> str:
    """
    Load and format template using Jinja2 FileSystemLoader.
    
    Args:
        template_name: Name of the template (e.g., 'welcome_email')
        template_type: Type of template ('html' or 'txt')
        variables: Dictionary of variables to substitute in the template
        
    Returns:
        Formatted template string
    """
    try:
        # Get the template directory
        template_dir = Path(__file__).parent
        
        # Create Jinja2 environment
        env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=True if template_type == 'html' else False
        )
        
        # Load template
        template_filename = f"{template_name}.{template_type}"
        template = env.get_template(template_filename)
        
        # Render template with variables
        return template.render(**variables)
        
    except Exception as e:
        # Fall back to basic loading if Jinja2 fails
        template_content = load_template(template_name, template_type)
        return format_template(template_content, variables)


def load_welcome_email_templates(variables: Dict[str, str], with_login: bool = False) -> Dict[str, str]:
    """
    Load and format both HTML and text versions of the welcome email template.
    
    Args:
        variables: Dictionary of variables to substitute in the templates
        with_login: If True, loads the welcome_email_with_login templates
        
    Returns:
        Dictionary with 'html' and 'text' keys containing formatted templates
        
    Raises:
        FileNotFoundError: If template files don't exist
        IOError: If template files cannot be read
        KeyError: If required template variables are missing
        ValueError: If template formatting fails
    """
    try:
        template_name = 'welcome_email_with_login' if with_login else 'welcome_email'
        
        html_content = load_and_format_template(template_name, 'html', variables)
        text_content = load_and_format_template(template_name, 'txt', variables)
        
        return {
            'html': html_content,
            'text': text_content
        }
    except Exception as e:
        template_type = "with login" if with_login else "standard"
        raise Exception(f"Failed to load welcome email templates ({template_type}): {str(e)}")


def load_welcome_email_with_login_templates(variables: Dict[str, str]) -> Dict[str, str]:
    """
    Load and format both HTML and text versions of the welcome email with login template.
    
    Args:
        variables: Dictionary of variables to substitute in the templates
        
    Returns:
        Dictionary with 'html' and 'text' keys containing formatted templates
        
    Raises:
        FileNotFoundError: If template files don't exist
        IOError: If template files cannot be read
        KeyError: If required template variables are missing
        ValueError: If template formatting fails
    """
    return load_welcome_email_templates(variables, with_login=True)


def get_welcome_email_subject() -> str:
    """
    Get the default welcome email subject.
    
    Returns:
        Default subject string
    """
    return "🎉 Welcome to KAVIA AI "


# Template variable validation
REQUIRED_WELCOME_EMAIL_VARIABLES = {
    'user_fullname', 'username', 'temporary_password', 
    'organization_name', 'set_password_url', 'login_url'
}


def validate_welcome_email_variables(variables: Dict[str, str]) -> None:
    """
    Validate that all required variables for welcome email template are present.
    
    Args:
        variables: Dictionary of variables to validate
        
    Raises:
        KeyError: If required variables are missing
    """
    missing_vars = REQUIRED_WELCOME_EMAIL_VARIABLES - set(variables.keys())
    if missing_vars:
        raise KeyError(f"Missing required template variables: {', '.join(missing_vars)}")