from typing import List, Dict, Optional, Union
from botocore.exceptions import ClientError
import boto3
from app.core.Settings import settings
from app.classes.SESHandler import ses_handler



class CognitoUserManager:
    """
    A plug-and-play AWS Cognito User Management class
    that handles user operations with flexible configurations
    """
    def __init__(self,
                 user_pool_id: Optional[str] = None,
                 client_id: Optional[str] = None):
        """
        Initialize Cognito Manager with minimal required credentials
        
        Args:
            aws_access_key_id: AWS access key
            aws_secret_access_key: AWS secret key
            region_name: AWS region
            user_pool_id: Optional - Cognito User Pool ID
            client_id: Optional - Cognito Client ID
        """
        self.cognito = boto3.client(
            'cognito-idp',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self._user_pool_id = user_pool_id
        self._client_id = client_id

    @property
    def user_pool_id(self) -> str:
        """Get user pool ID, fetch from AWS if not provided"""
        if not self._user_pool_id:
            response = self.cognito.list_user_pools(MaxResults=60)
            if response['UserPools']:
                self._user_pool_id = response['UserPools'][0]['Id']
            else:
                raise ValueError("No user pools found in the account")
        return self._user_pool_id

    @property
    def client_id(self) -> str:
        """Get client ID, fetch from AWS if not provided"""
        if not self._client_id:
            response = self.cognito.list_user_pool_clients(
                UserPoolId=self.user_pool_id,
                MaxResults=60
            )
            if response['UserPoolClients']:
                self._client_id = response['UserPoolClients'][0]['ClientId']
            else:
                raise ValueError("No clients found in the user pool")
        return self._client_id

    def get_user_by_identifier(self, identifier: str) -> Dict:
        """
        Get user by either username or email
        
        Args:
            identifier: Either username or email
            
        Returns:
            Dict containing user information
        """
        try:
            # Try as username first
            return self.get_user(identifier)
        except Exception as e:
            if 'UserNotFoundException' in str(e):
                # If not found, try to find by email
                users = self.list_users(filter_key='email', filter_value=identifier)
                if users:
                    return users[0]
                raise ValueError(f"No user found with identifier: {identifier}")
            raise e

    def create_user(self, 
                username: Optional[str] = None,
                email: Optional[str] = None,
                temporary_password: Optional[str] = None,
                custom_attributes: Optional[Dict] = None,
                send_welcome_email: bool = True,
                organization_name: Optional[str] = "Kavia",
                set_password_url: Optional[str] = "http://kavia.local:3000/users/set_password?tenant_id=T0000&email={username}",
                login_url: Optional[str] = "http://kavia.local:3000/login?tenant_id=T0000&email={username}",
                user_fullname: str = None,
                use_ses: bool = True,
                ses_source_arn: Optional[str] = settings.SES_SOURCE_ARN,
                ses_from_email: Optional[str] = settings.SES_FROM_EMAIL,
                ses_reply_to: Optional[str] = settings.SES_REPLY_TO,
               ) -> Dict:
        """
        Create a new user with flexible parameters
        """
        try:
            if not user_fullname:
                if 'Name' in custom_attributes:
                    user_fullname = str(custom_attributes['Name']).split(' ')[0]
                elif email:
                    user_fullname = email.split('@')[0]
                else:
                    user_fullname = username

            if not username and not email:
                raise ValueError("Either username or email must be provided")
                
            final_username = username or email
            
            if not temporary_password:
                import secrets
                chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + \
                        'abcdefghijklmnopqrstuvwxyz' + \
                        '0123456789' + \
                        '!@#$%^&*'
                temporary_password = ''.join(secrets.choice(chars) for _ in range(12))
            
            attributes = []
            if email:
                attributes.extend([
                    {'Name': 'email', 'Value': email},
                    {'Name': 'email_verified', 'Value': 'true'}
                ])
                    
            if custom_attributes:
                attributes.extend([
                    {
                        'Name': f"custom:{key}" if not key.startswith('custom:') else key, 
                        'Value': str(value)
                    }
                    for key, value in custom_attributes.items()
                ])

            params = {
                'UserPoolId': self.user_pool_id,
                'Username': final_username,
                'UserAttributes': attributes,
                'TemporaryPassword': temporary_password,
                'DesiredDeliveryMediums': ['EMAIL'],
                'ForceAliasCreation': True,
                'MessageAction': 'SUPPRESS'  # Always suppress Cognito's email since we'll send our own
            }

            response = self.cognito.admin_create_user(**params)

            # Set permanent password so user doesn't need to reset upon first login
            self.cognito.admin_set_user_password(
                UserPoolId=self.user_pool_id,
                Username=final_username,
                Password=temporary_password,
                Permanent=True
            )
            
            # Send welcome email using SESHandler if requested
            if send_welcome_email and email:
                try:
                    self._send_welcome_email(
                        email=email,
                        username=final_username,
                        temporary_password=temporary_password,
                        user_fullname=user_fullname,
                        organization_name=organization_name,
                        set_password_url=set_password_url.format(username=final_username),
                        login_url=login_url.format(username=final_username),
                        ses_from_email=ses_from_email,
                        ses_reply_to=ses_reply_to
                    )
                except Exception as email_error:
                    print(f"Warning: User created but email failed to send: {str(email_error)}")
            
            return response['User']

        except ClientError as e:
            error = e.response['Error']
            if error['Code'] == 'UsernameExistsException':
                raise ValueError(f"User exists: {final_username}")
            elif error['Code'] == 'InvalidParameterException':
                raise ValueError(f"Invalid parameters: {error['Message']}")
            elif error['Code'] == 'TooManyRequestsException':
                raise Exception("Rate limit exceeded")
            raise Exception(f"Failed to create user: {error['Message']}")

    def _send_welcome_email(self,
                           email: str,
                           username: str,
                           temporary_password: str,
                           user_fullname: str,
                           organization_name: str,
                           set_password_url: str,
                           login_url: str,
                           ses_from_email: Optional[str] = None,
                           ses_reply_to: Optional[str] = None) -> None:
        """
        Send welcome email using SESHandler
        """
        subject = "🎉 Welcome to KAVIA AI "
        
        # HTML email content
        html_content = f"""
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Welcome to KAVIA AI</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        
        /* Mobile responsive styles */
        @media only screen and (max-width: 600px) {{
            .email-container {{
                width: 100% !important;
                max-width: 100% !important;
            }}
            .content-padding {{
                padding: 20px !important;
            }}
            .header-padding {{
                padding: 20px 20px 16px 20px !important;
            }}
            .cta-padding {{
                padding: 16px 20px 20px 20px !important;
            }}
            .contact-padding {{
                padding: 20px 20px 0 20px !important;
            }}
            .footer-padding {{
                padding: 20px !important;
            }}
            .outer-padding {{
                padding: 20px 15px !important;
            }}
        }}
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #F4F3F3; font-family: 'Inter', Arial, Helvetica, sans-serif;">
    <!-- Main Container -->
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #F4F3F3;">
        <tr>
            <td align="center" class="outer-padding" style="padding: 40px 30px;">
                <!-- Email Content Container -->
                <table border="0" cellpadding="0" cellspacing="0" width="600" class="email-container" style="background-color: #ffffff; max-width: 600px; border-radius: 6px; overflow: hidden;">
                    
                    <!-- Header Section -->
                    <tr>
                        <td align="center" class="header-padding" style="padding: 24px 32px 20px 32px;">
                            <!-- Logo -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <table border="0" cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td style="font-size: 24px;  font-weight: bold; font-family: 'Inter', Arial, sans-serif;">
                                                    <span style="vertical-align: middle; display: inline-block; width: 24px; height: 24px;">
                                                        <!-- Paste your SVG code below, replacing the example -->
                                                        <svg width="21" height="24" viewBox="0 0 21 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <rect width="21" height="24" fill="#ffffff"/>
                                                            <path d="M19.2263 15.224C17.0527 14.3496 14.9359 13.3472 12.8639 12.26C12.7047 12.1752 12.5463 12.0896 12.3879 12.0024C14.5999 10.7928 16.8887 9.7256 19.2263 8.776C20.2639 8.3872 20.6359 7.0224 19.9263 6.1648C19.3471 5.4408 18.2911 5.3224 17.5671 5.9016C15.7231 7.3472 13.7967 8.6792 11.8191 9.9296C11.6663 10.0256 11.5127 10.1192 11.3583 10.2128C11.4175 7.692 11.6375 5.1768 11.9831 2.6776C12.1655 1.5848 11.1687 0.579999 10.0711 0.765599C9.18632 0.899999 8.56792 1.7024 8.65192 2.5824C9.27832 5.3616 9.63832 8.228 9.69592 11.1584C9.69832 11.2784 9.70072 11.3984 9.70232 11.5184C9.70392 11.6552 9.70472 11.7912 9.70552 11.928C9.70552 11.952 9.70552 11.976 9.70552 12C9.70552 12.0224 9.70552 12.044 9.70552 12.0664C9.70552 12.068 9.70552 12.0696 9.70552 12.0712C9.70552 12.2176 9.70392 12.364 9.70232 12.5104C9.70232 12.5312 9.70232 12.5528 9.70152 12.5736C9.70152 12.6272 9.69992 12.68 9.69912 12.7336C9.64792 15.7016 9.28712 18.604 8.65272 21.4176C8.56952 22.2976 9.18712 23.1 10.0719 23.2344C11.1695 23.42 12.1663 22.416 11.9839 21.3224C11.6383 18.8248 11.4183 16.3112 11.3591 13.792C13.5111 15.1024 15.5783 16.5504 17.5679 18.0984C18.4231 18.8032 19.7911 18.4416 20.1791 17.3984C20.5167 16.5352 20.0911 15.5608 19.2271 15.2232L19.2263 15.224Z" fill="#F26A1B"/>
                                                            <path d="M4.93202 13.6872C4.64322 13.8224 4.35362 13.9552 4.06242 14.0856C3.18722 14.4784 2.31122 14.8688 1.41922 15.2232C0.385622 15.6136 0.0152218 16.9728 0.720822 17.8296C1.29762 18.5552 2.35362 18.6752 3.07922 18.0976C3.33042 17.8976 3.58482 17.704 3.84082 17.5104C4.09762 17.3192 4.35282 17.1272 4.61122 16.94C5.12642 16.5632 5.64322 16.188 6.16642 15.8232C7.37922 14.9304 6.32402 13.0808 4.93202 13.6856V13.6872Z" fill="#F26A1B"/>
                                                            <path d="M6.16557 8.1752C5.90397 7.9928 5.64397 7.808 5.38557 7.6216C4.60797 7.06 3.83197 6.4968 3.07837 5.9016C2.22397 5.2008 0.860765 5.5608 0.471965 6.6C0.131965 7.4624 0.555966 8.4368 1.41837 8.7768C1.71677 8.8944 2.01277 9.0184 2.30717 9.1424C2.60077 9.2688 2.89517 9.3944 3.18637 9.5248C3.77037 9.7824 4.35357 10.0424 4.93117 10.3136C6.31037 10.9176 7.38557 9.0792 6.16557 8.176V8.1752Z" fill="#F26A1B"/>
                                                        </svg>
                                                        
                                                      </span><span style="position: relative; top: 2px; font-weight: 100;">KAVIA AI</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center">
                                        <h1 style="margin: 0; margin-top: 10px;font-size: 24px; font-family: 'Inter', Arial, sans-serif; font-weight: 600; color: #000000; line-height: 33.6px;">Welcome to KAVIA AI</h1>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Dark Card with Gradient -->
                    <tr>
                        <td style="padding: 0 32px 13px 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #231F20; border-radius: 12px; overflow: hidden;">
                                <tr>
                                    <td height="226" style="background: #231F20; position: relative;">
                                        <!--[if gte mso 9]>
                                        <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:536px;height:226px;">
                                            <v:fill type="gradient" color="#231F20" color2="#E15E0D" angle="180" />
                                            <v:textbox inset="0,0,0,0">
                                        <![endif]-->
                                        <div style="height: 226px; background: linear-gradient(to bottom, #231F20 0%, #231F20 60%, #E15E0D 150%);">
                                            &nbsp;
                                        </div>
                                        <!--[if gte mso 9]>
                                            </v:textbox>
                                        </v:rect>
                                        <![endif]-->
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Main Content -->
                    <tr>
                        <td class="content-padding" style="padding: 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <!-- Greeting -->
                                <tr>
                                    <td style="padding-bottom: 12px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            Dear <strong>{user_fullname}</strong>,
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Introduction -->
                                <tr>
                                    <td style="padding-bottom: 12px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            We're excited to have you onboard as part of our <strong>Beta Launch</strong>.
                                        </p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td style="padding-bottom: 30px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            As a friend of the team, you're getting special <strong>early access to a KAVIA Premium account.</strong>
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Platform Description -->
                                <tr>
                                    <td style="padding-bottom: 30px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">
                                            KAVIA is a new AI-powered software development platform, built for experienced developers and beginners.
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Features Header -->
                                <tr>
                                    <td style="padding-bottom: 11px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">
                                            What you can do with KAVIA:
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Features List -->
                                <tr>
                                    <td>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <!-- Feature 1 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Generate & deploy web apps & websites from prompts</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 2 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Build backends, mobile apps and full stack applications</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 3 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Connect to Supabase and public cloud hosted databases</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 4 -->
                                            <tr>
                                                <td style="padding: 10px 0; border-bottom: 1px solid #F4F3F3;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Import existing code to identify gaps, add features and fix bugs</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <!-- Feature 5 -->
                                            <tr>
                                                <td style="padding: 10px 0 0 0;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                        <tr>
                                                            <td width="24" valign="middle">
                                                                <span style="color: #4997B3; font-size: 18px;">✓</span>
                                                            </td>
                                                            <td style="padding-left: 8px;">
                                                                <p style="margin: 0; font-size: 14px; line-height: 19.6px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 500;">Built for beginners and experienced developers</p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- CTA Section -->
                    <tr>
                        <td align="center" class="cta-padding" style="padding: 16px 32px 32px 32px;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <!-- CTA Button -->
                                <tr>
                                    <td align="center" style="padding-bottom: 24px;">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" style="border-radius: 6px;" bgcolor="#F26A1B">
                                                    <a href="https://kavia.ai/startwithkavia" target="_blank" style="font-size: 15px; font-family: 'Inter', Arial, sans-serif; color: #ffffff; text-decoration: none; border-radius: 6px; padding: 12px 48px; border: 1px solid #F26A1B; display: inline-block; font-weight: 600; line-height: 20px;">
                                                        Get started with KAVIA AI <span><svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M0.765035 10.7192L-0.00537109 9.9488L4.46453 5.4789L-0.00537109 1.00899L0.765035 0.238586L6.00535 5.4789L0.765035 10.7192Z" fill="#F4F3F3"/>
                                                            </svg>
                                                            </span>
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <!-- Tutorials Link -->
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            If you need help getting started, take a look at our <a href="https://docs.kavia.app/welcome-guide.pdf" target="_blank" style="color: #191616; font-weight: 500;">Guide Document</a>.
                                        </p>
                                    </td>
                                </tr>
                                
                                <!-- Feedback Request -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            <strong>We'd love your feedback.</strong> Please try out the platform and share your thoughts via this <a href="#" style="color: #191616; font-weight: 600;">short survey</a>.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Contact Section -->
                    <tr>
                        <td class="contact-padding" style="padding: 32px 32px 0 32px; border-top: 1px solid #F4F3F3;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <td style="padding-bottom: 18px;">
                                        <p style="margin: 0; font-size: 15px; line-height: 21px; color: #191616; font-family: 'Inter', Arial, sans-serif;">
                                            If you have any questions or need help, just email us at:<br/>
                                            <strong><EMAIL></strong>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer Section -->
                    <tr>
                        <td align="center" class="footer-padding" style="padding: 32px;">
                            <table border="0" cellpadding="0" cellspacing="0">
                                <!-- Thank You -->
                                <tr>
                                    <td align="center" style="padding-bottom: 7px;">
                                        <p style="margin: 0; font-size: 16px; line-height: 22.4px; color: #191616; font-family: 'Inter', Arial, sans-serif;">Thank You!</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="padding-bottom: 20px;">
                                        <p style="margin: 0; font-size: 20px; line-height: 28px; color: #191616; font-family: 'Inter', Arial, sans-serif; font-weight: 600;">KAVIA AI Team</p>
                                    </td>
                                </tr>
                                
                                <!-- Footer Logo -->
                                <tr>
                                    <td align="center" style="padding-bottom: 16px;">
                                        <span style="font-size: 18px; font-weight: bold; font-family: 'Inter', Arial, sans-serif;">
  <span style="vertical-align: middle; display: inline-block; width: 18px; height: 18px;">
    <svg width="18" height="18" viewBox="0 0 21 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="21" height="24" fill="#ffffff"/>
      <path d="M19.2263 15.224C17.0527 14.3496 14.9359 13.3472 12.8639 12.26C12.7047 12.1752 12.5463 12.0896 12.3879 12.0024C14.5999 10.7928 16.8887 9.7256 19.2263 8.776C20.2639 8.3872 20.6359 7.0224 19.9263 6.1648C19.3471 5.4408 18.2911 5.3224 17.5671 5.9016C15.7231 7.3472 13.7967 8.6792 11.8191 9.9296C11.6663 10.0256 11.5127 10.1192 11.3583 10.2128C11.4175 7.692 11.6375 5.1768 11.9831 2.6776C12.1655 1.5848 11.1687 0.579999 10.0711 0.765599C9.18632 0.899999 8.56792 1.7024 8.65192 2.5824C9.27832 5.3616 9.63832 8.228 9.69592 11.1584C9.69832 11.2784 9.70072 11.3984 9.70232 11.5184C9.70392 11.6552 9.70472 11.7912 9.70552 11.928C9.70552 11.952 9.70552 11.976 9.70552 12C9.70552 12.0224 9.70552 12.044 9.70552 12.0664C9.70552 12.068 9.70552 12.0696 9.70552 12.0712C9.70552 12.2176 9.70392 12.364 9.70232 12.5104C9.70232 12.5312 9.70232 12.5528 9.70152 12.5736C9.70152 12.6272 9.69992 12.68 9.69912 12.7336C9.64792 15.7016 9.28712 18.604 8.65272 21.4176C8.56952 22.2976 9.18712 23.1 10.0719 23.2344C11.1695 23.42 12.1663 22.416 11.9839 21.3224C11.6383 18.8248 11.4183 16.3112 11.3591 13.792C13.5111 15.1024 15.5783 16.5504 17.5679 18.0984C18.4231 18.8032 19.7911 18.4416 20.1791 17.3984C20.5167 16.5352 20.0911 15.5608 19.2271 15.2232L19.2263 15.224Z" fill="#F26A1B"/>
      <path d="M4.93202 13.6872C4.64322 13.8224 4.35362 13.9552 4.06242 14.0856C3.18722 14.4784 2.31122 14.8688 1.41922 15.2232C0.385622 15.6136 0.0152218 16.9728 0.720822 17.8296C1.29762 18.5552 2.35362 18.6752 3.07922 18.0976C3.33042 17.8976 3.58482 17.704 3.84082 17.5104C4.09762 17.3192 4.35282 17.1272 4.61122 16.94C5.12642 16.5632 5.64322 16.188 6.16642 15.8232C7.37922 14.9304 6.32402 13.0808 4.93202 13.6856V13.6872Z" fill="#F26A1B"/>
      <path d="M6.16557 8.1752C5.90397 7.9928 5.64397 7.808 5.38557 7.6216C4.60797 7.06 3.83197 6.4968 3.07837 5.9016C2.22397 5.2008 0.860765 5.5608 0.471965 6.6C0.131965 7.4624 0.555966 8.4368 1.41837 8.7768C1.71677 8.8944 2.01277 9.0184 2.30717 9.1424C2.60077 9.2688 2.89517 9.3944 3.18637 9.5248C3.77037 9.7824 4.35357 10.0424 4.93117 10.3136C6.31037 10.9176 7.38557 9.0792 6.16557 8.176V8.1752Z" fill="#F26A1B"/>
    </svg>
  </span>
  <span style="position: relative; top: 2px; font-weight: 100;">KAVIA AI</span>
</span>
                                    </td>
                                </tr>
                                
                                <!-- Social Icons -->
                                <!-- <tr>
                                    <td align="center" style="padding-bottom: 12px;">
                                        <table border="0" cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td style="padding: 0 8px;">
                                                    <a href="#" style="color: #484546; text-decoration: none; font-size: 18px;">in</a>
                                                </td>
                                                <td style="padding: 0 8px;">
                                                    <a href="#" style="color: #767172; text-decoration: none; font-size: 18px;">𝕏</a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr> -->
                                
                                <!-- Copyright and Links -->
                                <tr>
                                    <td align="center">
                                        <p style="margin: 0; font-size: 12px; line-height: 16.8px; color: #555555; font-family: Arial, sans-serif;">
                                            © 2025 KAVIA | <a href="https://kavia.ai/privacy" target="_blank" style="color: #555555;">Privacy Policy</a> | <a href="#" style="color: #555555;">Unsubscribe</a>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <!-- End Email Content Container -->
            </td>
        </tr>
    </table>
    <!-- End Main Container -->
</body>
</html>"""

        # Plain text content
        text_content = f"""
Hello {user_fullname},

Welcome to {organization_name}! We're excited to have you on board.

Your Login Credentials:
Username: {username}
Password: {temporary_password}

Set Password URL: {set_password_url}

Login URL (after setting password): {login_url}

Note: Please keep this information secure and delete after use.

Best regards,
{organization_name} Team
"""

        # Send email using SESHandler
        reply_to_list = [ses_reply_to] if ses_reply_to else None
        
        ses_handler.send_email_api(
            to_addresses=[email],
            subject=subject,
            body_text=text_content,
            body_html=html_content,
            sender=ses_from_email,
            reply_to=reply_to_list
        )

    def update_user_attributes(self, 
                             identifier: str,
                             attributes: Dict,
                             is_custom: bool = True) -> Dict:
        """
        Update user attributes with simplified dictionary input
        
        Args:
            identifier: Username or email
            attributes: Dictionary of attributes to update
            is_custom: Whether these are custom attributes
            
        Returns:
            Updated user information
        """
        user = self.get_user_by_identifier(identifier)
        username = user['Username']
        
        formatted_attributes = []
        for key, value in attributes.items():
            if key =="picture" and value=="":
                formatted_attributes.append({
                    'Name': 'picture',
                    'Value': ''
                })
                continue
            if not value:
                continue
            if is_custom and not key.startswith('custom:'):
                key = f'custom:{key}'
            formatted_attributes.append({
                'Name': key,
                'Value': str(value)
            })
            
        try:
            self.cognito.admin_update_user_attributes(
                UserPoolId=self.user_pool_id,
                Username=username,
                UserAttributes=formatted_attributes
            )
            return self.get_user(username)
        except ClientError as e:
            raise Exception(f"Failed to update attributes: {str(e)}")

    def list_users(self, 
                  filter_key: Optional[str] = None,
                  filter_value: Optional[str] = None,
                  limit: int = 60) -> List[Dict]:
        """
        List users with optional filtering
        
        Args:
            filter_key: Optional attribute to filter on
            filter_value: Optional value to filter by
            limit: Maximum number of users to return
            
        Returns:
            List of user information dictionaries
        """
        try:
            params = {
                'UserPoolId': self.user_pool_id,
                'Limit': limit
            }
            
            if filter_key and filter_value:
                params['Filter'] = f"{filter_key} = \"{filter_value}\""
                
            response = self.cognito.list_users(**params)
            return response['Users']
        except ClientError as e:
            raise Exception(f"Failed to list users: {str(e)}")

    def get_user(self, username: str) -> Dict:
        """Get user details by username"""
        try:
            return self.cognito.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=username
            )
        except ClientError as e:
            raise Exception(f"Failed to get user: {str(e)}")

    def delete_user(self, identifier: str) -> None:
        """Delete user by username or email"""
        try:
            user = self.get_user_by_identifier(identifier)
            self.cognito.admin_delete_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
        except ClientError as e:
            raise Exception(f"Failed to delete user: {str(e)}")

    def enable_user(self, identifier: str) -> Dict:
        """
        Enable a user by username or email
        
        Args:
            identifier: Username or email of the user to enable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If enabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_enable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to enable user: {str(e)}")
    
    def disable_user(self, identifier: str) -> Dict:
        """
        Disable a user by username or email
        
        Args:
            identifier: Username or email of the user to disable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If disabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_disable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to disable user: {str(e)}")

    def get_user_attributes(self, identifier: str, custom_only: bool = False) -> Dict:
        """
        Get user attributes in a simplified dictionary format
        
        Args:
            identifier: Username or email
            custom_only: Whether to return only custom attributes
            
        Returns:
            Dictionary of user attributes
        """
        user = self.get_user_by_identifier(identifier)
        attributes = {}
        
        for attr in user.get('UserAttributes', []):
            name = attr['Name']
            if custom_only and not name.startswith('custom:'):
                continue
            attributes[name] = attr['Value']
            
        return attributes
    
    def configure_email_templates(self,
                                email_subject: str = "Welcome to Our Application!",
                                email_message: str = None,
                                use_ses: bool = True,
                                ses_source_arn: Optional[str] = None,
                                ses_from_email: Optional[str] = None,
                                ses_reply_to: Optional[str] = None) -> Dict:
        """
        Configure User Pool email templates for welcome messages
        
        Args:
            email_subject: Subject line for welcome emails
            email_message: Custom message template. Available variables:
                {username} - the username
                {####} - the temporary password
            use_ses: Whether to use Amazon SES for sending emails
            ses_source_arn: ARN of the SES identity to use (required if use_ses is True)
            ses_from_email: Email address to use as the sender (required if use_ses is True)
            ses_reply_to: Email address to use for replies (optional)
                
        Returns:
            Dict containing the API response
        """
        if email_message is None:
            email_message = """
Hello {username},

Welcome to our application! We're excited to have you on board.

Your temporary login credentials are:
Username: {username}
Temporary Password: {####}

Please log in and change your password at your earliest convenience.

Best regards,
Your App Team"""

        try:
            email_config = {}
            
            if use_ses:
                if not ses_source_arn or not ses_from_email:
                    raise ValueError("ses_source_arn and ses_from_email are required when use_ses is True")
                
                email_config = {
                    'EmailSendingAccount': 'DEVELOPER',
                    'SourceArn': ses_source_arn,
                    'From': ses_from_email
                }
                
                if ses_reply_to:
                    email_config['ReplyToEmailAddress'] = ses_reply_to
            else:
                email_config = {
                    'EmailSendingAccount': 'COGNITO_DEFAULT'
                }

            response = self.cognito.update_user_pool(
                UserPoolId=self.user_pool_id,
                EmailConfiguration=email_config,
                AdminCreateUserConfig={
                    'AllowAdminCreateUserOnly': True,
                    'InviteMessageTemplate': {
                        'EmailMessage': email_message,
                        'EmailSubject': email_subject
                    }
                }
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to configure email templates: {str(e)}")

    def check_if_attribute_exists(self, attribute_name):
        # Describe the user pool to get the schema
        response = self.cognito.describe_user_pool(UserPoolId=self.user_pool_id)
        
        # Get the list of schema attributes
        schema_attributes = response['UserPool']['SchemaAttributes']
        print("SCHEMA ATTRIBUTES: ", schema_attributes)
        
        # Check if the attribute exists in the schema
        for attribute in schema_attributes:
            if attribute['Name'] == attribute_name:
                return True  # Attribute exists
        return False  # Attribute does not exist

    def add_free_tier_to_pool(self):
        # Describe the user pool to get the current schema
        if self.check_if_attribute_exists('custom:free_user'):
            print('Free tier attribute already exist!')
            return

        response = self.cognito.add_custom_attributes(
            UserPoolId=self.user_pool_id,
            CustomAttributes=[
                {
                    'Name': 'free_user',
                    'AttributeDataType': 'Boolean',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False
                }
            ]
        )
        print("Updated schema:", response)