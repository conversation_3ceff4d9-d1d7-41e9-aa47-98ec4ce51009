from typing import List, Dict, Optional, Union
from botocore.exceptions import ClientError
import boto3
from app.core.Settings import settings
from app.classes.SESHandler import ses_handler
from app.utils.aws.email_templates import (
    load_welcome_email_templates,
    load_welcome_email_with_login_templates,
    get_welcome_email_subject,
    validate_welcome_email_variables
)



class CognitoUserManager:
    """
    A plug-and-play AWS Cognito User Management class
    that handles user operations with flexible configurations
    """
    def __init__(self,
                 user_pool_id: Optional[str] = None,
                 client_id: Optional[str] = None):
        """
        Initialize Cognito Manager with minimal required credentials
        
        Args:
            aws_access_key_id: AWS access key
            aws_secret_access_key: AWS secret key
            region_name: AWS region
            user_pool_id: Optional - Cognito User Pool ID
            client_id: Optional - Cognito Client ID
        """
        self.cognito = boto3.client(
            'cognito-idp',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION
        )
        self._user_pool_id = user_pool_id
        self._client_id = client_id

    @property
    def user_pool_id(self) -> str:
        """Get user pool ID, fetch from AWS if not provided"""
        if not self._user_pool_id:
            response = self.cognito.list_user_pools(MaxResults=60)
            if response['UserPools']:
                self._user_pool_id = response['UserPools'][0]['Id']
            else:
                raise ValueError("No user pools found in the account")
        return self._user_pool_id

    @property
    def client_id(self) -> str:
        """Get client ID, fetch from AWS if not provided"""
        if not self._client_id:
            response = self.cognito.list_user_pool_clients(
                UserPoolId=self.user_pool_id,
                MaxResults=60
            )
            if response['UserPoolClients']:
                self._client_id = response['UserPoolClients'][0]['ClientId']
            else:
                raise ValueError("No clients found in the user pool")
        return self._client_id

    def get_user_by_identifier(self, identifier: str) -> Dict:
        """
        Get user by either username or email
        
        Args:
            identifier: Either username or email
            
        Returns:
            Dict containing user information
        """
        try:
            # Try as username first
            return self.get_user(identifier)
        except Exception as e:
            if 'UserNotFoundException' in str(e):
                # If not found, try to find by email
                users = self.list_users(filter_key='email', filter_value=identifier)
                if users:
                    return users[0]
                raise ValueError(f"No user found with identifier: {identifier}")
            raise e

    def create_user(self, 
                username: Optional[str] = None,
                email: Optional[str] = None,
                temporary_password: Optional[str] = None,
                custom_attributes: Optional[Dict] = None,
                send_welcome_email: bool = True,
                organization_name: Optional[str] = "Kavia",
                set_password_url: Optional[str] = "http://kavia.local:3000/users/set_password?tenant_id=T0000&email={username}",
                login_url: Optional[str] = "http://kavia.local:3000/login?tenant_id=T0000&email={username}",
                user_fullname: str = None,
                use_ses: bool = True,
                ses_source_arn: Optional[str] = settings.SES_SOURCE_ARN,
                ses_from_email: Optional[str] = settings.SES_FROM_EMAIL,
                ses_reply_to: Optional[str] = settings.SES_REPLY_TO,
                send_login_credentials: bool = False
               ) -> Dict:
        """
        Create a new user with flexible parameters
        """
        try:
            if not user_fullname:
                if 'Name' in custom_attributes:
                    user_fullname = str(custom_attributes['Name']).split(' ')[0]
                elif email:
                    user_fullname = email.split('@')[0]
                else:
                    user_fullname = username

            if not username and not email:
                raise ValueError("Either username or email must be provided")
                
            final_username = username or email
            
            if not temporary_password:
                import secrets
                chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + \
                        'abcdefghijklmnopqrstuvwxyz' + \
                        '0123456789' + \
                        '!@#$%^&*'
                temporary_password = ''.join(secrets.choice(chars) for _ in range(12))
            
            attributes = []
            if email:
                attributes.extend([
                    {'Name': 'email', 'Value': email},
                    {'Name': 'email_verified', 'Value': 'true'}
                ])
                    
            if custom_attributes:
                attributes.extend([
                    {
                        'Name': f"custom:{key}" if not key.startswith('custom:') else key, 
                        'Value': str(value)
                    }
                    for key, value in custom_attributes.items()
                ])

            params = {
                'UserPoolId': self.user_pool_id,
                'Username': final_username,
                'UserAttributes': attributes,
                'TemporaryPassword': temporary_password,
                'DesiredDeliveryMediums': ['EMAIL'],
                'ForceAliasCreation': True,
                'MessageAction': 'SUPPRESS'  # Always suppress Cognito's email since we'll send our own
            }

            response = self.cognito.admin_create_user(**params)

            # Set permanent password so user doesn't need to reset upon first login
            self.cognito.admin_set_user_password(
                UserPoolId=self.user_pool_id,
                Username=final_username,
                Password=temporary_password,
                Permanent=True
            )
            
            # Send welcome email using SESHandler if requested
            if send_welcome_email and email:
                try:
                    self._send_welcome_email(
                        email=email,
                        username=final_username,
                        temporary_password=temporary_password,
                        user_fullname=user_fullname,
                        organization_name=organization_name,
                        set_password_url=set_password_url.format(username=final_username),
                        login_url=login_url.format(username=final_username),
                        ses_from_email=ses_from_email,
                        ses_reply_to=ses_reply_to,
                        send_login_credentials=send_login_credentials
                    )
                except Exception as email_error:
                    print(f"Warning: User created but email failed to send: {str(email_error)}")
            
            return response['User']

        except ClientError as e:
            error = e.response['Error']
            if error['Code'] == 'UsernameExistsException':
                raise ValueError(f"User exists: {final_username}")
            elif error['Code'] == 'InvalidParameterException':
                raise ValueError(f"Invalid parameters: {error['Message']}")
            elif error['Code'] == 'TooManyRequestsException':
                raise Exception("Rate limit exceeded")
            raise Exception(f"Failed to create user: {error['Message']}")

    def _send_welcome_email(self,
                           email: str,
                           username: str,
                           temporary_password: str,
                           user_fullname: str,
                           organization_name: str,
                           set_password_url: str,
                           login_url: str,
                           ses_from_email: Optional[str] = None,
                           ses_reply_to: Optional[str] = None,
                           send_login_credentials: bool = False) -> None:
        """
        Send welcome email using SESHandler and external templates
        
        Args:
            send_login_credentials: If True, uses welcome_email_with_login template,
                                   otherwise uses standard welcome_email template
        """
        # Prepare template variables
        template_variables = {
            'user_fullname': user_fullname,
            'username': username,
            'temporary_password': temporary_password,
            'organization_name': organization_name,
            'set_password_url': set_password_url,
            'login_url': login_url
        }
        
        try:
            # Validate template variables
            validate_welcome_email_variables(template_variables)
            
            # Load and format templates based on send_login_credentials flag
            if send_login_credentials:
                templates = load_welcome_email_with_login_templates(template_variables)
            else:
                templates = load_welcome_email_templates(template_variables)
            
            subject = get_welcome_email_subject()
            
            html_content = templates['html']
            text_content = templates['text']
            
        except Exception as template_error:
            print(f"Warning: Failed to load email templates: {str(template_error)}")
            # Fallback to simple text content if template loading fails
            subject = "Welcome to KAVIA AI"
            html_content = None
            
            if send_login_credentials:
                # Fallback for login credentials template
                text_content = f"""
Hello {user_fullname},

Your KAVIA AI account has been created successfully! Please use the links below to set up your password and access your account.

SET YOUR PASSWORD:
{set_password_url}

LOGIN TO YOUR ACCOUNT:
{login_url}

KAVIA is a new AI-powered software development platform, built for experienced developers and beginners.

If you have any questions or need help, just email us at:
<EMAIL>

Thank You!
KAVIA AI Team
"""
            else:
                # Fallback for standard welcome template
                text_content = f"""
Hello {user_fullname},

Welcome to {organization_name}! We're excited to have you on board.

Your Login Credentials:
Username: {username}
Password: {temporary_password}

Set Password URL: {set_password_url}
Login URL (after setting password): {login_url}

Note: Please keep this information secure and delete after use.

Best regards,
{organization_name} Team
"""

        # Send email using SESHandler
        reply_to_list = [ses_reply_to] if ses_reply_to else None
        
        ses_handler.send_email_api(
            to_addresses=[email],
            subject=subject,
            body_text=text_content,
            body_html=html_content,
            sender=ses_from_email,
            reply_to=reply_to_list
        )

    def update_user_attributes(self, 
                             identifier: str,
                             attributes: Dict,
                             is_custom: bool = True) -> Dict:
        """
        Update user attributes with simplified dictionary input
        
        Args:
            identifier: Username or email
            attributes: Dictionary of attributes to update
            is_custom: Whether these are custom attributes
            
        Returns:
            Updated user information
        """
        user = self.get_user_by_identifier(identifier)
        username = user['Username']
        
        formatted_attributes = []
        for key, value in attributes.items():
            if key =="picture" and value=="":
                formatted_attributes.append({
                    'Name': 'picture',
                    'Value': ''
                })
                continue
            if not value:
                continue
            if is_custom and not key.startswith('custom:'):
                key = f'custom:{key}'
            formatted_attributes.append({
                'Name': key,
                'Value': str(value)
            })
            
        try:
            self.cognito.admin_update_user_attributes(
                UserPoolId=self.user_pool_id,
                Username=username,
                UserAttributes=formatted_attributes
            )
            return self.get_user(username)
        except ClientError as e:
            raise Exception(f"Failed to update attributes: {str(e)}")

    def list_users(self, 
                  filter_key: Optional[str] = None,
                  filter_value: Optional[str] = None,
                  limit: int = 60) -> List[Dict]:
        """
        List users with optional filtering
        
        Args:
            filter_key: Optional attribute to filter on
            filter_value: Optional value to filter by
            limit: Maximum number of users to return
            
        Returns:
            List of user information dictionaries
        """
        try:
            params = {
                'UserPoolId': self.user_pool_id,
                'Limit': limit
            }
            
            if filter_key and filter_value:
                params['Filter'] = f"{filter_key} = \"{filter_value}\""
                
            response = self.cognito.list_users(**params)
            return response['Users']
        except ClientError as e:
            raise Exception(f"Failed to list users: {str(e)}")

    def get_user(self, username: str) -> Dict:
        """Get user details by username"""
        try:
            return self.cognito.admin_get_user(
                UserPoolId=self.user_pool_id,
                Username=username
            )
        except ClientError as e:
            raise Exception(f"Failed to get user: {str(e)}")

    def delete_user(self, identifier: str) -> None:
        """Delete user by username or email"""
        try:
            user = self.get_user_by_identifier(identifier)
            self.cognito.admin_delete_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
        except ClientError as e:
            raise Exception(f"Failed to delete user: {str(e)}")

    def enable_user(self, identifier: str) -> Dict:
        """
        Enable a user by username or email
        
        Args:
            identifier: Username or email of the user to enable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If enabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_enable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to enable user: {str(e)}")
    
    def disable_user(self, identifier: str) -> Dict:
        """
        Disable a user by username or email
        
        Args:
            identifier: Username or email of the user to disable
            
        Returns:
            Dict: Response from Cognito
            
        Raises:
            Exception: If disabling user fails
        """
        try:
            user = self.get_user_by_identifier(identifier)
            response = self.cognito.admin_disable_user(
                UserPoolId=self.user_pool_id,
                Username=user['Username']
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to disable user: {str(e)}")

    def get_user_attributes(self, identifier: str, custom_only: bool = False) -> Dict:
        """
        Get user attributes in a simplified dictionary format
        
        Args:
            identifier: Username or email
            custom_only: Whether to return only custom attributes
            
        Returns:
            Dictionary of user attributes
        """
        user = self.get_user_by_identifier(identifier)
        attributes = {}
        
        for attr in user.get('UserAttributes', []):
            name = attr['Name']
            if custom_only and not name.startswith('custom:'):
                continue
            attributes[name] = attr['Value']
            
        return attributes
    
    def configure_email_templates(self,
                                email_subject: str = "Welcome to Our Application!",
                                email_message: str = None,
                                use_ses: bool = True,
                                ses_source_arn: Optional[str] = None,
                                ses_from_email: Optional[str] = None,
                                ses_reply_to: Optional[str] = None) -> Dict:
        """
        Configure User Pool email templates for welcome messages
        
        Args:
            email_subject: Subject line for welcome emails
            email_message: Custom message template. Available variables:
                {username} - the username
                {####} - the temporary password
            use_ses: Whether to use Amazon SES for sending emails
            ses_source_arn: ARN of the SES identity to use (required if use_ses is True)
            ses_from_email: Email address to use as the sender (required if use_ses is True)
            ses_reply_to: Email address to use for replies (optional)
                
        Returns:
            Dict containing the API response
        """
        if email_message is None:
            email_message = """
Hello {username},

Welcome to our application! We're excited to have you on board.

Your temporary login credentials are:
Username: {username}
Temporary Password: {####}

Please log in and change your password at your earliest convenience.

Best regards,
Your App Team"""

        try:
            email_config = {}
            
            if use_ses:
                if not ses_source_arn or not ses_from_email:
                    raise ValueError("ses_source_arn and ses_from_email are required when use_ses is True")
                
                email_config = {
                    'EmailSendingAccount': 'DEVELOPER',
                    'SourceArn': ses_source_arn,
                    'From': ses_from_email
                }
                
                if ses_reply_to:
                    email_config['ReplyToEmailAddress'] = ses_reply_to
            else:
                email_config = {
                    'EmailSendingAccount': 'COGNITO_DEFAULT'
                }

            response = self.cognito.update_user_pool(
                UserPoolId=self.user_pool_id,
                EmailConfiguration=email_config,
                AdminCreateUserConfig={
                    'AllowAdminCreateUserOnly': True,
                    'InviteMessageTemplate': {
                        'EmailMessage': email_message,
                        'EmailSubject': email_subject
                    }
                }
            )
            return response
        except ClientError as e:
            raise Exception(f"Failed to configure email templates: {str(e)}")

    def check_if_attribute_exists(self, attribute_name):
        # Describe the user pool to get the schema
        response = self.cognito.describe_user_pool(UserPoolId=self.user_pool_id)
        
        # Get the list of schema attributes
        schema_attributes = response['UserPool']['SchemaAttributes']
        print("SCHEMA ATTRIBUTES: ", schema_attributes)
        
        # Check if the attribute exists in the schema
        for attribute in schema_attributes:
            if attribute['Name'] == attribute_name:
                return True  # Attribute exists
        return False  # Attribute does not exist

    def add_free_tier_to_pool(self):
        # Describe the user pool to get the current schema
        if self.check_if_attribute_exists('custom:free_user'):
            print('Free tier attribute already exist!')
            return

        response = self.cognito.add_custom_attributes(
            UserPoolId=self.user_pool_id,
            CustomAttributes=[
                {
                    'Name': 'free_user',
                    'AttributeDataType': 'Boolean',
                    'DeveloperOnlyAttribute': False,
                    'Mutable': True,
                    'Required': False
                }
            ]
        )
        print("Updated schema:", response)