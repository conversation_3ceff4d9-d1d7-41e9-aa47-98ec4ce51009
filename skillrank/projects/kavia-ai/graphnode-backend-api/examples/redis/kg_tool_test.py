import os
import re
from app.knowledge.redis_kg import getRedisKnowledge
from app.utils.kg_inspect.knowledge_embeddings import KnowledgeEmbeddings
from rapidfuzz import fuzz

# Helper methods for finding relevant files
def _is_unit_match( term, text):
    """Create a pattern with word boundaries around term and search for it in text"""
    pattern = r'\b' + re.escape(term) + r'\b'
    return bool(re.search(pattern, text, re.IGNORECASE))

def _check_against_file_search_keys( file_info, search_term):
    """Check if search term matches any of the file's search terms"""
    match = False
    terms = file_info.get('search-terms', [])
    for term in terms:
        similarity = fuzz.ratio(search_term, term)
        if similarity > 80:
            match = True
            break
    return match

def _get_base_path( path):
    """Get the base path for a given file path"""
    # Since we may not have access to code_bases, we'll extract from the path
    parts = path.split('/')
    # This is a simplified version - in reality we'd need to know the actual base path
    base_path = '/'.join(parts[:3])  # Assuming first 3 parts form the base path
    return base_path

def _check_against_path( path, search_term):
    """Check if search term matches any part of the file path"""
    match = False
    base_path = _get_base_path(path)
    # Using replace instead of removeprefix for compatibility
    path_of_interest = path.replace(base_path + '/', '', 1)
    path_elements = path_of_interest.split('/')
    for path_element in reversed(path_elements):
        similarity = fuzz.ratio(search_term, path_element)
        if similarity > 70:
            match = True
            break
    return match

def _check_against_file_description( file_info, search_term):
    """Check if search term matches the file description"""
    match = False
    description = file_info.get('description', '')
    if _is_unit_match(search_term, description):
        match = True
    return match

def _check_against_method_descriptions( file_info, search_term):
    """Check if search term matches any method description"""
    match = False
    methods = file_info.get('methods', [])
    for method in methods:
        if 'description' in method:
            if _is_unit_match(search_term, method['description']):
                match = True
                break
    return match

def _check_against_ctags( file_info, search_term):
    """Check if search term matches any ctag pattern"""
    match = False
    ctags = file_info.get('ctags', [])
    for tag in ctags:
        if _is_unit_match(search_term, tag.get('pattern', '')):
            match = True
            break
    return match

def _check_against_externals( file_info, search_term):
    """Check if search term matches any external file reference"""
    match = False
    external_files = file_info.get('external_files', [])
    for external_file in external_files:
        if _is_unit_match(search_term, external_file):
            match = True
            break
    return match

def _check_against_class_descriptions( file_info, search_term):
    """Check if search term matches any class description"""
    match = False
    classes = file_info.get('classes', [])
    for klass in classes:
        if 'description' in klass:
            if _is_unit_match(search_term, klass['description']):
                match = True
                break
    return match
    
def find_relevant_files( search_terms, and_search, code_base=None):
        build_ids = ['b7060dm']
        knowledge = getRedisKnowledge(id=build_ids,verbose=True)
        
        repo_paths = {}
    
        if knowledge and 'source_code_bases' in knowledge and 'source_file_info' in knowledge:
            for code_base in knowledge['source_code_bases']:
                # Extract org/repo from URL
                url_parts = code_base.split('/')
                if len(url_parts) >= 5:
                    org = url_parts[3]
                    repo_with_branch = url_parts[4]
                    
                    # Handle branch part and .git extension
                    if ':' in repo_with_branch:
                        repo_part, branch = repo_with_branch.split(':')
                    else:
                        repo_part = repo_with_branch
                        branch = 'master'
                    
                    if repo_part.endswith('.git'):
                        repo_part = repo_part[:-4]
                    
                    # Dynamically find the base path by examining file paths
                    org_repo_pattern = f"{org}/{repo_part}"
                    
                    # Find first file matching this repository
                    for file_path, file_info in knowledge['source_file_info'].items():
                        if 'code-base-name' in file_info and file_info['code-base-name'] == code_base:
                            # Extract the base path by finding the org/repo pattern in the path
                            path_parts = file_path.split('/')
                            base_path = ""
                            
                            # Find where org/repo pattern starts in the path
                            for i in range(len(path_parts) - 1):
                                if path_parts[i] == org and path_parts[i+1] == repo_part:
                                    # Found org/repo pattern, construct base path up to repo
                                    base_path = '/'.join(path_parts[:i+2])
                                    break
                            
                            # If we couldn't find org/repo exactly, look for partial matches
                            if not base_path:
                                for i in range(len(path_parts)):
                                    if org in path_parts[i] or repo_part in path_parts[i]:
                                        # Go up until we find a common ancestor path
                                        # that contains both org and repo in some form
                                        ancestor_path = '/'.join(path_parts[:i+1])
                                        if org in ancestor_path and repo_part in ancestor_path:
                                            base_path = ancestor_path
                                            break
                            
                            if base_path:
                                print(f"Found base path for {code_base}: {base_path}")
                                repo_paths[code_base] = base_path
                                break
                    
                    # If we couldn't find the base path, use a default path format
                    if code_base not in repo_paths:
                        # Try to construct from file paths directly
                        for file_path in knowledge['source_file_info']:
                            # Extract common prefix that would represent the repo root
                            path_segments = file_path.split('/')
                            # Look for rdkcentral/rialto-gstreamer pattern
                            for i in range(len(path_segments) - 1):
                                if path_segments[i] == org or path_segments[i+1] == repo_part:
                                    base_path = '/'.join(path_segments[:i+2])
                                    repo_paths[code_base] = base_path
                                    print(f"Constructed base path for {code_base}: {base_path}")
                                    break
                            if code_base in repo_paths:
                                break
                        
                        # If still not found, use the first file's directory as a fallback
                        if code_base not in repo_paths and knowledge['source_file_info']:
                            first_file = next(iter(knowledge['source_file_info']))
                            last_dir_index = first_file.rfind('/')
                            if last_dir_index > 0:
                                base_path = first_file[:last_dir_index]
                                repo_paths[code_base] = base_path
                                print(f"Fallback base path for {code_base}: {base_path}")
            
            print("DEBUG EMBEDDING IN KG TOOL", repo_paths)
            
        # Initialize embeddings with repository paths
        embeddings = KnowledgeEmbeddings.get_instance(
            'test-123',
            repo_paths
        )
            
        """
        Find relevant files matching the search terms with support for both keyword and semantic search.
        
        Args:
            search_terms: List of search terms to find
            and_search: Boolean indicating whether all terms must match (True) or any term (False)
            code_base: Optional name of codebase to limit search to
            
        Returns:
            Dictionary with search results
        """
        relevant_files = []
        matches_high = []
        matches_medium = []
        matches_low = []
        matches_by_weight = {"high": matches_high, "medium": matches_medium, "low": matches_low}
        matches_per_term = {}
        results_abridged = False
        
        # Initialize match counter for each term
        for search_term in search_terms:
            matches_per_term[search_term] = 0
        
        # Get semantic search results
        semantic_matches = {}
        try:
            similar_files = embeddings.search_similar_files(code_base, search_terms)
            for result in similar_files:
                semantic_matches[result['file_path']] = True
        except Exception as e:
            print(f"Error in semantic search: {str(e)}")
        
        # Iterate through all source files
        for file in knowledge.get('source_files', []):
            file_info = knowledge.get('source_file_info', {}).get(file, {})
            
            # Skip if we're limiting to a specific code base and this doesn't match
            if code_base and file_info.get('code-base-name') != code_base:
                continue
            
            # Determine initial weight based on semantic search results
            weight = "low"
            if file in semantic_matches:
                weight = "medium"  # Boost weight for semantic matches
            
            # Get keyword search results
            matchInFile = False
            
            for search_term in search_terms:
                match = False
                for method in ["description", "terms", "method", "classes", "ctags", "external", "path"]:
                    if method == "description" and 'description' in file_info:
                        match |= _check_against_file_description(file_info, search_term)
                        weight = "high"
                    elif method == "terms" and 'search-terms' in file_info and file_info['search-terms']:
                        match |= _check_against_file_search_keys(file_info, search_term)
                        weight = "medium"
                    elif method == "method" and 'methods' in file_info:
                        match |= _check_against_method_descriptions(file_info, search_term)
                        weight = "medium"
                    elif method == "classes" and 'classes' in file_info:
                        match |= _check_against_class_descriptions(file_info, search_term)
                        weight = "medium"
                    elif method == "ctags" and 'ctags' in file_info:
                        match |= _check_against_ctags(file_info, search_term)
                        weight = "medium"
                    elif method == "external" and 'external_files' in file_info:
                        match |= _check_against_externals(file_info, search_term)
                        weight = "medium"
                    elif method == "path":
                        match |= _check_against_path(file, search_term)
                        weight = "low"

                    if match:
                        matches_per_term[search_term] += 1
                        break

                matchInFile |= match

                if and_search and not match:
                    matchInFile = False
                    break
                if not and_search and match:
                    break
            
            # Add semantic-only matches that didn't match keywords
            if (not matchInFile or weight == "low") and file in semantic_matches:
                matchInFile = True
                weight = "medium"
            
            # If there's a match, add to the appropriate result bin
            if matchInFile:
                if matches_per_term.get(search_term, 0) < 60:
                    result = {
                        "name": file,
                        "description": file_info.get('description', 'No description available'),
                    }
                    if 'code-base-name' in file_info:
                        result['code-base'] = file_info.get('code-base-name', '')
                    if file in semantic_matches:
                        result['semantic_match'] = True
                        
                    matches_by_weight[weight].append(result)
                else:
                    results_abridged = True
        
        # Combine results with priority to higher weights
        for weight in ["high", "medium", "low"]:
            match_bin = matches_by_weight[weight]
            for match in match_bin:
                if len(relevant_files) < 120:
                    relevant_files.append(match)
        
        # Add abridged notice if needed
        if results_abridged:
            relevant_files.append({
                "name": "...",
                "description": "Search results have been abridged due to too many matches"
            })
        
        return {
            "status": "SUCCESS" if relevant_files else "ERROR",
            "value": relevant_files
        }

print(find_relevant_files('processcoveragestats.py',True))