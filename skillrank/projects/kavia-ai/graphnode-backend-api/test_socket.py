from app.connection.task_queue import TaskQueue
import asyncio
import time
import json
import uuid


code_task_id = "cg3c3cec55"
BATCH_LINE_THRESHOLD = 10  # Number of lines to send in each batch

files_to_edit = [
    # {"path": "testing/Calculator.jsx", "name": "Calculator.jsx",
    #  "search": "const [display, setDisplay] = useState('0');", 
    #  "replace": "const [display, setDisplay] = useState('0');\n  const [history, setHistory] = useState([]);"
    #  },
    # {"path": "testing/calc.js", "name": "calc.js", 
    #  "search": "constructor() {\n    this.result = 0;\n    this.currentValue = '0';\n    this.previousValue = null;\n    this.operation = null;\n    this.resetInput = true;", 
    #  "replace": "constructor() {\n    this.result = 0;\n    this.currentValue = '0';\n    this.previousValue = null;\n    this.operation = null;\n    this.resetInput = true;\n    this.history = [];"},
    {"path": "testing/calc.py", 
     "name": "calc.py", 
     "search": "def __init__(self, root):", "replace": "def __init__(self, root):\n        # Initialize calculator\n        self.root = root\n        self.root.title(\"Python Calculator\")\n        self.root.geometry(\"300x400\")\n        self.root.resizable(False, False)\n        \n        # Set theme colors\n        self.root.configure(bg=\"#2d2d2d\")\n        \n        # Display\n        self.display_var = tk.StringVar(value=\"0\")\n        self.display = ttk.Entry(\n            self.root, \n            textvariable=self.display_var, \n            font=(\"Arial\", 24), \n            justify=\"right\"\n        )\n        self.display.grid(row=0, column=0, columnspan=4, sticky=\"nsew\", padx=5, pady=5)\n        \n        # Create calculator state\n        self.previous_value = None\n        self.operation = None\n        self.waiting_for_operand = False\n        self.history = []  # Add history list to track calculations\n        \n        # Create history display\n        self.history_frame = ttk.Frame(self.root)\n        self.history_frame.grid(row=6, column=0, columnspan=4, sticky=\"nsew\", padx=5, pady=5)\n        \n        self.history_label = ttk.Label(self.history_frame, text=\"History\", font=(\"Arial\", 12))\n        self.history_label.pack(anchor=\"w\")\n        \n        self.history_listbox = tk.Listbox(self.history_frame, height=3, font=(\"Arial\", 10))\n        self.history_listbox.pack(fill=\"both\", expand=True)\n        \n        # Add clear history button\n        self.clear_history_button = ttk.Button(\n            self.history_frame,\n            text=\"Clear History\",\n            command=self.clear_history\n        )\n        self.clear_history_button.pack(pady=5)\n        \n        # Create buttons\n        self.create_buttons()"},
    {"path": "testing/Calculator.html",
      "name": "Calculator.html", 
      "search": "// Initialize display\n        updateDisplay();", "replace": "// Initialize display\n        updateDisplay();\n        \n        // Add history tracking\n        let calculationHistory = [];\n        \n        function addToHistory(calculation) {\n            calculationHistory.push(calculation);\n            // Keep only the last 10 calculations\n            if (calculationHistory.length > 10) {\n                calculationHistory.shift();\n            }\n            console.log('Calculation history:', calculationHistory);\n        }\n        \n        function calculate() {\n            if (!operator) return;\n            \n            const secondOperand = parseFloat(displayValue);\n            const result = performCalculation();\n            \n            // Add to history\n            addToHistory(`${firstOperand} ${operator} ${secondOperand} = ${result}`);\n            \n            displayValue = String(result);\n            \n            // Reset for new calculation\n            firstOperand = result;\n            waitingForSecondOperand = false;\n            operator = null;\n            \n            updateDisplay();\n        }"},
]

files_to_send = [
    # {"path": "testing/Calculator.jsx", "name": "Calculator.jsx", "is_new": True},
    # {"path": "testing/calc.js", "name": "calc.js", "is_new": True},
    {"path": "testing/calc.py", "name": "calc.py", "is_new": True},
    {"path": "testing/Calculator.html", "name": "Calculator.html", "is_new": True},
]

async def message_handler(message):
    """Handle incoming messages - now used for displaying queued data"""
    print(f"Processed message: {json.dumps(message, indent=2)}")
    
    if 'data' in message and 'file_content' in message['data']:
        content = message['data'].get('file_content', '')
        lines_received = len(content.splitlines())
        print(f"Content length: {lines_received} lines, {len(content)} characters")
        
        # Extract metadata for tracking
        is_end = message['data'].get('is_end', False)
        line_number = message['data'].get('line_number', None)
        total_lines = message['data'].get('total_lines', None)
        batch_number = message['data'].get('batch_number', None)
        total_batches = message['data'].get('total_batches', None)
        
        if is_end:
            print(f"PROCESSED FINAL CONTENT CHUNK. Status summary:")
            if line_number and total_lines:
                print(f"Line-by-line mode: Processed line {line_number}/{total_lines}")
                if line_number < total_lines:
                    print(f"WARNING: Missing {total_lines - line_number} lines!")
            elif batch_number and total_batches:
                print(f"Batch mode: Processed batch {batch_number}/{total_batches}")
                if batch_number < total_batches:
                    print(f"WARNING: Missing {total_batches - batch_number} batches!")

def split_content(content):
    """Split content into individual lines"""
    return content.splitlines()

async def send_file_data(queue, file_path, file_name, message_id, operation="write", search=None, replace=None):
    """Send file data line by line using TaskQueue"""
    if operation == "edit":
        message_data = {
            "type": "file_update",
            "message_id": message_id,
            "file_path": file_path,
            "file_name": file_name,
            "operation": operation,
            "search": search,
            "replace": replace
        }
        queue.enqueue(code_task_id, message_data)
        print(f"Queued edit request for {file_name}")
        return
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return
    total_lines = len(lines)
    print(f"\nQueuing {total_lines} lines for {file_name}")
    
    # Send lines progressively
    for i, line in enumerate(lines):
        is_last_line = (i == total_lines - 1)
        message_data = {
            "type": "file_update",
            "message_id": message_id,
            "file_path": file_path,
            "file_name": file_name,
            "file_content": line.rstrip('\n')+'\n',  # Remove trailing newline
            "stream": True,
            "is_end": is_last_line,  # Set to True for the last line
            "custom_chunk": False,  # Add indicator for non-custom chunking
            "operation": operation,
            "line_number": i+1,  # Add line number for better tracking
            "total_lines": total_lines  # Add total lines for verification
        }
            
        queue.enqueue(code_task_id, message_data)
        print(f"Queued line {i+1}/{total_lines} for {file_name}")
        
        # Add 0.2 second delay as in original
        await asyncio.sleep(0.2)

async def send_file_data_batch(queue, file_path, file_name, message_id, operation="write"):
    """Send file data in batches using TaskQueue"""
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return
    
    total_lines = len(lines)
    print(f"\nQueuing {total_lines} lines for {file_name} in batches of {BATCH_LINE_THRESHOLD}")
    
    # Process lines in batches
    batch_count = (total_lines + BATCH_LINE_THRESHOLD - 1) // BATCH_LINE_THRESHOLD  # Ceiling division
    print(f"Total batches to queue: {batch_count}")
    
    for i in range(0, total_lines, BATCH_LINE_THRESHOLD):
        batch_lines = lines[i:i + BATCH_LINE_THRESHOLD]
        is_last_batch = (i + BATCH_LINE_THRESHOLD >= total_lines)
        batch_number = i // BATCH_LINE_THRESHOLD + 1
        
        # Join the batch lines
        batch_content = ''.join(batch_lines)
        
        message_data = {
            "type": "file_update",
            "message_id": message_id,
            "file_path": file_path,
            "file_name": file_name,
            "file_content": batch_content,
            "stream": True,
            "is_end": is_last_batch,
            "custom_chunk": True,  # Indicate this is a custom chunked message
            "operation": operation,
            "batch_size": len(batch_lines),
            "batch_number": batch_number,
            "total_batches": batch_count,
            "line_start": i + 1,
            "line_end": min(i + BATCH_LINE_THRESHOLD, total_lines)
        }
        
        queue.enqueue(code_task_id, message_data)
        print(f"Queued batch {batch_number}/{batch_count} (lines {i+1}-{min(i+BATCH_LINE_THRESHOLD, total_lines)}) for {file_name}")
        
        # Verify batch content
        print(f"Batch size: {len(batch_lines)} lines, Content length: {len(batch_content)} characters")
        
        # Add 1 second delay as in original
        await asyncio.sleep(1)

async def send_complete_file(queue, file_path, file_name, message_id, operation="write"):
    """Send the entire file content in a single message using TaskQueue"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return
    
    lines = content.splitlines()
    total_lines = len(lines)
    print(f"\nQueuing entire file ({total_lines} lines, {len(content)} characters) for {file_name}")
    
    message_data = {
        "type": "file_update",
        "message_id": message_id,
        "file_path": file_path,
        "file_name": file_name,
        "file_content": content,
        "stream": False,  # Not streaming since we're sending it all at once
        "is_end": True,
        "custom_chunk": False,
        "operation": operation,
        "total_lines": total_lines
    }
    
    queue.enqueue(code_task_id, message_data)
    print(f"Queued complete file {file_name} in one message")
    return

async def handle_deployment(queue):
    """Handle deployment process using TaskQueue"""
    deployment_id = str(uuid.uuid4())  # Generate a unique ID for deployment
    deployment_data = {
        "type": "start_deploy",
        "task_id": code_task_id,
        "data": {
            "id": deployment_id,  # Add unique ID to deployment data
            "deployment_type": "react_app",
            "command": "npm install && npm run build",
            "root_path": "/home/<USER>/workspace/code-maintenance-job-58b07cbc/hello-react/vite-project"
        }
    }
    queue.enqueue(code_task_id, deployment_data)
    print(f"Deployment request queued with ID: {deployment_id}")

async def handle_stream_edit(queue):
    """Handle stream editing process using TaskQueue and predefined files_to_edit list"""
    for file_data in files_to_edit:
        message_id = str(uuid.uuid4())
        print(f"\nProcessing file: {file_data['name']}")
        await send_file_data(
            queue, 
            file_data['path'], 
            file_data['name'], 
            message_id, 
            operation="edit", 
            search=file_data['search'], 
            replace=file_data['replace']
        )
        # Add 1 second delay between files
        await asyncio.sleep(1)
    print("\nFinished queuing all edit requests")

async def handle_batch_file_watch(queue):
    """Handle batch file watching process using TaskQueue"""
    for file_data in files_to_send:
        message_id = str(uuid.uuid4())
        print(f"\nProcessing file: {file_data['name']}")
        await send_file_data_batch(queue, file_data['path'], file_data['name'], message_id)
        # Add 1 second delay between files
        await asyncio.sleep(1)
    
    print("\nFinished queuing all files in batch mode")

def show_queue_status(queue):
    """Display current queue status"""
    queue_length = queue.get_queue_length(code_task_id)
    print(f"\n📋 Queue Status for task_id: {code_task_id}")
    print(f"   Items in queue: {queue_length}")
    
    if queue_length > 0:
        # Peek at next few items
        peek_items = queue.peek(code_task_id, min(3, queue_length))
        print(f"   Next {len(peek_items)} items:")
        for i, item in enumerate(peek_items, 1):
            item_type = item.get('data', {}).get('type', 'unknown')
            print(f"     {i}. Type: {item_type} (ID: {item['id']})")

async def process_queue_items(queue, count=None):
    """Process items from the queue"""
    processed = 0
    while True:
        item = queue.dequeue(code_task_id)
        if not item:
            break
            
        print(f"\n🔄 Processing item {item['id']}:")
        await message_handler(item)
        processed += 1
        
        if count and processed >= count:
            break
            
        await asyncio.sleep(0.1)  # Small delay between processing
    
    if processed == 0:
        print("No items to process")
    else:
        print(f"\n✅ Processed {processed} items from queue")

async def main():
    # Create TaskQueue instance
    queue = TaskQueue(queue_path="/home/<USER>/workspace/.queue")
    
    print(f"🚀 Task Queue initialized for task_id: {code_task_id}")
    
    # Show initial queue status
    show_queue_status(queue)
    
    # Ask for option
    print("\nPlease select an option:")
    print("1. Deployment")
    print("2. File Watch (Line-by-Line)")
    print("3. Stream Edit")
    print("4. Batch File Watch")
    print("5. Complete File Send (No Streaming)")
    print("6. Show Queue Status")
    print("7. Process Queue Items")
    print("8. Clear Queue")
    
    option = input("Enter your choice (1-8): ")
    # Add 1 second delay
    await asyncio.sleep(1)
    
    if option == "1":
        await handle_deployment(queue)
    elif option == "2":
        # Send files from files_to_send list
        for file_data in files_to_send:
            message_id = str(uuid.uuid4())
            print(f"\nProcessing file: {file_data['name']}")
            await send_file_data(queue, file_data['path'], file_data['name'], message_id)
            # Add 1 second delay between files
            await asyncio.sleep(1)
        
        print("\nFinished queuing all files")
    elif option == "3":
        await handle_stream_edit(queue)
    elif option == "4":
        await handle_batch_file_watch(queue)
    elif option == "5":
        # Send complete files in one message
        for file_data in files_to_send:
            message_id = str(uuid.uuid4())
            print(f"\nProcessing file: {file_data['name']}")
            await send_complete_file(queue, file_data['path'], file_data['name'], message_id)
            # Add 1 second delay between files
            await asyncio.sleep(1)
        
        print("\nFinished queuing all files in complete mode")
    elif option == "6":
        show_queue_status(queue)
    elif option == "7":
        count_input = input("Enter number of items to process (or press Enter for all): ")
        count = int(count_input) if count_input.strip() else None
        await process_queue_items(queue, count)
    elif option == "8":
        cleared = queue.clear_task(code_task_id)
        if cleared:
            print(f"✅ Cleared queue for task_id: {code_task_id}")
        else:
            print(f"ℹ️ No queue found for task_id: {code_task_id}")
    else:
        print("Invalid option selected")
        return

    # Show final queue status
    show_queue_status(queue)
    print("\nQueue operations completed!")

if __name__ == "__main__":
    asyncio.run(main())


'''
{
  "type": "file_update",
  "task_id": code_task_id,
  "data": {
    "file_path": "file_path",
    "file_name": "file_name",
    "file_content": "file_content",
    "message_id": "id",
    "stream": true,
    "is_end": false
  }
}

{
    "type": "deploy",
    "task_id": code_task_id,
    "data": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "levels": ["build", "deploy"],
        "deployment_type": "frontend",
        "command": "npm run build",
        "root_path": "/home/<USER>/workspace/vite-project"
    }
}

{
    "type": "deploy_status",
    "task_id": code_task_id,
    "data": {
        "command": "frontend",
        "status": "building",
        "content": "npm"
    }
}
'''