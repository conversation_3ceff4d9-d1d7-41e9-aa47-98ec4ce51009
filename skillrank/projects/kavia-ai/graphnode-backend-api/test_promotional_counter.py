#!/usr/bin/env python3
"""
Test script for the Promotional Counter Service
This script tests the promotional counter functionality without requiring a full server setup.
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Mock the settings and database connections for testing
class MockSettings:
    KAVIA_B2C_CLIENT_ID = "test_b2c_client"

class MockCollection:
    def __init__(self):
        self.data = {}
        self.collection = "promotional_counters"
    
    @property
    def db(self):
        return {self.collection: self}
    
    def find_one(self, query):
        """Mock find_one operation"""
        if "_id" in query:
            return self.data.get(query["_id"])
        return None
    
    def insert_one(self, document):
        """Mock insert_one operation"""
        self.data[document["_id"]] = document
        return type('Result', (), {'inserted_id': document["_id"]})()
    
    def find_one_and_update(self, filter_query, update_query, return_document=None):
        """Mock find_one_and_update operation"""
        doc_id = filter_query["_id"]
        if doc_id not in self.data:
            return None
        
        doc = self.data[doc_id]
        
        # Check filter conditions
        if "count" in filter_query and "$lt" in filter_query["count"]:
            if doc["count"] >= filter_query["count"]["$lt"]:
                return None
        
        if "active" in filter_query and doc.get("active") != filter_query["active"]:
            return None
        
        # Apply updates
        if "$inc" in update_query:
            for field, value in update_query["$inc"].items():
                doc[field] = doc.get(field, 0) + value
        
        if "$set" in update_query:
            for field, value in update_query["$set"].items():
                doc[field] = value
        
        return doc

def mock_get_mongo_db(db_name, collection_name):
    """Mock database connection"""
    return MockCollection()

# Mock the imports
sys.modules['app.core.Settings'] = type('Module', (), {'settings': MockSettings()})()
sys.modules['app.connection.establish_db_connection'] = type('Module', (), {'get_mongo_db': mock_get_mongo_db})()
sys.modules['app.connection.tenant_middleware'] = type('Module', (), {'KAVIA_ROOT_DB_NAME': 'test_db'})()

# Now import the PromotionalCounterService
from app.routes.authentication_route import PromotionalCounterService

async def test_promotional_counter():
    """Test the promotional counter functionality"""
    print("🧪 Testing Promotional Counter Service")
    print("=" * 50)
    
    # Create service instance
    service = PromotionalCounterService()
    
    # Test 1: Initialize counter
    print("\n1. Testing counter initialization...")
    await service.initialize_counter()
    status = await service.get_current_count()
    print(f"   ✅ Counter initialized: {status}")
    assert status["count"] == 0
    assert status["limit"] == 1000
    assert status["remaining"] == 1000
    
    # Test 2: Check and increment counter (should succeed)
    print("\n2. Testing counter increment...")
    result = await service.check_and_increment_counter()
    print(f"   ✅ First increment result: {result}")
    assert result == True
    
    status = await service.get_current_count()
    print(f"   ✅ Counter after increment: {status}")
    assert status["count"] == 1
    assert status["remaining"] == 999
    
    # Test 3: Multiple increments
    print("\n3. Testing multiple increments...")
    for i in range(2, 6):  # Increment 4 more times
        result = await service.check_and_increment_counter()
        assert result == True
        print(f"   ✅ Increment {i}: Success")
    
    status = await service.get_current_count()
    print(f"   ✅ Counter after 5 increments: {status}")
    assert status["count"] == 5
    assert status["remaining"] == 995
    
    # Test 4: Simulate reaching limit
    print("\n4. Testing limit enforcement...")
    # Manually set counter to 999 to test limit
    service.counters_collection.data[service.FIRST_1000_PROMOTION_ID]["count"] = 999
    
    # This should succeed (999 -> 1000)
    result = await service.check_and_increment_counter()
    print(f"   ✅ Increment at 999: {result}")
    assert result == True
    
    # This should fail (already at 1000)
    result = await service.check_and_increment_counter()
    print(f"   ✅ Increment at 1000: {result}")
    assert result == False
    
    status = await service.get_current_count()
    print(f"   ✅ Final counter status: {status}")
    assert status["count"] == 1000
    assert status["remaining"] == 0
    
    print("\n🎉 All tests passed!")
    print("=" * 50)
    
    # Test 5: Verify promotional constants
    print("\n5. Testing promotional constants...")
    print(f"   ✅ Promotional Price ID: {service.PROMOTIONAL_PRICE_ID}")
    print(f"   ✅ Promotional Credits: {service.PROMOTIONAL_CREDITS:,}")
    print(f"   ✅ Promotion Limit: {service.FIRST_1000_LIMIT:,}")
    
    assert service.PROMOTIONAL_PRICE_ID == "price_1RqqagCI2zbViAE2XU0Hx3i6"
    assert service.PROMOTIONAL_CREDITS == 220000
    assert service.FIRST_1000_LIMIT == 1000
    
    print("\n✨ Promotional Counter Service is working correctly!")

if __name__ == "__main__":
    asyncio.run(test_promotional_counter())
