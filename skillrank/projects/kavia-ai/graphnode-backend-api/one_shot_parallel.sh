#!/bin/bash

# Configuration
NAMESPACE="duploservices-k-dev01"
SERVICE_LABEL="service=codegen"

# Get all pods at once, excluding zoltan-dev pods
all_pods=$(kubectl get pods -l "$SERVICE_LABEL" -n "$NAMESPACE" --no-headers | awk '{print $1}' | grep -v "^zoltan-dev-")

if [[ -z "$all_pods" ]]; then
    echo "No pods found with label: $SERVICE_LABEL (excluding zoltan-dev pods)"
    exit 0
fi

echo "Found pods (excluding zoltan-dev): $all_pods"

# Extract all deployment names
deployment_names=()
while IFS= read -r pod_name; do
    [[ -z "$pod_name" ]] && continue
    deployment_name=$(echo "$pod_name" | cut -d "-" -f1,2)
    deployment_names+=("$deployment_name")
done <<< "$all_pods"

# Remove duplicates and convert to space-separated string
unique_deployments=$(printf '%s\n' "${deployment_names[@]}" | sort -u | tr '\n' ' ')
echo "Unique deployments to delete: $unique_deployments"

# Build all resource names in parallel arrays
deployments=()
services_internal=()
services_clusterip=()
pvcs=()
configmaps=()

for deployment in $unique_deployments; do
    deployments+=("$deployment")
    services_internal+=("internal-$deployment")
    services_clusterip+=("internal-clusterip-$deployment")
    pvcs+=("pvc-$deployment")
    configmaps+=("pod-status-$deployment")
done

echo "Starting parallel deletion of all resources..."

# Delete all resources of each type in parallel using single kubectl commands
{
    echo "Deleting deployments..."
    if [ ${#deployments[@]} -gt 0 ]; then
        kubectl delete deployment "${deployments[@]}" -n "$NAMESPACE" --ignore-not-found &
    fi
} &

{
    echo "Deleting internal services..."
    if [ ${#services_internal[@]} -gt 0 ]; then
        kubectl delete service "${services_internal[@]}" -n "$NAMESPACE" --ignore-not-found &
    fi
} &

{
    echo "Deleting clusterip services..."
    if [ ${#services_clusterip[@]} -gt 0 ]; then
        kubectl delete service "${services_clusterip[@]}" -n "$NAMESPACE" --ignore-not-found &
    fi
} &

{
    echo "Deleting PVCs..."
    if [ ${#pvcs[@]} -gt 0 ]; then
        kubectl delete pvc "${pvcs[@]}" -n "$NAMESPACE" --ignore-not-found &
    fi
} &

{
    echo "Deleting ConfigMaps..."
    if [ ${#configmaps[@]} -gt 0 ]; then
        kubectl delete configmap "${configmaps[@]}" -n "$NAMESPACE" --ignore-not-found &
    fi
} &

# Wait for all parallel operations to complete
wait

echo "All resources deleted successfully!"
echo "Processed ${#deployments[@]} deployments with their associated resources."
