// utils/kaviaWebsiteData.js
export const getKaviaWebsiteDataFromHash = () => {
  try {
    // Get hash from current URL
    const hash = window.location.hash;
    
    if (!hash || !hash.includes('#data=')) {
      return {
        success: true,
        data: null,
        hasData: false,
        message: 'No KAVIA website data found in URL hash'
      };
    }
    
    // Extract the data parameter from hash
    const hashParams = new URLSearchParams(hash.substring(1)); // Remove # and parse
    const encodedData = hashParams.get('data');
    
    if (!encodedData) {
      return {
        success: true,
        data: null,
        hasData: false,
        message: 'No data parameter found in hash'
      };
    }
    
    // Decode base64 and parse JSON
    const jsonData = atob(encodedData);
    const userData = JSON.parse(jsonData);
    
    // Validate data structure
    if (userData.prompt !== undefined && userData.stack && userData.platform) {
      return {
        success: true,
        data: userData,
        hasData: true
      };
    } else {
      return {
        success: false,
        data: null,
        hasData: false,
        error: 'Invalid data structure'
      };
    }
    
  } catch (error) {
    console.error('Error retrieving KAVIA website data from hash:', error);
    return {
      success: false,
      data: null,
      hasData: false,
      error: error.message
    };
  }
};

export const clearKaviaWebsiteDataFromHash = () => {
  try {
    // Remove hash from URL
    if (window.location.hash) {
      window.history.replaceState(null, null, window.location.pathname + window.location.search);
    }
    return { success: true };
  } catch (error) {
    console.error('Error clearing KAVIA website data from hash:', error);
    return { success: false, error: error.message };
  }
};

// Legacy sessionStorage support (fallback)
export const getKaviaWebsiteData = () => {
  // First try hash method
  const hashResult = getKaviaWebsiteDataFromHash();
  if (hashResult.hasData) {
    return hashResult;
  }
  
  // Fallback to sessionStorage (for same-domain scenarios)
  try {
    const storedData = sessionStorage.getItem('kaviaWebsiteData');
    if (storedData) {
      const userData = JSON.parse(storedData);
      
      if (userData.prompt !== undefined && userData.stack && userData.platform) {
        return {
          success: true,
          data: userData,
          hasData: true
        };
      }
    }
    
    return {
      success: true,
      data: null,
      hasData: false,
      message: 'No KAVIA website data found'
    };
  } catch (error) {
    console.error('Error retrieving KAVIA website data:', error);
    return {
      success: false,
      data: null,
      hasData: false,
      error: error.message
    };
  }
};
