/**
 * Format merge time to readable format
 */
const formatMergeTime = (mergeTime) => {
  if (!mergeTime) return null;
  
  const date = new Date(mergeTime);
  const formattedDate = date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  const formattedTime = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
  
  return `${formattedDate} • ${formattedTime}`;
};

/**
 * Transform merge status data with formatted time display
 */
const transformMergeStatus = (mergeStatusArray) => {
  if (!Array.isArray(mergeStatusArray)) return [];
  
  return mergeStatusArray.map(mergeItem => {
    const formattedTime = formatMergeTime(mergeItem.merge_time);
    
    return {
      ...mergeItem,
      formatted_merge_time: formattedTime || 'Time not available',
      // Keep original merge_time for programmatic use
      merge_time: mergeItem.merge_time,
      // Add status styling helper
      status_color: mergeItem.merge_status === 'success' ? 'green' :
                   mergeItem.merge_status === 'failed' ? 'red' :
                   mergeItem.merge_status === 'pending' ? 'yellow' : 'gray'
    };
  });
};

/**
 * Transform API response to format expected by Sessions component
 * Moves container_id from task level to session level
 */
export const transformSessionsResponse = (apiResponse) => {
  if (!apiResponse || !apiResponse.tasks) {
    return {
      sessions: [],
      pagination: {
        total_count: 0,
        limit: 10,
        skip: 0
      }
    };
  }

  const transformedSessions = apiResponse.tasks.map(task => {
    // Convert ISO date to readable format
    const date = new Date(task.start_time);
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
    const formattedTime = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return {
      id: task._id,
      title: task.session_name || task.description || 'Untitled Session',
      icon: task.agent_name === 'CodeMaintenance' ? 'edit' : 'code',
      branch: `task/${task._id}`,
      status: task.status === 'RUNNING' ? 'Running' :
              task.status === 'stopped' ? 'Stopped' :
              task.status === 'failed' ? 'Failed' :
              task.status || 'Unknown',
      date: `${formattedDate} • ${formattedTime}`,
      duration: task.duration || "<1m",
      // Move container_id to session level
      container_id: task.container_id,
      session_id: task.session_id,
      llm_model: task.llm_model,
      // Extract additional properties if available
      messages: task.messages || [],
      // Add architecture_id if it can be derived or is available
      architecture_id: task.architecture_id || null,
      // Add merge information with formatted time display
      merge_status: transformMergeStatus(task.merge_status || []),
      merge_summary: task.merge_summary || null,
      stop_reason: task.stop_reason || null,
    };
  });

  return {
    sessions: transformedSessions,
    pagination: {
      total_count: apiResponse.total_count || apiResponse.tasks.length,
      limit: apiResponse.limit || 10,
      skip: apiResponse.skip || 0
    },
    // Keep container_id at the top level for easy access
    container_id: apiResponse.tasks[0]?.container_id || null
  };
};

/**
 * Format session data for the Sessions component
 */
export const formatSessionData = (sessions) => {
  return sessions.map(session => ({
    ...session,
    // Ensure consistent formatting
    title: session.title || session.session_name || 'Untitled Session',
    status: session.status || 'Unknown',
    duration: session.duration || '<1m'
  }));
};

/**
 * Extract container_id from sessions data
 */
export const extractContainerIds = (sessions) => {
  const containerIds = new Set();
  sessions.forEach(session => {
    if (session.container_id) {
      containerIds.add(session.container_id);
    }
  });
  return Array.from(containerIds);
};

/**
 * Get merge status summary for a session
 */
export const getMergeStatusSummary = (session) => {
  if (!session.merge_status || !Array.isArray(session.merge_status) || session.merge_status.length === 0) {
    return {
      total: 0,
      successful: 0,
      failed: 0,
      pending: 0,
      hasAnyMerge: false
    };
  }

  const summary = session.merge_status.reduce((acc, merge) => {
    acc.total++;
    if (merge.merge_status === 'success') {
      acc.successful++;
    } else if (merge.merge_status === 'failed') {
      acc.failed++;
    } else if (merge.merge_status === 'pending') {
      acc.pending++;
    }
    return acc;
  }, {
    total: 0,
    successful: 0,
    failed: 0,
    pending: 0,
    hasAnyMerge: true
  });

  return summary;
};

/**
 * Get latest merge status for a session
 */
export const getLatestMergeStatus = (session) => {
  if (!session.merge_status || !Array.isArray(session.merge_status) || session.merge_status.length === 0) {
    return null;
  }

  // Sort by merge_time descending to get the latest
  const sortedMerges = [...session.merge_status].sort((a, b) =>
    new Date(b.merge_time) - new Date(a.merge_time)
  );

  return sortedMerges[0];
};

/**
 * Example usage with the provided JSON structure:
 *
 * const apiResponse = {
 *   "tasks": [
 *     {
 *       "_id": "cgfb3337d6",
 *       "container_id": 1723,
 *       "llm_model": "bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0",
 *       "start_time": "2025-05-29T13:41:57.557955+00:00",
 *       "status": "stopped",
 *       "session_id": "058c4e5b-9601-46f8-a867-dd45a05f9acf",
 *       "session_name": "Untitled",
 *       "duration": "<1m",
 *       "merge_status": [
 *         {
 *           "repo_name": "Workflow-Manager-admin/interactive-tic-tac-toe-9979aca6",
 *           "merge_time": "2025-07-17T11:47:56.731259+00:00",
 *           "merge_status": "success",
 *           "error": null,
 *           "sha": "cf1e3988aa5c5acfcaefc839a61a45d5c94c047a"
 *         }
 *       ]
 *     }
 *   ],
 *   "total_count": 1,
 *   "limit": 10,
 *   "skip": 0
 * };
 *
 * const transformed = transformSessionsResponse(apiResponse);
 * // Result:
 * // {
 * //   sessions: [
 * //     {
 * //       id: "cgfb3337d6",
 * //       title: "Untitled",
 * //       container_id: 1723,  // Now at session level
 * //       session_id: "058c4e5b-9601-46f8-a867-dd45a05f9acf",
 * //       llm_model: "bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0",
 * //       status: "Stopped",
 * //       duration: "<1m",
 * //       merge_status: [
 * //         {
 * //           repo_name: "Workflow-Manager-admin/interactive-tic-tac-toe-9979aca6",
 * //           merge_time: "2025-07-17T11:47:56.731259+00:00",
 * //           formatted_merge_time: "Jul 17, 2025 • 5:17 PM",  // Human-readable time
 * //           merge_status: "success",
 * //           status_color: "green",  // Color coding for UI
 * //           error: null,
 * //           sha: "cf1e3988aa5c5acfcaefc839a61a45d5c94c047a"
 * //         }
 * //       ],
 * //       // ... other formatted properties
 * //     }
 * //   ],
 * //   pagination: { total_count: 1, limit: 10, skip: 0 },
 * //   container_id: 1723  // Also available at top level
 * // }
 *
 * // Utility functions usage:
 * const session = transformed.sessions[0];
 * const mergeSummary = getMergeStatusSummary(session);
 * // { total: 1, successful: 1, failed: 0, pending: 0, hasAnyMerge: true }
 *
 * const latestMerge = getLatestMergeStatus(session);
 * // Returns the most recent merge status object with formatted_merge_time
 */