// Supabase Logo Component
export const SupabaseLogo = () => (
    <svg width="24" height="24" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear_supabase)" />
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear_supabase)" fillOpacity="0.2" />
      <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E" />
      <defs>
        <linearGradient id="paint0_linear_supabase" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
          <stop stopColor="#249361" />
          <stop offset="1" stopColor="#3ECF8E" />
        </linearGradient>
        <linearGradient id="paint1_linear_supabase" x1="36.1558" y1="30.578" x2="54.4844" y2="65.0806" gradientUnits="userSpaceOnUse">
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
  
  // Stripe Logo Component
 export const StripeLogo = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.274 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z" fill="white" />
    </svg>
  );
  
  // GitHub Logo Component
  export const GitHubLogo = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M12 2C6.477 2 2 6.477 2 12c0 4.418 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.34-3.369-1.34-.454-1.156-1.11-1.463-1.11-1.463-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.831.092-.646.35-1.086.636-1.336-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.269 2.75 1.025A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.294 2.747-1.025 2.747-1.025.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.935.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48C19.138 20.161 22 16.416 22 12c0-5.523-4.477-10-10-10z" fill="white"/>
    </svg>
  );

export const GitlabLogo = () => (
  <svg 
    id="Layer_1" 
    xmlns="http://www.w3.org/2000/svg" 
    version="1.1" 
    viewBox="0 0 380 380"
    width="20"
    height="20"
  >
    <defs>
      <style>
        {`
          .st0 {
            fill: #fca326;
          }
          .st1 {
            fill: #fc6d26;
          }
          .st2 {
            fill: #e24329;
          }
        `}
      </style>
    </defs>
    <g id="LOGO">
      <g>
        <path 
          className="st2" 
          d="M265.3,174.4l-.2-.6-21.2-55.3c-.4-1.1-1.2-2-2.2-2.6s-2.1-.9-3.3-.9-2.3.5-3.2,1.2c-.9.7-1.6,1.7-1.9,2.9l-14.3,43.8h-57.9l-14.3-43.8c-.3-1.1-1-2.1-1.9-2.9-.9-.7-2-1.2-3.2-1.2s-2.3.2-3.3.9c-1,.6-1.8,1.5-2.2,2.6l-21.2,55.3-.2.6c-6.3,16.4-.9,34.9,13.1,45.5h.2c0,.1,32.3,24.3,32.3,24.3l16,12.1,9.7,7.3c2.3,1.8,5.6,1.8,7.9,0l9.7-7.3,16-12.1,32.5-24.3h0c14-10.6,19.3-29.1,13-45.5h.1Z"
        />
        <path 
          className="st1" 
          d="M265.3,174.4l-.2-.6c-10.5,2.2-20.2,6.6-28.5,12.8-.1,0-25.2,19.1-46.6,35.2,15.8,12,29.6,22.4,29.6,22.4l32.5-24.3h0c14-10.6,19.3-29.1,13-45.5h.2Z"
        />
        <path 
          className="st0" 
          d="M160.3,244.2l16,12.1,9.7,7.3c2.3,1.8,5.6,1.8,7.9,0l9.7-7.3,16-12.1s-13.8-10.4-29.6-22.4c-15.9,12-29.7,22.4-29.7,22.4h0Z"
        />
        <path 
          className="st1" 
          d="M143.4,186.6c-8.3-6.2-18-10.7-28.5-12.8l-.2.6c-6.3,16.4-.9,34.9,13.1,45.5h.2c0,.1,32.3,24.3,32.3,24.3,0,0,13.8-10.4,29.7-22.4-21.3-16.1-46.4-35.1-46.6-35.2h0Z"
        />
      </g>
    </g>
  </svg>
);