import React, { useState, useEffect } from 'react';
import { ProjectBlueprint as ProjectBlueprintType, ProjectCreationState, FrameworkOption } from './types';
// import TemplateSelector from './TemplateSelector';
import ProjectBlueprint from './ProjectBlueprint';

interface ProjectCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartImplementation: (state: ProjectCreationState) => void;
  initialBlueprint?: ProjectBlueprintType; // Added prop for initial blueprint data
  frameworkOptions?: FrameworkOption[]; // Add framework options prop
  isImplementing?: boolean; // Add isImplementing prop to show loading state
}

const ProjectCreationModal: React.FC<ProjectCreationModalProps> = ({
  isOpen,
  onClose,
  onStartImplementation,
  initialBlueprint,
  frameworkOptions = [],
  isImplementing = false
}) => {

  // Add debug logging to see the initialBlueprint prop
  useEffect(() => {
    if (isOpen && initialBlueprint) {
      
    }
  }, [isOpen, initialBlueprint]);

  const [state, setState] = useState<ProjectCreationState>({
    currentStep: 'blueprint', // Start directly with blueprint step
    selectedTemplate: null,
    projectBlueprint: null,
    customRequirements: ''
  });

  // When the modal opens and we have initialBlueprint data,
  // set the projectBlueprint and go directly to blueprint step
  useEffect(() => {
    if (isOpen && initialBlueprint) {
      
      setState(prev => ({
        ...prev,
        currentStep: 'blueprint', // Skip directly to blueprint step
        projectBlueprint: initialBlueprint
      }));
    }
  }, [isOpen, initialBlueprint]);

  // Template selection logic is commented out but kept for future use
  /*
  const handleTemplateSelect = (template: Template) => {
    setState(prev => ({
      ...prev,
      selectedTemplate: template
    }));
  };
  */

  const handleBlueprintUpdate = (blueprint: ProjectBlueprintType) => {
    // Prevent unnecessary updates that can cause loops
    if (JSON.stringify(blueprint) === JSON.stringify(state.projectBlueprint)) {
      return; // Skip update if nothing has changed
    }
    
    
    setState(prev => ({
      ...prev,
      projectBlueprint: blueprint
    }));
  };

  /*
  const handleBack = () => {
    setState(prev => ({
      ...prev,
      currentStep: 'template'
    }));
  };
  */

  const handleStartImplementation = () => {
    // Log state before starting implementation
    
    onStartImplementation(state);
  };

  /*
  const handleSkip = () => {
    // Skip directly to project blueprint screen without a template selection
    setState(prev => ({
      ...prev,
      currentStep: 'blueprint',
      selectedTemplate: null
    }));
  };

  const handleContinue = () => {
    if (state.currentStep === 'template') {
      if (state.selectedTemplate) {
        setState(prev => ({
          ...prev,
          currentStep: 'blueprint'
        }));
      }
    } else {
      handleStartImplementation();
    }
  };
  */

  // Reset state when modal is closed, but don't do this effect on first render
  const initialRender = React.useRef(true);
  React.useEffect(() => {
    if (initialRender.current) {
      initialRender.current = false;
      return;
    }
    
    if (!isOpen) {
      
      setState({
        currentStep: 'blueprint',
        selectedTemplate: null,
        projectBlueprint: null,
        customRequirements: ''
      });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white text-gray-800 rounded-lg w-full max-w-4xl shadow-xl relative flex flex-col max-h-[90vh]">
        {/* Header with close button */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <div>
            <h2 className="typography-body-lg font-weight-semibold text-gray-800">
              Get Ready to Build Your App
            </h2>
            <p className="typography-body-sm text-gray-500 mt-1">
              Review and customize your project settings below to match your vision. Once you&apos;re satisfied, start implementation and watch your app come to life.
            </p>
          </div>
          <button
            onClick={onClose}
            className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50 disabled:opacity-50 disabled:cursor-not-allowed ml-4 flex-shrink-0"
            disabled={isImplementing}
            aria-label="Close modal"
            title="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto p-6">

          <ProjectBlueprint
            onBlueprintUpdate={handleBlueprintUpdate}
            initialBlueprint={state.projectBlueprint || undefined}
            frameworkOptions={frameworkOptions}
            isDarkMode={false}
          />
        </div>

        {/* Footer with action button */}
        <div className="border-t border-gray-200 p-6 flex-shrink-0">
          <div className="flex justify-end">
            <button
              onClick={handleStartImplementation}
              disabled={isImplementing}
              className={`px-6 py-2 bg-primary text-white rounded-md ${isImplementing ? 'opacity-70 cursor-not-allowed' : 'hover:bg-primary-600'} flex items-center`}
            >
              {isImplementing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Starting session...
                </>
              ) : (
                'Start Session'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCreationModal;