import React, { useState, useContext, useRef, useEffect } from 'react';
import { X, Upload, Trash2, Gith<PERSON>, <PERSON>Circle, CheckCircle2, RefreshCw, ArrowLeft, ChevronDown, Link, Loader2, Code } from 'lucide-react';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { useRouter } from 'next/navigation';
import {
  checkGitConnectionStatus,
  gitHubConnect,
  gitHubDisconnect,
  getRepoDetails,
  getRepoBranches,
  getBranchDetails,
  clone_and_build,
  fetchRepositoryDetails,
  fetchRepository
} from "@/utils/gitAPI";
import { FiArrowRight } from "react-icons/fi";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import RepoImportTable from './RepoImportTable';
import { createNewNode, extractTextFromFile, getScmConfiguration } from "../../utils/api";
import { buildProjectUrl } from '@/utils/navigationHelpers';
import { TopBarContext } from '@/components/Context/TopBarContext';
import Cookies from 'js-cookie';

interface ImportProjectModalProps {
  isOpen: boolean;
  onClose: (closeDrawer?:boolean) => void;
  refreshProjectsList?: () => void;
  projectId?: string;
  initialProjectName?: string;
  handleNameChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

// Connection status interface
interface ConnectionStatus {
  isConnected: boolean;
  loading: boolean;
  error: string | null;
  username: string | null;
}

// Repository interface
interface Repository {
  id: number;
  name: string;
  description: string | null;
  languages: string[];
  branch: string | null;
  branches: Array<{
    name: string;
    isDefault: boolean;
  }>;
  selected: boolean;
  lastUpdated: string;
  clone_url: string;
  path?: string;
  repo_type?: 'public' | 'private';
  full_name?: string;
  default_branch?: string;
  html_url?: string;
  private?: boolean;
  owner?: {
    login: string;
  };
  encrypted_scm_id?: string | null;
}

// Imported repository interface
interface ImportedRepository {
  id: number;
  name: string;
  full_name: string;
  branch: string;
  repo_type: 'public' | 'private';
  html_url?: string;
  description?: string | null;
  importedAt: string;
  encrypted_scm_id?: string | null;
}

// File interface for multiple file uploads
interface SelectedFile {
  id: string;
  file: File;
  name: string;
  size: string;
  type: string;
}

//scm_configurations
interface ScmConfiguration {
  scm_type: string;
  credentials: {
    auth_type: string;
    token_expires_at: string | null;
    organization: string;
  };
  encrypted_scm_id: string;
  webhook_url: string | null;
  webhook_secret: string | null;
  api_url: string | null;
}

const ImportProjectModal: React.FC<ImportProjectModalProps> = ({
  isOpen,
  onClose,
  refreshProjectsList,
  projectId,
  initialProjectName,
  handleNameChange
}) => {
  // Initialize router for navigation
  const router = useRouter();

  // Form data state
  const [formData, setFormData] = useState({
    projectTitle: initialProjectName || '',
    projectOverview: '',
    repositorySource: '',
    repositoryUrl: '',
    operationType: ['Query'],
    files: [] as SelectedFile[], // Changed from single file to multiple files
    repositoryBranch: '',
  });

  // Loading and connection states
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    loading: true,
    error: null,
    username: null,
  });

  // Repository states
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [selectedCount, setSelectedCount] = useState(0);
  const [importedRepositories, setImportedRepositories] = useState<ImportedRepository[]>([]);

  // Branch selection states
  const [branches, setBranches] = useState<string[]>([]);
  const [isFetchingBranches, setIsFetchingBranches] = useState(false);
  const [gitRepoId, setGitRepoId] = useState('');

  // View states
  const [showRepositories, setShowRepositories] = useState(false);
  const [showRepoList, setShowRepoList] = useState(false);
  const [showConnectionDetails, setShowConnectionDetails] = useState(false);
  
  // Public repo states
  const [gitUrl, setGitUrl] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('');
  const [showBranchDropdown, setShowBranchDropdown] = useState(false);
  const [searchedBranch, setSearchedBranch] = useState('');
  const [isSearchingBranch, setIsSearchingBranch] = useState(false);

  // Refs and context
  const { showAlert } = useContext(AlertContext);
  const {addTab} = useContext(TopBarContext)
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper function to format file size
  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  //Tabs for git modal 
  const[activeTabVal,setActiveTabVal] = useState("personal");

  //scm
  const [scmConfigurations, setSCMConfigurations] = useState<ScmConfiguration[]>([]);
  const [selectedSCMId, setSelectedSCMId] = useState<string | null>(null);
  const [scanningRepoId, setScanningRepoId] = useState<string | null>(null);

  // Update project title when initialProjectName changes
  useEffect(() => {
    if (initialProjectName && initialProjectName !== formData.projectTitle) {
      setFormData(prev => ({
        ...prev,
        projectTitle: initialProjectName
      }));
    }
  }, [initialProjectName]);

  // Effect to handle repository source changes
  useEffect(() => {
    
    if (formData.repositorySource === 'GitHub') {
      setShowConnectionDetails(true);
      checkConnection();
    } else {
      setShowConnectionDetails(false);
    }
  }, [formData.repositorySource]);

  // Check GitHub connection status
  const checkConnection = async () => {
    try {
      setConnectionStatus((prev) => ({ ...prev, loading: true }));
      const response = await checkGitConnectionStatus();
      
      setConnectionStatus({
        isConnected: response.git_connected,
        loading: false,
        error: null,
        username: response.username,
      });
    } catch (error) {
      console.error('Error checking Git connection status:', error);
      setConnectionStatus({
        isConnected: false,
        loading: false,
        error: "Failed to check connection status",
        username: null,
      });
      showAlert('GitHub authentication required. Please connect your GitHub account.', 'warning');
    }
  };

  //get scm configurations
  const fetchSCMConfigurations = async () => {
      try {
  
        const response = await getScmConfiguration();
        if (response.status === "success") {
          setSCMConfigurations(response.data?.configurations || []);
        }
      } catch (err) {
        
        console.error('Failed to fetch SCM configurations');
      }
  };

  useEffect(() => {
    fetchSCMConfigurations();
  }, []);

  // Extract repository info from URL
  const extractRepoInfo = (url: string) => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      const cleanParts = pathParts
        .filter(part => part)
        .map(part => part.replace('.git', ''));

      if (cleanParts.length >= 2) {
        return {
          owner: cleanParts[0],
          repo: cleanParts[1],
        };
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  // Improved fetch branches function with better error handling
  const fetchBranches = async (owner: string, repo: string) => {
    try {
      setIsFetchingBranches(true);
      

      const repoData = await getRepoDetails(owner, repo);
      if (!repoData) {
        throw new Error("Failed to fetch repository details");
      }

      if (repoData.detail && repoData.detail.includes('Bad credentials')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        throw new Error('GitHub authentication expired. Please reconnect your account.');
      }

      // Updated API call for branches with proper error handling
      const branchResponse = await getRepoBranches(owner, repo);

      if (!branchResponse || branchResponse.length === 0) {
        // If no branches returned, try to use the default branch
        const defaultBranch = repoData.default_branch || 'main';
        setBranches([defaultBranch]);
        setSelectedBranch(defaultBranch);
        setFormData(prev => ({ ...prev, repositoryBranch: defaultBranch }));
        setGitRepoId(repoData.id.toString());
        return;
      }

      if (branchResponse && (branchResponse as any).detail && (branchResponse as any).detail.includes('Bad credentials')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        throw new Error('GitHub authentication expired. Please reconnect your account.');
      }

      const branchList = branchResponse.map((branch: any) => branch.name);
      setBranches(branchList);
      setGitRepoId(repoData.id.toString());

      if (branchList.length > 0) {
        const defaultBranch = repoData.default_branch || branchList[0];
        setSelectedBranch(defaultBranch);
        setFormData(prev => ({ ...prev, repositoryBranch: defaultBranch }));
      }
    } catch (error: any) {
      console.error('Error fetching repository data:', error);
      
      if (error.message?.includes('Bad credentials') || error.message?.includes('Authentication failed') || error.message?.includes('authentication expired')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        showAlert('GitHub authentication error: Please reconnect your GitHub account', 'error');
      } else {
        showAlert("Error fetching repository data. Using default branch.", "warning");
        // Set default branch as fallback
        setBranches(['main']);
        setSelectedBranch('main');
        setFormData(prev => ({ ...prev, repositoryBranch: 'main' }));
      }
    } finally {
      setIsFetchingBranches(false);
    }
  };

  // Form input handler
  type InputElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

  const handleChange = (e: React.ChangeEvent<InputElement>) => {
    const { name, value, type } = e.target;

    if (type === 'file') {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        handleFileChange(e as React.ChangeEvent<HTMLInputElement>);
      }
    } else {
      setFormData(prevState => ({
        ...prevState,
        [name]: value
      }));
      
      if (handleNameChange && e.target instanceof HTMLInputElement) {
        handleNameChange(e as React.ChangeEvent<HTMLInputElement>);
      }
    }
  };

  // Add a processFiles function to handle both drag-and-drop and file input
  const processFiles = (files: File[]) => {
    if (files.length > 0) {
      // Show notification if uploading multiple files
      if (files.length > 1) {
        showAlert(`Selected ${files.length} files for upload`, "info");
      }
      
      // Convert FileList to array and create SelectedFile objects
      const newFiles: SelectedFile[] = Array.from(files).map((file, index) => ({
        id: Date.now() + index + Math.random().toString(36).substr(2, 9), // Unique ID
        file: file,
        name: file.name,
        size: formatBytes(file.size),
        type: file.name.split(".").pop()?.toUpperCase() || "FILE"
      }));
      
      // Add new files to existing files (allow multiple selections)
      setFormData(prev => ({
        ...prev,
        files: [...prev.files, ...newFiles]
      }));
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Update the handleFileChange function to use processFiles
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    processFiles(Array.from(files));
  };

  // File upload handlers
  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  // Remove individual file
  const handleFileDelete = (fileId: string) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter(file => file.id !== fileId)
    }));
    showAlert('File removed from selection', 'success');
  };

  // Clear all files
  const handleClearAllFiles = () => {
    setFormData(prev => ({
      ...prev,
      files: []
    }));
    showAlert('All files removed', 'success');
  };

  const handleRepositorySourceClick = (sourceId: string) => {
    
    
    // Don't allow selection of disabled sources
    if (sourceId === 'GitLab') {
      
      return;
    }
    
    // Reset states when selecting GitHub
    if (sourceId === 'GitHub') {
      
      setShowRepoList(false);
      setRepositories([]);
      setSelectedCount(0);
      // Reset branch selection states
      setBranches([]);
      setSelectedBranch('');
      setGitUrl('');
    }
    
    // Directly open file picker when LocalUpload is selected
    if (sourceId === 'LocalUpload') {
      setShowRepoList(false);
      setRepositories([]);
      setSelectedCount(0);
      setShowConnectionDetails(false);
      setBranches([]);
      setSelectedBranch('');
      setGitUrl('');
      setGitRepoId('');
      
      // Set the repository source first
      setFormData(prev => ({ ...prev, repositorySource: sourceId }));
      
      // Then trigger file input click
      setTimeout(() => {
        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      }, 100);
      
      return;
    }
    
    
    setFormData(prev => ({ ...prev, repositorySource: sourceId }));
  };

  // GitHub connection handlers
  const handleConnect = async () => {
    try {
      setConnectionStatus(prev => ({ ...prev, loading: true }));
      const response = await gitHubConnect();
      if (response.url) {
        const newWindow = window.open(response.url, "_blank");
        setTimeout(() => {
          checkConnection();
        }, 3000);

        const checkWindowClosed = setInterval(() => {
          if (newWindow && newWindow.closed) {
            clearInterval(checkWindowClosed);
            checkConnection();
          }
        }, 500);
      } else {
        await checkConnection();
      }
    } catch (error: any) {
      console.error('Error connecting to GitHub:', error);
      setConnectionStatus((prev) => ({
        ...prev,
        loading: false,
        error: "Failed to initiate GitHub connection",
      }));
      showAlert('Failed to connect to GitHub. Please try again.', 'error');
    }
  };

  const handleDisconnect = async () => {
    try {
      await gitHubDisconnect();
      setShowRepoList(false);
      setRepositories([]);
      setSelectedCount(0);
      await checkConnection();
      showAlert('Successfully disconnected from GitHub', 'success');
    } catch (error) {
      console.error('Error disconnecting from GitHub:', error);
      showAlert('Failed to disconnect from GitHub', 'error');
    }
  };

  // Repository scanning handler with proper owner handling
  const handleScanRepositories = async () => {
    try {
      setIsLoading(true);
      
      
      const response = await fetchRepository();
      
      
      if (response.detail && response.detail.includes('Bad credentials')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        throw new Error('GitHub authentication failed. Please reconnect your GitHub account.');
      }
      
      // Transform repositories with proper owner handling
      const repositoriesWithSelection = response.map((repo: any, index: number) => {
        
        
        // Use the connected username as the primary owner
        const ownerLogin = connectionStatus.username || repo.owner?.login || 'unknown';
        
        // Create owner object
        const ownerInfo = { login: ownerLogin };
        
        // Create full_name if it doesn't exist
        const fullName = repo.path ? repo.path.split('/').slice(-2).join('/') : repo.full_name || `${ownerLogin}/${repo.name}`;
        
        const transformedRepo = {
          id: repo.id,
          name: repo.name,
          description: repo.description,
          languages: repo.language ? [repo.language] : (repo.languages || []),
          branch: repo.default_branch || 'main',
          branches: [
            {
              name: repo.default_branch || 'main',
              isDefault: true
            }
          ],
          selected: false,
          lastUpdated: repo.updated_at || new Date().toISOString(),
          clone_url: repo.clone_url || repo.html_url || '',
          path: repo.html_url || repo.clone_url || '',
          repo_type: 'private',
          full_name: fullName,
          default_branch: repo.default_branch || repo.branch || 'main',
          html_url: repo.html_url,
          private: repo.private,
          owner: ownerInfo
        };
        
        
        return transformedRepo;
      });
      
      
      
      setRepositories(repositoriesWithSelection);
      setShowRepoList(true);
      
      showAlert(`Found ${response.length} repositories`, 'success');
    } catch (error: any) {
      console.error('Error fetching repositories:', error);
      
      if (error.message?.includes('Bad credentials') || error.message?.includes('authentication')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        showAlert('GitHub authentication failed. Please reconnect your account and try again.', 'error');
      } else {
        showAlert('Error fetching repositories. Please try again.', 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };


  const handleScanRepo = async (scm_id: string, owner: string) => {
    setScanningRepoId(scm_id);
    setSelectedSCMId(scm_id);

    try {
      

      const response = await fetchRepositoryDetails(scm_id);
      

      if (response.detail && response.detail.includes('Bad credentials')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        throw new Error('GitHub authentication failed. Please reconnect your GitHub account.');
      }

      // Transform repositories with proper owner handling
      const repositoriesWithSelection = response.map((repo: any, index: number) => {
        
        
        // Use the connected username as the primary owner
        const ownerLogin = owner || 'unknown';
        
        // Create owner object
        const ownerInfo = { login: ownerLogin };
        
        // Create full_name if it doesn't exist
        const fullName = repo.path ? repo.path.split('/').slice(-2).join('/') : repo.full_name || `${ownerLogin}/${repo.name}`;
        
        const transformedRepo = {
          id: repo.id,
          name: repo.name,
          description: repo.description,
          languages: repo.language ? [repo.language] : (repo.languages || []),
          branch: repo.default_branch || 'main',
          branches: [
            {
              name: repo.default_branch || 'main',
              isDefault: true
            }
          ],
          selected: false,
          lastUpdated: repo.updated_at || new Date().toISOString(),
          clone_url: repo.clone_url || repo.html_url || '',
          path: repo.html_url || repo.clone_url || '',
          repo_type: 'private',
          full_name: fullName,
          default_branch: repo.default_branch || repo.branch || 'main',
          html_url: repo.html_url,
          private: repo.private,
          owner: ownerInfo,
          encrypted_scm_id: scm_id
        };
        
        
        return transformedRepo;
      });

      
      
      setRepositories(repositoriesWithSelection);
      setShowRepoList(true);

      showAlert(`Found ${response.length} repositories`, 'success');
    } catch (error: any) {
      console.error('Error scanning scm repositories:', error);

      if (error.message?.includes('Bad credentials') || error.message?.includes('authentication')) {
        setConnectionStatus(prev => ({ ...prev, isConnected: false, error: "Authentication failed" }));
        showAlert('GitHub authentication failed. Please reconnect account and try again.', 'error');
      } else {
        showAlert('Error scanning repository. Please try again.', 'error');
      }

      setSelectedSCMId(null);
    } finally {
      setScanningRepoId(null);
    }
  };

  // Repository table handlers
  const handleToggleRepository = (index: number) => {
    const newRepositories = repositories.map((repo, i) =>
      i === index ? { ...repo, selected: !repo.selected } : repo
    );
    setRepositories(newRepositories);
    setSelectedCount(newRepositories.filter(repo => repo.selected).length);
  };

  const handleSelectAll = (checked: boolean) => {
    const newRepositories = repositories.map(repo => ({
      ...repo,
      selected: checked
    }));
    setRepositories(newRepositories);
    setSelectedCount(checked ? repositories.length : 0);
  };

  const handleBranchChange = (index: number, updates: any) => {
    const newRepositories = repositories.map((repo, i) =>
      i === index ? { ...repo, ...updates } : repo
    );
    setRepositories(newRepositories);
  };

  // Import selected repositories
  const handleImportSelectedRepositories = () => {
    const selectedRepos = repositories.filter(repo => repo.selected);
    
    if (selectedRepos.length === 0) {
      showAlert('Please select at least one repository to import.', 'warning');
      return;
    }

    // Add imported repositories to the imported list (preserve existing ones)
    const newImportedRepos = selectedRepos.map(repo => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name || repo.name,
      branch: repo.branch || repo.default_branch || 'main',
      repo_type: "private" as "private", //all repo coming from github account scan are considered "private" regardless of their visibility
      html_url: repo.html_url,
      description: repo.description,
      importedAt: new Date().toISOString(),
      encrypted_scm_id: repo.encrypted_scm_id
    }));

    setImportedRepositories(prev => [...prev, ...newImportedRepos]);
    
    // Don't clear local files when repositories are selected

    // Reset all states to allow fresh GitHub selection
    setRepositories([]);
    setSelectedCount(0);
    setShowRepoList(false);
    setShowConnectionDetails(false);
    setBranches([]);
    setSelectedBranch('');
    setGitUrl('');
    setGitRepoId('');
    
    // Reset repository source selection to allow clicking GitHub again
    setFormData(prev => ({ ...prev, repositorySource: '' }));
    
    showAlert(`Successfully selected ${selectedRepos.length} repositories!`, 'success');
  };

  // Navigation handlers
  const handleBackToForm = () => {
    setShowConnectionDetails(false);
    setShowRepoList(false);
    setRepositories([]);
    setSelectedCount(0);
    setFormData(prev => ({ ...prev, repositorySource: '' }));
    // Reset connection and branch states
    setBranches([]);
    setSelectedBranch('');
    setGitUrl('');
    setGitRepoId('');
  };

  const handleBackToConnectionDetails = () => {
    setShowRepoList(false);
    setRepositories([]);
    setSelectedCount(0);
    // Keep showConnectionDetails true to stay in connection details view
  };

  // Public repository handlers
  const validateGitUrl = (url: string) => {
    const repoInfo = extractRepoInfo(url);
    return repoInfo !== null;
  };

  const handleGitUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setGitUrl(url);
    setFormData(prev => ({ ...prev, repositoryUrl: url }));
    setBranches([]);
    setGitRepoId("");
    setSelectedBranch("");
    setFormData(prev => ({ ...prev, repositoryBranch: "" }));
  };

  const handleSelect = () => {
    if (gitUrl && branches.length === 0) {
      const repoInfo = extractRepoInfo(gitUrl);
      if (repoInfo) {
        fetchBranches(repoInfo.owner, repoInfo.repo);
      } else {
        showAlert("Invalid repository URL format", "error");
      }
    }
    setShowBranchDropdown((prev) => !prev);
  };

  const handleSelectBranch = (branchName: string) => {
    setSelectedBranch(branchName);
    setFormData(prev => ({ ...prev, repositoryBranch: branchName }));
    setShowBranchDropdown(false);
    setSearchedBranch('');
  };

  const checkBranchExistence = async (branchName: string, owner: string, repo: string) => {
    try {
      setIsSearchingBranch(true);
      const response = await getBranchDetails(branchName, owner, repo);
      
      if (response && Object.keys(response).length > 0) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    } finally {
      setIsSearchingBranch(false);
    }
  };

  const checkBranchDetails = async (e: React.FormEvent) => {
    e.preventDefault();

    const repoInfo = extractRepoInfo(gitUrl);

    if (repoInfo) {
      const branchName = searchedBranch;
      const branchExists = await checkBranchExistence(branchName, repoInfo.owner, repoInfo.repo);
      if (branchExists) {
        handleSelectBranch(branchName);
      } else {
        showAlert("Couldn't find branch. Make sure the name is correct.", "error");
      }
    }
  };

  const handleImportGitUrl = () => {
    if (!gitUrl) {
      showAlert("Please enter a Git Public URL.", "warning");
      return;
    }

    if (!validateGitUrl(gitUrl)) {
      showAlert(
        "Invalid Git Public URL format. Please enter a valid GitHub repository URL.",
        "error"
      );
      return;
    }

    if (!selectedBranch) {
      showAlert("Please select a branch.", "warning");
      return;
    }

    const repoUrl = gitUrl.replace(/\.git$/, "");
    const repoName = repoUrl.split("/").pop();
    const owner_name = repoUrl.split("/")[3];

    // Add imported repository to the imported list (preserve existing ones)
    const newImportedRepo: ImportedRepository = {
      id: parseInt(gitRepoId) || Date.now(),
      name: repoName || '',
      full_name: owner_name + "/" + repoName,
      branch: selectedBranch,
      repo_type: 'public',
      html_url: gitUrl,
      description: null,
      importedAt: new Date().toISOString()
    };

    setImportedRepositories(prev => [...prev, newImportedRepo]);
    
    // Don't clear local files when repository is selected

    showAlert("Repository selected successfully!", "success");
    
    // Reset all states to allow fresh GitHub selection
    setGitUrl("");
    setSelectedBranch("");
    setBranches([]);
    setGitRepoId('');
    setShowConnectionDetails(false);
    
    // Reset repository source selection to allow clicking GitHub again
    setFormData(prev => ({ 
      ...prev, 
      repositorySource: '',
      repositoryUrl: "", 
      repositoryBranch: "" 
    }));
  };

  // Remove imported repository
  const handleRemoveImportedRepo = (index: number) => {
    setImportedRepositories(prev => prev.filter((_, i) => i !== index));
    showAlert('Repository removed from selection', 'success');
  };

  // Upload assets function for local files
  const uploadAssets = async (projectResponse: any, files: SelectedFile[]) => {
    try {
      // Show notification if uploading multiple files with a slight delay
      if (files.length > 1) {
        showAlert(`Uploading ${files.length} documents...`, "info");
        // Add a small delay to ensure the notification is shown
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      let successCount = 0;
      let errorCount = 0;
      const uploadResults = [];

      // Process each file sequentially
      for (let i = 0; i < files.length; i++) {
        const selectedFile = files[i];
        
        if (selectedFile && selectedFile.file) {
          try {            
            const result = await extractTextFromFile(projectResponse.id, selectedFile.file);
            
            if (result) {
              successCount++;
              uploadResults.push({
                file: selectedFile.name,
                status: 'success',
                file_uuid: result.files?.[0]?.file_uuid
              });
              // Show success for individual file
            } else {
              errorCount++;
              uploadResults.push({
                file: selectedFile.name,
                status: 'error',
                error: 'No result returned from server'
              });
              showAlert(`✗ Failed to upload ${selectedFile.name}: No result returned`, "error");
            }
          } catch (error) {
            errorCount++;
            const errorMessage = error instanceof Error ? error.message : "Unknown error";
            uploadResults.push({
              file: selectedFile.name,
              status: 'error',
              error: errorMessage
            });
            console.error(`Upload error for ${selectedFile.name}:`, error);
            showAlert(`✗ Failed to upload ${selectedFile.name}: ${errorMessage}`, "error");
          }
          
          // Add a small delay between file uploads to ensure notifications are visible
          if (i < files.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
      }

      // Add delay before showing final summary
      await new Promise(resolve => setTimeout(resolve, 500));

      // Show final summary
      if (successCount > 0 && errorCount === 0) {
        showAlert(`🎉 All ${successCount} files uploaded successfully! Redirecting...`, "success");
      } else if (successCount > 0 && errorCount > 0) {
        showAlert(`⚠️ ${successCount} files uploaded, ${errorCount} failed. Redirecting...`, "info");
      } else if (errorCount > 0) {
        showAlert(`❌ All ${errorCount} files failed to upload. Redirecting...`, "error");
      }

      

      // Wait before navigation to allow user to see final message
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      showAlert(`Failed to upload assets: ${errorMessage}`, "error");
      console.error('Upload assets error:', error);
      // Wait before navigation even on error
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  };

  // Form submission handler with clone and build
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Check if we have either repositories or local files
    const hasRepositories = importedRepositories.length > 0;
    const hasLocalFiles = formData.repositorySource === 'LocalUpload' && formData.files.length > 0;
    
    if (!hasRepositories && !hasLocalFiles) {
      showAlert('Please select at least one repository or upload local files before submitting.', 'warning');
      return;
    }
    
    setIsLoading(true);
    
    try {
      
      
      
      
      // Step 1: Create the project node
      
      const response = await createNewNode(
        "Project",
        formData.projectTitle.trim(),
        formData.projectOverview.trim(),
        {}
      );
      
      // Log successful node creation
      if (response && response.id) {
        
        
        
        
        // Handle different submission types
     
        if (importedRepositories.length > 0 || formData.repositorySource === 'LocalUpload') {
          // Handle repository imports
          await uploadAssets(response, formData.files);
          
          // Step 2: Prepare repositories for clone and build
          const repositoriesPayload = importedRepositories.map(repo => {
            // Extract repo name from full_name (owner/repo format) or use name
            const repoName = repo.full_name || repo.name;
            
            return {
              repo_id: String(repo.id),
              repo_name: repoName,
              branch_name: repo.branch,
              repo_type: repo.repo_type, // 'public' or 'private'
              associated: true,
              encrypted_scm_id: repo.encrypted_scm_id || ""
            };
          });

          
          // Step 3: Clone and build repositories
          
          const clonePayload = {
            project_id: String(response.id), // Use the project ID from node creation
            repositories: repositoriesPayload
          };

          
          const cloneResponse = await clone_and_build(clonePayload);
          
          
          
          if (cloneResponse.status === "success") {
            
            showAlert('Project created and repositories imported successfully!', 'success');
            
            // Reset form
            setFormData({
              projectTitle: '',
              projectOverview: '',
              repositorySource: '',
              repositoryUrl: '',
              operationType: ['Query'],
              files: [],
              repositoryBranch: '',
            });
            setImportedRepositories([]);
            
              const hasQueryOperation = formData.operationType.includes('Query');
              const hasModifyOperation = formData.operationType.includes('Modify');
              const hasExtractOperation = formData.operationType.includes('Extract');

              // Determine the redirect path based on operation type
              let redirectPath = 'query'; // default
              let queryParams = '';
              
              if (hasModifyOperation) {
                redirectPath = 'code';
                queryParams = '?tab=maintenance';
              } else if (hasExtractOperation) {
                redirectPath = 'query';
                queryParams = '?openAssets=true'; // Add query param to auto-open project assets
              } else if (hasQueryOperation) {
                redirectPath = 'query';
                queryParams = '';
              }

              const projectRedirectUrl = `${buildProjectUrl(response.id, redirectPath)}${queryParams}`;
              addTab(response?.properties?.Title, projectRedirectUrl)
              Cookies.set('is_public_selected', 'false')
              

              // Use Next.js router for navigation
              onClose(true);
              router.push(projectRedirectUrl);
            
          } else {
            console.error('Clone and build failed:', cloneResponse);
            throw new Error('Failed to import repositories - clone and build process failed');
          }
        } else {
          throw new Error('No repositories or files selected for import or upload');
        }
        
      } else {
        throw new Error('Invalid response from node creation - missing ID');
      }
      
    } catch (error: unknown) {
      console.error('Error creating project:', error);
      
      // Handle TypeScript 'unknown' error type properly
      let errorMessage = 'Failed to save project configuration';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        // Handle specific error cases
        if (error.message?.includes('node creation') || error.message?.includes('Invalid response')) {
          errorMessage = 'Failed to create project node';
        } else if (error.message?.includes('clone and build')) {
          errorMessage = 'Project created but failed to import repositories';
        } else if (error.message?.includes('import repositories')) {
          errorMessage = 'Failed to import repositories';
        } else if (error.message?.includes('upload assets')) {
          errorMessage = 'Project created but failed to upload files';
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      showAlert(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const isFormValid = (): boolean => {
    const hasTitle = formData.projectTitle.trim() !== '';
    const hasOverview = formData.projectOverview.trim() !== ''; 
    const hasOperationType = formData.operationType.length > 0;
    const hasSelectedRepositories = importedRepositories.length > 0;
    const hasLocalFiles = formData.files.length > 0;
    
    return hasTitle && hasOverview && hasOperationType && (hasSelectedRepositories || hasLocalFiles); 
  };

  // Configuration arrays
  const repositorySources = [
    { id: 'GitHub', label: 'GitHub', description: 'Connect to GitHub repo', disabled: false },
    { id: 'GitLab', label: 'GitLab', description: 'Coming soon', disabled: true },
    { id: 'LocalUpload', label: 'Local Upload', description: 'Upload local files', disabled: false }
  ];

  const operationTypes = [
    { id: 'Query', label: 'Query', description: 'Extract information from codebase', disabled: false },
    { id: 'Modify', label: 'Modify', description: 'Make changes to existing code', disabled: false },
    { id: 'Extract', label: 'Extract', description: 'Extract and analyze codebase structure', disabled: false } 
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => onClose()}
      />
      <div className="relative w-full max-w-6xl mx-4 bg-white shadow-xl rounded-lg max-h-[90vh] min-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            {(showConnectionDetails && !showRepoList) && (
              <button
                onClick={handleBackToForm}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="Back to form"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            )}
            {showRepoList && (
              <button
                onClick={handleBackToConnectionDetails}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="Back to connection details"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            )}
            <h2 className="text-xl font-semibold text-black">
              {showRepoList ? 'Select Repositories' : showConnectionDetails ? 'Import Repository' : 'Import Project'}
            </h2>
          </div>
          <button
            onClick={() => onClose()}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          {showRepoList ? (
            /* Repository List Table View with Custom Component */
            <div className="flex flex-col h-full">
              {/* Breadcrumb Navigation */}
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <nav className="flex items-center space-x-2 text-sm">
                  <button
                    onClick={handleBackToConnectionDetails}
                    className="text-primary-600 font-medium hover:text-primary-700 transition-colors cursor-pointer underline"
                  >
                    Connection Details
                  </button>
                  <ChevronDown className="w-4 h-4 text-gray-400 rotate-[-90deg]" />
                  <span className="text-black font-medium">Repositories List</span>
                </nav>
              </div>

              <div className="flex-1 p-6">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-black">
                    Available Repositories ({repositories.length})
                  </h3>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={handleScanRepositories}
                      className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <RefreshCw className="w-4 h-4 text-black" />
                      <span className='text-black'>Refresh</span>
                    </button>
                    {selectedCount > 0 && (
                      <button
                        onClick={handleImportSelectedRepositories}
                        className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                      >
                        <span>Select Repositories ({selectedCount})</span>
                        <FiArrowRight className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Repo Import Table */}
                <RepoImportTable
                  repositories={repositories}
                  onToggleRepository={handleToggleRepository}
                  onBranchChange={handleBranchChange}
                  selectedCount={selectedCount}
                  onSelectAll={handleSelectAll}
                  isLoading={isLoading}
                  activeTabVal="personal"
                  organization_name={""}
                  connectedUsername={connectionStatus.username}
                  importedRepositories={importedRepositories}
                />
              </div>
            </div>
          ) : showConnectionDetails ? (
            /* Connection Details View */
            <div className="p-6">
              <div className="">
                <span className="text-sm text-gray-600">Connection Details</span>
              </div>

              <div className="mb-4">
                <nav className="flex justify-center space-x-8 border-b border-gray-200 -mt-4">
                  <button
                    onClick={() => setActiveTabVal('personal')}
                    className={`py-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTabVal === 'personal'
                      ? 'border-primary text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    Personal Accounts
                  </button>
                  <button
                    onClick={() => setActiveTabVal('organization')}
                    className={`py-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTabVal === 'organization'
                      ? 'border-primary text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    Organization Accounts
                  </button>
                </nav>
              </div>
              
              {activeTabVal=="personal" && (
                <div className="max-w-lg mx-auto">
                  <div className="w-full p-4 border border-gray-200 rounded-lg bg-white">
                    <div className="flex flex-col items-center space-y-3">
                      <div className="flex items-center space-x-2">
                        <Github className="w-6 h-6 text-gray-700" />
                        {connectionStatus.loading && (
                          <div className="w-4 h-4 border-2 border-gray-200 border-t-orange-500 rounded-full animate-spin" />
                        )}
                        {connectionStatus.isConnected && (
                          <CheckCircle2 className="w-5 h-5 text-green-500" />
                        )}
                        {!connectionStatus.isConnected && !connectionStatus.loading && (
                          <XCircle className="w-5 h-5 text-red-600" />
                        )}
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-black">
                          {connectionStatus.loading && 'Checking connection...'}
                          {connectionStatus.isConnected && 'Connected to GitHub'}
                          {!connectionStatus.isConnected && !connectionStatus.loading && 'GitHub Authentication Required'}
                        </p>
                        {connectionStatus.isConnected && connectionStatus.username && (
                          <p className="text-sm text-gray-600">
                            {connectionStatus.username}
                          </p>
                        )}
                        {connectionStatus.error && (
                          <p className="text-xs text-red-600 mt-1">
                            Please connect your GitHub account to access repositories
                          </p>
                        )}
                      </div>
                      
                      {connectionStatus.isConnected && (
                        <div className="flex flex-col w-full space-y-2 mt-4">
                          <button
                            type="button"
                            onClick={handleScanRepositories}
                            disabled={isLoading}
                            className={`w-full px-3 py-1.5 text-white rounded-md transition-colors ${
                              isLoading
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-primary-600 hover:bg-primary-700'
                            }`}
                          >
                            {isLoading ? (
                              <div className="flex items-center justify-center gap-2">
                                <RefreshCw className="w-4 h-4 animate-spin" />
                                Scanning...
                              </div>
                            ) : (
                              'Scan Repositories'
                            )}
                          </button>
                          <button
                            type="button"
                            onClick={handleDisconnect}
                            className="w-full px-3 py-1.5 text-red-600 border border-red-200 rounded-md hover:bg-red-50 transition-colors"
                          >
                            Disconnect
                          </button>
                        </div>
                      )}
                      
                      {!connectionStatus.isConnected && !connectionStatus.loading && (
                        <div className="w-full mt-4">
                          <button
                            type="button"
                            onClick={handleConnect}
                            className="w-full px-3 py-1.5 text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors"
                          >
                            Connect to GitHub
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* GitHub Or Section */}
                  <div className="relative mt-6">
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                      <div className="w-full border-t border-gray-200" />
                    </div>
                    <div className="relative flex justify-center">
                      <span className="px-2 bg-white text-sm text-gray-600">Or</span>
                    </div>
                  </div>

                  {/* Public Repository Section */}
                  <div className="border border-gray-200 rounded-lg p-6 mt-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <Link className="w-6 h-6 text-gray-400" />
                      <h3 className="text-sm font-medium text-black">
                        Import Public Repository
                      </h3>
                    </div>
                    <div className="space-y-4 flex flex-col gap-4">
                      <input
                        type="text"
                        placeholder="Repository URL (e.g., https://github.com/user/repo)"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                        value={gitUrl}
                        onChange={handleGitUrlChange}
                      />

                      {gitUrl && (
                        <div className="relative">
                          {showBranchDropdown && (
                            <div className="absolute border border-gray-500 w-full bg-white rounded-md z-20 max-h-[200px] overflow-y-scroll top-[calc(100%+4px)]">
                              {isFetchingBranches ? (
                                <div className="text-gray-600 px-4 py-2">Loading Branches...</div>
                              ) : branches.length === 0 ? (
                                <div className="text-gray-600 px-4 py-2">No branches found</div>
                              ) : (
                                <div>
                                  {branches.length > 30 && (
                                    <form className="flex px-4 py-2 items-center justify-between gap-4" onSubmit={checkBranchDetails}>
                                      <input 
                                        className="border-gray-300 rounded-md flex-1 text-black px-2 py-1" 
                                        value={searchedBranch} 
                                        type='text' 
                                        placeholder="Type the branch name manually..." 
                                        onChange={(e) => setSearchedBranch(e.target.value)} 
                                      />
                                      {searchedBranch && (
                                        <DynamicButton
                                          variant="primaryLegacy"
                                          text={isSearchingBranch ? "Searching..." : "Search"}
                                          type="submit"
                                          disabled={isSearchingBranch}
                                        />
                                      )}
                                    </form>
                                  )}
                                  {branches.map((branch, index) => (
                                    <p
                                      key={branch}
                                      className={`w-full px-4 py-2 bg-white text-black hover:bg-primary hover:text-white hover:cursor-pointer 
                                        ${index === 0 ? 'rounded-t-md' : index === branches.length - 1 ? 'rounded-b-md' : ''}`}
                                      onClick={() => handleSelectBranch(branch)}
                                    >
                                      {branch}
                                    </p>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}
                          <div 
                            className={`w-full z-10 border border-gray-200 h-[30px] rounded-md flex justify-between px-4 items-center cursor-pointer ${showBranchDropdown ? 'ring-2 ring-primary-500' : ''}`} 
                            onClick={showBranchDropdown ? () => setShowBranchDropdown(prev => !prev) : handleSelect}
                          >
                            <p className={selectedBranch ? "text-black" : "text-gray-500"}>
                              {selectedBranch ? selectedBranch : "Select Branch"}
                            </p>
                            <ChevronDown className={`w-4 h-4 transition-transform text-gray-600 ${showBranchDropdown ? 'rotate-180' : ''}`} />
                          </div>
                          {isFetchingBranches && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-900"></div>
                            </div>
                          )}
                        </div>
                      )}
                      
                      <button
                        type="button"
                        className={`w-full bg-primary text-white py-2 rounded-md hover:bg-primary-600 transition duration-300 flex items-center justify-center ${
                          !selectedBranch ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        onClick={handleImportGitUrl}
                        disabled={!selectedBranch}
                      >
                        Select Repository <FiArrowRight className="ml-2" />
                      </button>
                      <p className="text-xs text-gray-600 text-center">
                        Supports public repositories from GitHub
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {activeTabVal === 'organization' && (
                <div className="max-w-lg mx-auto space-y-3">
                  {scmConfigurations.filter(config => config.scm_type === 'github').length > 0 ? (
                    scmConfigurations.map((config) => {
                      if (config.scm_type !== 'github') return null;
    
                      return (
                        <div
                          key={config.encrypted_scm_id}
                          className="group w-full p-4 text-left border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors flex justify-between items-center"
                        >
                          <div className="flex items-center space-x-3">
                            <Github className="w-6 h-6 text-gray-700" />
                            <div>
                              <div className='flex items-center space-x-2'>
                                <p className="font-weight-medium text-gray-900">
                                  {config.credentials.organization}
                                </p>
                                <span className="px-2 py-1 typography-caption font-weight-medium text-primary bg-primary-50 rounded-full">
                                  {config.scm_type.toUpperCase()}
                                </span>
                              </div>
                              <p className="typography-body-sm text-gray-500">
                                {config.api_url || 'Default API URL'}
                              </p>
                            </div>
                          </div>
                          <button
                            onClick={() => handleScanRepo(config.encrypted_scm_id, config.credentials.organization)}
                            className={`px-4 py-1.5 rounded-lg typography-body-sm font-weight-medium transition-all flex items-center space-x-1
                              ${scanningRepoId === config.encrypted_scm_id
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800'
                              }`}
                            disabled={scanningRepoId === config.encrypted_scm_id}
                          >
                            {scanningRepoId === config.encrypted_scm_id ? (
                              'Scanning...'
                            ) : (
                              <>
                                <RefreshCw className="w-4 h-4 mr-1" />
                                Scan Repo
                              </>
                            )}
                          </button>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full">
                        <Code className="w-6 h-6 text-gray-400" />
                      </div>
                      <h3 className="mb-2 typography-body-lg font-weight-medium text-gray-900">
                        No GitHub Organization Accounts
                      </h3>
                      <p className="typography-body-sm text-gray-500">
                        Contact your administrator to set up GitHub organization accounts.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            /* Original Form View */
            <form onSubmit={handleSubmit} className="p-6">
              {/* Project Title */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-black mb-2">
                  Project Title <span className="text-red-600">*</span>
                </label>
                <input
                  type="text"
                  name="projectTitle"
                  value={formData.projectTitle}
                  onChange={handleChange}
                  placeholder="Project Title"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-black placeholder-gray-400"
                  required
                />
              </div>

              {/* Project Overview */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-black mb-2">
                  Project Overview <span className="text-red-600">*</span>
                </label>
                <textarea
                  name="projectOverview"
                  value={formData.projectOverview}
                  onChange={handleChange}
                  placeholder="Enter Project Overview"
                  rows={3}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-black placeholder-gray-400 resize-none"
                />
              </div>

              {/* Repository Source */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-black mb-3">
                  Repository Source
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {repositorySources.map((source) => (
                    <button
                      key={source.id}
                      type="button"
                      onClick={() => handleRepositorySourceClick(source.id)}
                      disabled={source.disabled}
                      className={`px-5 py-4 rounded-lg border text-left transition-all ${
                        source.disabled
                          ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                          : formData.repositorySource === source.id
                          ? 'bg-[#f7f6ff] border-l-[3px] border-l-[#f26522] border-r border-r-[#f26522] border-t border-t-[#f26522] border-b border-b-[#f26522]'
                          : 'border border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50 cursor-pointer'
                      }`}
                    >
                      <div className={`flex items-center gap-2 text-[15px] font-semibold leading-snug mb-1 ${
                        source.disabled
                          ? 'text-gray-400'
                          : formData.repositorySource === source.id 
                          ? 'text-[#1a202c]' 
                          : 'text-black'
                      }`}>
                        {source.id === 'LocalUpload' && <Upload className="w-4 h-4" />}
                        {source.id === 'GitHub' && <Github className="w-4 h-4" />}
                        {source.label}
                      </div>
                      <div className={`text-[13px] leading-tight ${
                        source.disabled
                          ? 'text-gray-400'
                          : formData.repositorySource === source.id 
                          ? 'text-[#4a5568]' 
                          : 'text-gray-600'
                      }`}>
                        {source.description}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Show file details only when files are selected */}
              {formData.files.length > 0 && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-black">
                      Selected Files ({formData.files.length})
                    </label>
                    <button
                      type="button"
                      onClick={handleClearAllFiles}
                      className="text-sm text-red-600 hover:text-red-700 transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                  
                  <div className="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50">
                    {formData.files.map((selectedFile) => (
                      <div
                        key={selectedFile.id}
                        className="flex items-center justify-between bg-white p-3 rounded-md border border-gray-200"
                      >
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {/* Show different icons based on file type */}
                            {selectedFile.file.type.startsWith('image/') ? (
                              <img 
                                src={URL.createObjectURL(selectedFile.file)} 
                                alt={selectedFile.name}
                                className="w-8 h-8 object-cover rounded border flex-shrink-0"
                                onLoad={(e) => {
                                  // Clean up the object URL after the image loads
                                  setTimeout(() => URL.revokeObjectURL((e.target as HTMLImageElement).src), 1000);
                                }}
                              />
                            ) : (
                              <Upload className="w-4 h-4 text-gray-600 flex-shrink-0" />
                            )}
                            <span className="text-sm font-medium text-black truncate">
                              {selectedFile.name}
                            </span>
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800 flex-shrink-0">
                              {selectedFile.type}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600">
                            Size: <span className="font-medium">{selectedFile.size}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <CheckCircle2 className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <button
                            type="button"
                            onClick={() => handleFileDelete(selectedFile.id)}
                            className="text-red-500 hover:text-red-600 transition-colors p-1 flex-shrink-0"
                            title="Remove file"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Imported Repositories Section */}
              {importedRepositories.length > 0 && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-black mb-3">
                    Selected Repositories ({importedRepositories.length})
                  </label>
                  <div className="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50">
                    {importedRepositories.map((repo, index) => (
                      <div
                        key={`${repo.id}-${index}`}
                        className="flex items-center justify-between bg-white p-3 rounded-md border border-green-200 border-l-4 border-l-green-500"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Github className="w-4 h-4 text-gray-600" />
                            <span className="text-sm font-medium text-black">
                              {repo.full_name}
                            </span>
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              {repo.repo_type}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600">
                            Branch: <span className="font-medium">{repo.branch}</span>
                            {repo.description && (
                              <span className="ml-2">• {repo.description}</span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Selected: {new Date(repo.importedAt).toLocaleDateString()} at{' '}
                            {new Date(repo.importedAt).toLocaleTimeString()}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <CheckCircle2 className="w-5 h-5 text-green-500" />
                          {repo.html_url && (
                            <a
                              href={repo.html_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary-600 hover:text-primary-700"
                              title="View on GitHub"
                            >
                              <Link className="w-4 h-4" />
                            </a>
                          )}
                          <button
                            type="button"
                            onClick={() => handleRemoveImportedRepo(index)}
                            className="text-red-500 hover:text-red-600 transition-colors p-1"
                            title="Remove from selection"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Hidden file input with multiple attribute */}
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleChange}
                className="hidden"
                multiple
                accept="image/*,.pdf,.doc,.docx,.txt,.json,.md"
              />

              {/* Operation Type */}
              <div className="mb-8">
                <label className="block text-sm font-medium text-black mb-3">
                  Operation Type (Single Selection)
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {operationTypes.map((type) => {
                    const isSelected = formData.operationType.includes(type.id);
                    const isDisabled = type.disabled;

                    return (
                      <button
                        key={type.id}
                        type="button"
                        disabled={isDisabled}
                        onClick={() => {
                          if (!isDisabled) {
                            setFormData((prev) => ({
                              ...prev,
                              operationType: isSelected ? [] : [type.id],
                            }));
                          }
                        }}
                        className={`px-5 py-4 rounded-lg border text-left transition-all ${
                          isDisabled
                            ? 'bg-gray-100 text-gray-400 border border-gray-200 cursor-not-allowed'
                            : isSelected
                            ? 'bg-[#f7f6ff] border-l-[3px] border-l-[#f26522] border-r border-r-[#f26522] border-t border-t-[#f26522] border-b border-b-[#f26522]'
                            : 'border border-gray-200 hover:border-gray-300 bg-white'
                        }`}
                      >
                        <div
                          className={`text-[15px] font-semibold leading-snug mb-1 ${
                            isDisabled
                              ? 'text-gray-400'
                              : isSelected
                              ? 'text-[#1a202c]'
                              : 'text-black'
                          }`}
                        >
                          {type.label}
                        </div>
                        <div
                          className={`text-[13px] leading-tight ${
                            isDisabled
                              ? 'text-gray-400 italic'
                              : isSelected
                              ? 'text-[#4a5568]'
                              : 'text-gray-600'
                          }`}
                        >
                          {type.description}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end gap-3 mt-8">
                <button
                  type="button"
                  onClick={() => onClose()}
                  className="px-4 py-2 text-sm font-medium text-black bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!isFormValid() || isLoading}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary flex items-center gap-2 ${
                    isFormValid() && !isLoading
                      ? 'bg-primary-600 hover:bg-primary-700'
                      : 'bg-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
                  {isLoading ? 'Importing...' : 'Import Project'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImportProjectModal;
