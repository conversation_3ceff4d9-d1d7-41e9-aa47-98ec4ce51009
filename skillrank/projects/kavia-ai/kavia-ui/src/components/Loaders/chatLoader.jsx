import React from "react";

const ChatLoader = ({message}) => {
  return (
    <div className="relative flex flex-col space-y-6 p-4 w-full h-screen">
      {/* Background pattern for blur effect */}
      <div className="absolute top-0 left-0 right-0 bottom-0 opacity-30">
        <div className="w-full h-full bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100"></div>
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-50"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-40"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-pink-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-40 right-1/3 w-18 h-18 bg-indigo-200 rounded-full opacity-45"></div>
      </div>
      
      {/* Blur overlay with centered message */}
      <div
        className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center"
        style={{
          backgroundColor: "rgba(255, 255, 255, 0.1)",
          backdropFilter: "blur(20px)",
          zIndex: 10,
        }}
      >
        {message && (
          <p className="text-center text-gray-800 typography-body-lg font-weight-medium px-8 bg-white/20 rounded-lg p-6 backdrop-blur-sm">
            {message}
          </p>
        )}
      </div>
    </div>
  );
};

export default ChatLoader;