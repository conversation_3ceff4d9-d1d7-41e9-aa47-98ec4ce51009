import React, { useContext, useState, useEffect, useCallback, useRef } from "react";
import { useParams,usePathname, useRouter, useSearchParams } from 'next/navigation';
import { MoveLeft, Inbox, Clock, User, HelpCircle, Globe, Lock, FolderPlus } from "lucide-react";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { PanelContext } from "../Context/PanelContext";
import { TopBarContext } from '../Context/TopBarContext';
import ProjectAssets from "../Modal/DocumentProjectAssest";
import { getUserById } from "@/utils/api";
import { useProjectAsset } from "../Context/ProjectAssetContext";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import { DriverContext } from "../Context/DriverContext";
import { checkProjectVisibility, updateProjectVisibility } from "@/utils/projectApi";
import Cookies from 'js-cookie';
import { useUser } from "../Context/UserContext";
import { getUserPlan } from "@/utils/api";
import LockedTabIndicator from "../UIComponents/LockedTabIndicator";
import { ProjectSetupContext } from '../Context/ProjectSetupContext';
import ProjectCreationFlow from "../Modal/ProjectCreationFlow";
import { useToggleGraph } from '../Context/ToggleGraphContext';
interface ProjectProperties {
  Title?: string;
  Name?: string;
  updated_at?: string;
  created_at?: string;
  created_by?: string;
}
interface Project {
  properties?: ProjectProperties;
}
interface Tab {
  id: string;
  href: string;
  title: string;
  active: boolean;
}
interface Creator {
  Picture?: string;
  Name?: string;
  Email?: string;
}
interface HeaderProps {
  onCommentsIconClick: () => void;
  onPlayIconClick: () => void;
  showToggleButton?: boolean;
  isGraphView?: boolean;
  onToggleGraph?: () => void;
}

interface ProjectVisibility {
  project_id: number;
  tenant_id: string;
  is_public: boolean;
  visibility: string;
  public_info: any;
}

const Header: React.FC<HeaderProps> = ({
  onCommentsIconClick,
  onPlayIconClick
}) => {
  const { showToggleButton, isGraphView, toggleGraph } = useToggleGraph();
  const pathname = usePathname();
  const router = useRouter();
  const params = useParams();
  const { getProject } = useContext(PanelContext);
  const { updateTablinkTitle, tabs } = useContext(TopBarContext);
  const searchParams = useSearchParams();
  const [creator, setCreator] = useState<Creator | null>(null);
  const [creatorLoading, setCreatorLoading] = useState(false);
  const creatorCacheRef = useRef<Map<string, Creator>>(new Map());
  const currentCreatorRef = useRef<Creator | null>(null);
  const [title, setTitle] = useState<string>("");
  const [lastUpdated, setLastUpdated] = useState<string>("Never");
  const [isUpdatedTab, setIsUpdatedTab] = useState(false);
  const [showProjectAsset, setShowProjectAsset] = useState(false);
  const organizationId = pathname.split("/")[1];
  const type = pathname.split("/")[2];
  const id = pathname.split("/")[3];
  const [abortController, setAbortController] = useState<AbortController>(new AbortController());
  const { setActiveTab } = useProjectAsset();
  const {prepareDriver, handleDriveTour} = useContext(DriverContext);
  const {projectId} = useParams();
  const [projectVisibility, setProjectVisibility] = useState<ProjectVisibility | null>(null);
  const [updatingVisibility, setUpdatingVisibility] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const { tenant_id: userTenantId, is_admin, is_having_permission ,is_free_user} = useUser();
  const [tenantError, setTenantError] = useState(false);
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const visibilityFetchedRef = useRef<Set<string>>(new Set());

  const is_change_visibility_allowed = async () => {
    const tenant_id = Cookies.get("selected_tenant_id");

    if (userTenantId.startsWith("free") || userTenantId.startsWith("default")) {
      return userTenantId === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID;
    }

    if (tenant_id != userTenantId) {
      return false;
    }

    if (userTenantId == process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
      const user_plan = await getUserPlan();
      if (user_plan.price_id == "free_plan") {
        return false;
      } else {
        return true;
      }
    }

    return is_admin;
  }
  const { setOpenFlowModel,openFlowModel,setProjectId } = useContext(ProjectSetupContext);
  useEffect(() => {
      setProjectId(params.projectId)
    }, [params.projectId]);

  const runOverviewDriver = () => {
    prepareDriver("overview");
    handleDriveTour(0);
  }

  const handleVisibilityClick = async () => {
    if (!projectVisibility || updatingVisibility) return;

    try {
      const canChangeVisibility = await is_change_visibility_allowed();

      if (!canChangeVisibility) {
        let errorMessage = "You don't have permission to change project visibility.";

        const tenant_id = Cookies.get("selected_tenant_id");
        if (tenant_id !== userTenantId) {
          errorMessage = "Tenant mismatch: Cannot update visibility when selected tenant doesn't match user tenant";
          setTenantError(true);
        } else {
          setPermissionError(errorMessage);
        }

        console.error(errorMessage);
        return;
      }

      setTenantError(false);
      setPermissionError(null);
      setShowConfirmModal(true);
    } catch (error) {
      console.error("Error checking visibility permissions:", error);
      setPermissionError("An error occurred while checking permissions");
    }
  }

  const toggleVisibility = async () => {
    if (!projectVisibility || updatingVisibility) return;

    try {
      setUpdatingVisibility(true);
      setShowConfirmModal(false);
      // Call API to toggle visibility - pass the opposite of current state
      const makePublic = !projectVisibility.is_public;


      // Update visibility
      await updateProjectVisibility(Number(id), makePublic);

      // Refresh visibility data
      const updatedVisibility = await checkProjectVisibility(Number(id)) as ProjectVisibility;
      setProjectVisibility(updatedVisibility);


    } catch (error) {
      console.error("Error toggling project visibility:", error);
      setPermissionError("Failed to update project visibility");
    } finally {
      setUpdatingVisibility(false);
    }
  }

  const convertUTCToString = (dateString: string) => {
    if (!dateString) { return "Never"}
    const date = new Date(dateString.split(".")[0] + "Z");
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    const formattedDate = date.toLocaleString('en-US', options);
    return formattedDate;
  }

  const currentRequestRef = useRef<string>('');

const getProjectDetails = useCallback(async () => {
  if (!["project", "task", "team", "chat", "settings"].includes(type)) return;

  if (type === "project" && id && !isNaN(Number(id))) {
    const requestId = `${type}-${id}-${Date.now()}`;
    currentRequestRef.current = requestId;
    
    // Don't clear creator data immediately to prevent blinking
    // setCreator(null);
    // setCreatorLoading(false);

    try {
      const project = await getProject(Number(id)) as Project;
      
      // Check if this is still the current request
      if (currentRequestRef.current !== requestId) return;
      
      setTitle(project.properties?.Title || project.properties?.Name || "");
      setLastUpdated(convertUTCToString(project.properties?.updated_at || project.properties?.created_at || ""));
      
      if (project.properties?.created_by) {
        const createdById = project.properties.created_by;

        // Check if we have cached creator data
        if (creatorCacheRef.current.has(createdById)) {
          const cachedCreator = creatorCacheRef.current.get(createdById);
          if (currentRequestRef.current === requestId) {
            setCreator(cachedCreator || null);
            currentCreatorRef.current = cachedCreator || null;
            setCreatorLoading(false);
          }
        } else {
          // Fetch creator data in background without showing loading state
          try {
            const userDetails = await getUserById(createdById) as Creator;

            // Cache the result
            creatorCacheRef.current.set(createdById, userDetails);

            // Only update if still the current request
            if (currentRequestRef.current === requestId) {
              setCreator(userDetails);
              currentCreatorRef.current = userDetails;
              setCreatorLoading(false);
            }
          } catch (error) {
            console.error('Error fetching creator details:', error);
            if (currentRequestRef.current === requestId) {
              setCreator(null);
              currentCreatorRef.current = null;
              setCreatorLoading(false);
            }
          }
        }
      } else {
        // Clear creator data only if no created_by field exists
        if (currentRequestRef.current === requestId) {
          setCreator(null);
          currentCreatorRef.current = null;
          setCreatorLoading(false);
        }
      }
    } catch (error) {
      if (currentRequestRef.current === requestId) {
        console.error('Error fetching project details:', error);
        setCreator(null);
        currentCreatorRef.current = null;
        setCreatorLoading(false);
      }
    }
  } else {
    // Only clear creator if switching to a non-project type
    if (type !== "project") {
      setCreator(null);
      currentCreatorRef.current = null;
      setCreatorLoading(false);
    }
    setTitle(type.charAt(0).toUpperCase() + type.slice(1));
  }
}, [type, id, getProject]);

  useEffect(() => {
    getProjectDetails();
  }, [getProjectDetails]);

  // Separate effect for project visibility - only runs once per project
  useEffect(() => {
    const fetchProjectVisibility = async () => {
      if (type === "project" && id && !visibilityFetchedRef.current.has(id)) {
        try {
          visibilityFetchedRef.current.add(id);
          const visibilityInfo = await checkProjectVisibility(Number(id)) as ProjectVisibility;
          setProjectVisibility(visibilityInfo);
        } catch (error) {
          console.error('Error fetching project visibility:', error);
          // Remove from fetched set if error occurred to allow retry
          visibilityFetchedRef.current.delete(id);
        }
      }
    };

    fetchProjectVisibility();
  }, [type, id]);


  useEffect(() => {
    setAbortController(new AbortController());
    // Clear project visibility when switching projects
    if (type === "project") {
      setProjectVisibility(null);
    }
  }, [id, type])

  useEffect(() => {
    if (!isUpdatedTab && type === "project") {
      (async () => {
        const project = await getProject(Number(id)) as Project;
        if (project) {
          const existingTab = tabs.find((tab: Tab) =>
            tab.href.includes(`/${organizationId}/${type}/${id}`)
          );
          if (existingTab) {
            updateTablinkTitle(id, project.properties?.Title || project.properties?.Name || "");
          }
        }
        setIsUpdatedTab(true);
      })();
    }
  }, [id, type, isUpdatedTab, getProject, updateTablinkTitle, tabs]);

  useEffect(() => {
    if (searchParams.get('refresh')) {
      const timer = setTimeout(() => {
        const newSearchParams = new URLSearchParams(window.location.search);
        newSearchParams.delete('refresh');
        const newUrl = `${window.location.pathname}${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ''}`;
        window.location.replace(newUrl);
      }, 200);
      return () => clearTimeout(timer);
    }
    if (searchParams.get('ProjectAsset')) {
      setShowProjectAsset(true);
      const activeTab = searchParams.get('ProjectAsset') || 'code';
      setActiveTab(activeTab);
      const url = new URL(window.location.href);
      url.searchParams.delete('ProjectAsset');
      window.history.pushState({}, '', url);
    }
  }, [searchParams]);


  const handleOpenAsset = () => {
    setShowProjectAsset(true);
  }

  const handleCloseAsset = () => {
    setShowProjectAsset(false);
  }

  const handleOpenSetupModel = () :any=>{
    setOpenFlowModel(true)
  }


  useEffect(() => {
    if (searchParams.get('openAssets') === 'true') {
      setShowProjectAsset(true);

      // Clean up the URL
      const url = new URL(window.location.href);
      url.searchParams.delete('openAssets');
      window.history.pushState({}, '', url);
    }
  }, [searchParams]);

  // Cleanup cache on unmount
  useEffect(() => {
    return () => {
      creatorCacheRef.current.clear();
    };
  }, []);


  return (
    <>
      <header className="flex items-center justify-between border-b shadow-sm content-right-padding py-2">
        {/* Left Section */}
        <div className="flex items-center gap-2 overflow-hidden">
          <IconButton
            icon={<MoveLeft className="h-5 w-5 text-gray-600" />}
            tooltip="Go back"
            onClick={() => router.back()}
            className="hover:bg-gray-100 rounded-full transition-colors"
          />

        <div className="flex-1 overflow-hidden">
          {/* Title */}
          <h1 className="typography-body font-weight-semibold text-gray-800 truncate flex items-center">
            {title}
            {type === "project" && projectVisibility && (
              <BootstrapTooltip title={`Click to change to ${projectVisibility.is_public ? 'Private' : 'Public'}`} placement="right">
                <button
                  onClick={handleVisibilityClick}
                  disabled={updatingVisibility}
                  className={`ml-2 typography-caption px-2 py-0.5 rounded-full
                    ${projectVisibility.is_public ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}
                    flex items-center gap-1 transition-colors cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed
                    focus:outline-none focus:ring-2 focus:ring-offset-1 ${projectVisibility.is_public ? 'focus:ring-green-500' : 'focus:ring-gray-500'}`}
                >
                  {updatingVisibility ? (
                    <>
                      <span className="animate-pulse">Updating...</span>
                    </>
                  ) : (
                    <>
                      {projectVisibility.visibility === 'public'
                        ? <><Globe className="h-3 w-3" /> Public</>
                        : <><Lock className="h-3 w-3" /> Private</>
                      }
                    </>
                  )}
                </button>
              </BootstrapTooltip>
            )}
          </h1>

            {/* Metadata */}
            <div className="flex flex-wrap gap-5 typography-caption mt-0.5">
              <div className="flex items-center">
                <Clock className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                <span className="text-gray-500">Updated</span>
                <span className="ml-1.5 text-gray-700 font-weight-medium truncate">
                  {lastUpdated}
                </span>
              </div>

              {creator && type === "project" && (
                <div className="flex items-center">
                  <User className="h-3.5 w-3.5 mr-1.5 text-gray-400" />
                  <span className="text-gray-500">Created by</span>
                  <span className="ml-1.5 text-gray-700 font-weight-medium truncate max-w-[120px] md:max-w-[180px]">
                    {creator.Name || creator.Email}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2 content-right-padding" >
          {/* <UserIcons /> TODO: Uncomment later*/}

          {showToggleButton && (
            <BootstrapTooltip placement="bottom">
              <button
                onClick={toggleGraph}
                aria-label="Toggle Graph View"
                className="flex items-center rounded-sm border border-primary-500"
              >
                <div className="flex items-center">
                  <div
                    className={`p-1 ${!isGraphView ? "bg-primary-100 text-primary-600" : ""}`}
                  >
                    <BootstrapTooltip title="Project" placement="bottom">
                      <div className={`${isGraphView ? "opacity-50" : "opacity-100"}`}>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M3 5C3 3.89543 3.89543 3 5 3H19C20.1046 3 21 3.89543 21 5V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5Z" stroke="hsl(var(--primary-600))" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M3 9H21" stroke="hsl(var(--primary-600))" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M9 21V9" stroke="hsl(var(--primary-600))" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    </BootstrapTooltip>
                  </div>
                  <div
                    className={`p-1 ${isGraphView ? "bg-primary-100 text-primary-600" : ""}`}
                  >
                    <BootstrapTooltip title="Graph" placement="bottom">
                      <div className={`${isGraphView ? "opacity-100" : "opacity-50"}`}>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="5" cy="12" r="2" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                          <circle cx="19" cy="12" r="2" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                          <circle cx="12" cy="19" r="2" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                          <circle cx="12" cy="5" r="2" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                          <path d="M7 12H17" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                          <path d="M12 7V17" stroke="hsl(var(--primary-600))" strokeWidth="2"/>
                        </svg>
                      </div>
                    </BootstrapTooltip>
                  </div>
                </div>
              </button>
            </BootstrapTooltip>
          )}

          <BootstrapTooltip title={!is_having_permission() ? "You don't have permission" : "Project Assets"} placement="bottom">
            <span>
              <button
                id="projectAssetButton"
                onClick={handleOpenAsset}
                disabled={!is_having_permission()}
                className={`inline-flex items-center justify-center gap-2 h-8 px-2
                  typography-body-sm font-weight-medium bg-white rounded-md
                  border border-gray-300 transition-colors duration-150
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1
                  ${is_having_permission()
                    ? 'text-gray-700 hover:bg-gray-50 hover:border-gray-400 active:bg-gray-100'
                    : 'text-gray-400 cursor-not-allowed'}`}
              >
                <Inbox className="w-4 h-4" />
                <span className="hidden md:inline">Project Assets</span>
              </button>
            </span>
          </BootstrapTooltip>

          <div className="flex items-center">
            <BootstrapTooltip
              title={
                !is_having_permission()
                  ? "You don't have permission"
                  : is_free_user 
                  ? "Premium feature - Upgrade to access"
                  : "Project Guidance"
              }
              placement="bottom"
            >
              <span>
                <button
                  onClick={handleOpenSetupModel}
                  disabled={!is_having_permission() || (is_free_user)}
                  className={`inline-flex items-center justify-center gap-2 h-8 px-2
                  typography-body-sm font-weight-medium bg-white rounded-md
                  border border-gray-300 transition-colors duration-150
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1
                  ${
                    is_having_permission() && !(is_free_user )
                      ? "text-gray-700 hover:bg-gray-50 hover:border-gray-400 active:bg-gray-100"
                      : "text-gray-400 cursor-not-allowed"
                  }`}
                >
                  <FolderPlus className="w-4 h-4" />
                  <span className="hidden md:inline">Project Guidance</span>
                </button>
              </span>
            </BootstrapTooltip>
            {(is_free_user )  && <div className="ml-1"><LockedTabIndicator /></div>}
          </div>
          {/* Help Button */}
          {/* <BootstrapTooltip title="Get Help" placement="bottom-end">
            <button
              onClick={runOverviewDriver}
              className="inline-flex items-center justify-center h-8 w-8
                typography-body-sm font-weight-medium text-gray-700 bg-white rounded-md
                border border-gray-300 hover:bg-gray-50 hover:border-gray-400
                active:bg-gray-100 transition-colors duration-150
                focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1"
            >
              <HelpCircle className="w-4 h-4 text-gray-600" />
            </button>
          </BootstrapTooltip> */}

          {/* Project Assets Modal */}
          {showProjectAsset && (
            <ProjectAssets
              onClose={() => setShowProjectAsset(false)}
              handleCloseAsset={handleCloseAsset}
            />
          )}
        </div>
      </header>

      {/* Modals - moved outside header element */}
      {/* Visibility Confirmation Modal */}
      {showConfirmModal && projectVisibility && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-[1000] flex items-center justify-center w-screen h-screen">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-4">
              Change Project Visibility
            </h3>
            <p className="typography-body-sm text-gray-600 mb-6">
              Are you sure you want to change this project from <span className="font-weight-semibold">{projectVisibility.is_public ? 'Public' : 'Private'}</span> to <span className="font-weight-semibold">{projectVisibility.is_public ? 'Private' : 'Public'}</span>?
              {projectVisibility.is_public
                ? " Making it private will restrict access to only authorized team members."
                : " Making it public will allow anyone with the link to access this project."}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={toggleVisibility}
                className={`px-4 py-2 typography-body-sm font-weight-medium text-white rounded-md ${
                  projectVisibility.is_public
                    ? 'bg-gray-600 hover:bg-gray-700'
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {projectVisibility.is_public ? 'Make Private' : 'Make Public'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tenant Mismatch Error Modal */}
      {tenantError && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-[1000] flex items-center justify-center w-screen h-screen">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <h3 className="typography-body-lg font-weight-medium text-red-600 mb-4">
              Permission Error
            </h3>
            <p className="typography-body-sm text-gray-600 mb-6">
              You cannot change the project visibility because your currently selected tenant doesn't match your user account tenant.
              <br /><br />
              Please ensure you're working in the correct tenant before attempting to change visibility settings.
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => setTenantError(false)}
                className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Generic Permission Error Modal */}
      {permissionError && !tenantError && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-[1000] flex items-center justify-center w-screen h-screen">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <h3 className="typography-body-lg font-weight-medium text-red-600 mb-4">
              Permission Error
            </h3>
            <p className="typography-body-sm text-gray-600 mb-6">
              {permissionError}
              <br /><br />
              You may not have sufficient permissions to change project visibility. This could be due to:
              <ul className="list-disc ml-5 mt-2">
                <li>You're not an admin for this workspace</li>
                <li>You're on a free plan that doesn't support this feature</li>
                <li>The project has restricted permissions</li>
              </ul>
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => setPermissionError(null)}
                className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      {openFlowModel ? (
        <ProjectCreationFlow onClose={()=>setOpenFlowModel(false)} />
        // <></>
        ):""}
    </>
  );
};

export default Header;
