"use client";
import React, { useState, useEffect, useContext } from "react";
import {
  createNewNode,
  updateNodeProperties,
  createProjectGuidanceFlow,
} from "../../utils/api";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { SideBarContext } from "../Context/SideBarContext";

import { X } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ProjectCreationFlow from "./ProjectCreationFlow";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import { useRouter } from "next/navigation";
import { useUser } from "../Context/UserContext";
import LockedTabIndicator from "../UIComponents/LockedTabIndicator";
import Cookies from "js-cookie";
import { LLMModel } from "@/constants/llmModels";
import LlmModelSelect from "../UIComponents/LlmModelSelect";
// Type definitions
interface Fields {
  Title: string;
  Description: string;
  llmModel: string;
  [key: string]: string;
}

interface TouchedFields {
  Title: boolean;
  Description: boolean;
  [key: string]: boolean;
}

interface InitialValues {
  id?: string;
  type?: string;
  [key: string]: any;
}

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: string;
  isEdit?: boolean;
  initialValues?: InitialValues;
  onUpdateResponse?: (response: any) => void;
  handleCloseProjectModel? :() => void
}

interface AlertContextType {
  alerts: never[];
  showAlert: (message: any, type: any) => void;
  closeAlert: () => void;
}

interface SideBarContextType {
  updateCurrentProject: (project: any) => void;
}

const omitFields: string[] = [
  "id",
  "type",
  "created_by",
  "Created_by",
  "Configuration_state",
  "Created_at",
  "Type",
  "configuration_state",
  "Updated_at",
  "updated_at",
  "name",
  "created_at",
  "updated_by",
  "Updated_by",
  "embedding",
  "Cloneurlhttp",
  "Cloneurlssh",
  "Current_task_id",
  "Repositoryid",
  "Repositoryname",
  "Repositorystatus",
];

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onClose,
  type,
  isEdit = false,
  initialValues = {},
  onUpdateResponse,
  handleCloseProjectModel
}) => {
  const [fields, setFields] = useState<Fields>({
    Title: "",
    Description: "",
    llmModel: LLMModel.gpt_4_1_mini,
  });
  const {
    projectSetupOpen,
    setProjectSetupOpen,
    projectId,
    setProjectId,
    openFlowModel,
    setOpenFlowModel,
  } = useContext(ProjectSetupContext);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isChanged, setIsChanged] = useState<boolean>(false);
  const [touched, setTouched] = useState<TouchedFields>({
    Title: false,
    Description: false,
  });
  const [hasExistingDocuments, setHasExistingDocuments] =
    useState<boolean>(false);
  const [setupMethod, setSetupMethod] = useState<string>("assisted");
  const llmModelOptions = [
    {
      label: "GPT-4.1",
      value: LLMModel.gpt_4_1,
      tag: ["Latest", "Most Capable"],
      tagColor: ["indigo", "purple"],
    },
    {
      label: "GPT-4.1 Mini",
      value: LLMModel.gpt_4_1_mini,
      tag: ["Fast", "Most Cost Effective"],
      tagColor: ["blue", "green"],
    },
    {
      label: "Claude 3.5 Sonnet",
      value: LLMModel.claude_3_5_sonnet,
      tag: ["Great for summarization"],
      tagColor: ["purple"],
    },
    {
      label: "Claude 3.7 Sonnet",
      value: LLMModel.claude_3_7_sonnet,
      tag: ["Fast"],
      tagColor: ["blue"],
    },
    {
      label: "Gemini 2.5 Pro",
      value: LLMModel.gemini_2_5_pro,
      tag: ["Fast"],
      tagColor: ["blue"],
    },
  ];

  const { showAlert } = useContext<AlertContextType>(AlertContext);
  const { updateCurrentProject } = useContext(
    SideBarContext as React.Context<SideBarContextType>
  );
  const router = useRouter();
  const { is_free_user } = useUser();

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    if (!isEdit && !is_free_user) {
      setFields({
        Title: "",
        Description: "",
        llmModel: LLMModel.gpt_4_1_mini,
      });
      setTouched({ Title: false, Description: false });
      setIsChanged(false);
      setHasExistingDocuments(false);
      setSetupMethod("assisted");
      return;
    }

    if (!isEdit && is_free_user) {
      setFields({
        Title: "",
        Description: "",
        llmModel: LLMModel.gpt_4_1_mini,
      });
      setTouched({ Title: false, Description: false });
      setIsChanged(false);
      setHasExistingDocuments(false);
      setSetupMethod("manual");
      return;
    }

    // For edit mode, create a stable initialization function
    const initializeFields = (): Fields => {
      const initialFields: Fields = {
        Title: "",
        Description: "",
        llmModel: LLMModel.gpt_4_1_mini,
      };

      Object.entries(initialValues).forEach(([key, value]) => {
        const lowerCaseKey = key.toLowerCase();
        if (!omitFields.includes(lowerCaseKey)) {
          const capitalizedKey = capitalizeFirstLetter(lowerCaseKey);
          initialFields[capitalizedKey] = value || "";
        }
      });

      return initialFields;
    };

    const initialFields = initializeFields();
    setFields(initialFields);

    const touchedFields = Object.keys(initialFields).reduce<TouchedFields>(
      (acc, key) => ({
        ...acc,
        [key]: true,
      }),
      {} as TouchedFields
    );

    setTouched(touchedFields);
    setIsChanged(false);
  }, [isOpen, isEdit]);

  const capitalizeFirstLetter = (string: string): string => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFields((prevFields) => {
      const newFields = { ...prevFields, [name]: value };

      if (isEdit) {
        const initialValue =
          initialValues[name.toLowerCase()] || initialValues[name] || "";
        const hasChanged =
          value !== initialValue ||
          Object.keys(newFields).some(
            (key) =>
              newFields[key] !==
              (initialValues[key.toLowerCase()] || initialValues[key] || "")
          );
        setIsChanged(hasChanged);
      } else {
        setIsChanged(true);
      }

      return newFields;
    });
  };

  const handleBlur = (fieldName: string): void => {
    setTouched((prev) => ({ ...prev, [fieldName]: true }));
  };

  const isFormValid = (): boolean => {
    return fields.Title.trim() !== "" && fields.Description.trim() !== "";
  };
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setTouched({ Title: true, Description: true });

    if (!isFormValid()) {
      showAlert("Title and Description are required.", "danger");
      return;
    }

    if (!isChanged && isEdit) {
      showAlert("No changes detected.", "warning");
      return;
    }

    setIsLoading(true);
    try {
      let response;
      if (isEdit) {
        const capitalizedFields = Object.fromEntries(
          Object.entries(fields).map(([key, value]) => [
            capitalizeFirstLetter(key),
            value,
          ])
        );
        response = await updateNodeProperties(
          initialValues.id,
          type,
          capitalizedFields
        );
      } else {
        const properties = {
          has_existing_documents: hasExistingDocuments,
          setup_method: setupMethod,
        };
        response = await createNewNode(
          type,
          fields.Title.trim(),
          fields.Description.trim(),
          fields.llmModel,
          properties
        );
      }
      if (response && response.id) {
        const { navigateToProject } = await import("@/utils/navigationHelpers");
        navigateToProject(router, response.id, "overview");
      }
      if (hasExistingDocuments) {
        localStorage.setItem(`hasExistingDocuments-${response.id}`, "true");
      }

      if (response != null) {
        if (!isEdit && response.id) {
          // Get existing project IDs from localStorage (or initialize as empty array)
          const existingIds = JSON.parse(
            localStorage.getItem("createdProjectIds") || "[]"
          );
          setProjectId(response.id);
          setProjectSetupOpen(true);

          // Add the new ID if it's not already in the list (optional check)
          if (!existingIds.includes(response.id)) {
            existingIds.push(response.id);
            localStorage.setItem(
              "createdProjectIds",
              JSON.stringify(existingIds)
            );
          }

          try {
            const projectData = {
              project_id:
                typeof response.id === "string"
                  ? parseInt(response.id, 10)
                  : response.id,
              step_name: "project_creation", // Hardcoded step_name
              status: "completed",
              data: {
                project_details: {
                  id:
                    typeof response.id === "string"
                      ? parseInt(response.id, 10)
                      : response.id,
                  type: type,
                  name: fields.Title.trim(),
                  description: fields.Description.trim(),
                },
              },
            };

            // Call the API with properly formatted data
            await createProjectGuidanceFlow(response.id, projectData);
          } catch (flowError) {}
        }

        if (isEdit) {
          if (onUpdateResponse) {
            onUpdateResponse(response);
          }
          onClose();
        }

        if (!isEdit) {
          if (setupMethod === "assisted") {
            Cookies.set("is_public_selected", "false");
            setOpenFlowModel(true);
            onClose();
          } else {
            if (onUpdateResponse) {
              onUpdateResponse(response);
            }
          }
        }
      } else {
        showAlert("An unexpected error occurred", "danger");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      showAlert(
        `Failed to ${
          isEdit ? "update" : "create"
        } the ${type.toLowerCase()}: ${errorMessage}`,
        "danger"
      );
    } finally {
      setIsLoading(false);
    }
  };
  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div
          className="fixed inset-0 bg-semantic-gray-900/50 dark:bg-background/80 backdrop-blur-sm dark:backdrop-blur-md"
          onClick={handleCloseProjectModel}
        />
        <div className="relative w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden bg-white dark:bg-card shadow-xl rounded-lg border border-gray-200 dark:border-border">
          <div className="border-b p-4 flex items-center justify-between bg-white dark:bg-card border-gray-200 dark:border-border">
            <h2 className="typography-heading-4 font-weight-semibold text-gray-900 dark:text-custom-text-primary">
              {isEdit ? `Edit ${type}` : "Create New Project"}
            </h2>
            <DynamicButton
              variant="ghost"
              size="default"
              icon={X}
              onClick={handleCloseProjectModel}
              tooltip="Close"
            />
          </div>

          <div className="p-6 flex-1 flex flex-col overflow-hidden bg-white dark:bg-card">
            <form onSubmit={handleSubmit} className="flex flex-col h-full">
              <div className="space-y-6 flex-1">
                <div>
                  <div className="space-y-4">
                    <div>
                      <label className="block typography-body-sm font-weight-medium text-gray-700 dark:text-custom-text-primary mb-2">
                        <h4 className="typography-body font-weight-medium mb-1 text-gray-700 dark:text-custom-text-primary">
                          {" "}
                          Project Title <span className="text-red-600">*</span>
                        </h4>
                      </label>
                      <input
                        type="text"
                        name="Title"
                        value={fields.Title}
                        onChange={handleChange}
                        onBlur={() => handleBlur("Title")}
                        placeholder="Enter project title"
                        className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-custom-bg-secondary text-gray-900 dark:text-custom-text-primary placeholder-gray-500 dark:placeholder-custom-text-secondary ${
                          touched.Title && !fields.Title?.trim()
                            ? "border-red-500"
                            : "border-gray-300 dark:border-custom-border focus:border-primary focus:ring-1 focus:ring-primary"
                        }`}
                      />
                    </div>

                    <div>
                      <label className="block typography-body-sm font-weight-medium text-gray-700 dark:text-custom-text-primary mb-1">
                        <h4 className="typography-body font-weight-medium mb-1 text-gray-700 dark:text-custom-text-primary">
                          {" "}
                          Project Description{" "}
                          <span className="text-red-600">*</span>
                        </h4>
                      </label>
                      <textarea
                        name="Description"
                        value={fields.Description}
                        onChange={handleChange}
                        onBlur={() => handleBlur("Description")}
                        placeholder="Briefly describe what your project aims to accomplish"
                        className={`w-full min-h-[40px] max-h-[80px] px-3 py-2 border rounded-lg bg-white dark:bg-custom-bg-secondary text-gray-900 dark:text-custom-text-primary placeholder-gray-500 dark:placeholder-custom-text-secondary ${
                          touched.Description && !fields.Description?.trim()
                            ? "border-red-500"
                            : "border-gray-300 dark:border-custom-border focus:border-primary focus:ring-1 focus:ring-primary"
                        }`}
                        rows={4}
                      />
                    </div>

                    {/* LLM Model Select */}
                    <LlmModelSelect
                      llmModel={fields.llmModel}
                      setLlmModel={(value: string) => {
                        setFields((prev) => {
                          const newFields = { ...prev, llmModel: value };
                          return newFields;
                        });
                      }}
                      llmModelOptions={llmModelOptions}
                      isError={touched.Title && !fields.Title?.trim()}
                    />
                  </div>
                </div>

                {!isEdit && (
                  <div>
                    <h4 className="typography-body font-weight-medium -mt-3 mb-1 text-gray-700 dark:text-custom-text-primary">
                      {" "}
                      Setup Method <span className="text-red-600">*</span>
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`border rounded-lg p-4 bg-gray-50 dark:bg-custom-bg-secondary ${
                          setupMethod === "assisted"
                            ? "border-primary ring-2 ring-primary/30"
                            : "border-gray-300 dark:border-custom-border hover:border-primary/50"
                        } ${
                          is_free_user
                            ? "opacity-75 cursor-not-allowed"
                            : "cursor-pointer"
                        }`}
                        onClick={() => {
                          if (!is_free_user) {
                            setSetupMethod("assisted");
                          }
                        }}
                        title={
                          is_free_user
                            ? "Premium feature - Upgrade to access"
                            : ""
                        }
                      >
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="bg-primary-100 dark:bg-primary/10 px-1.5 py-1 rounded-md">
                            <Image
                              src={Logo}
                              alt="Logo"
                              width="16"
                              height="16"
                              className="text-primary dark:text-primary"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <h4 className="typography-body font-weight-medium mb-1 text-gray-700 dark:text-custom-text-primary">
                              Assisted Setup
                            </h4>
                            {is_free_user && (
                              <div>
                                <LockedTabIndicator />
                              </div>
                            )}
                          </div>
                        </div>

                        <p className="typography-body-sm text-gray-600 dark:text-custom-text-secondary">
                          Let our LLM guide you through the setup process with
                          interactive assistance.
                        </p>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer bg-gray-50 dark:bg-custom-bg-secondary ${
                          setupMethod === "manual"
                            ? "border-primary ring-2 ring-primary/30"
                            : "border-gray-300 dark:border-custom-border hover:border-primary/50"
                        }`}
                        onClick={() => setSetupMethod("manual")}
                      >
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="bg-primary-100 dark:bg-primary/10 px-1.5 py-1 rounded-md">
                            <Image
                              src={Logo}
                              alt="Logo"
                              width="16"
                              height="16"
                              className="text-primary dark:text-primary"
                            />
                          </div>
                          <h4 className="typography-body font-weight-medium mb-1 text-gray-700 dark:text-custom-text-primary">
                            Manual Setup
                          </h4>
                        </div>

                        <p className="typography-body-sm text-gray-600 dark:text-custom-text-secondary">
                          Configure your project manually with complete control
                          over settings.
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="flex items-center space-x-2 cursor-pointer ml-2">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 dark:border-custom-border text-primary focus:ring-primary cursor-pointer"
                          checked={hasExistingDocuments}
                          onChange={() =>
                            setHasExistingDocuments(!hasExistingDocuments)
                          }
                        />
                        <div className="mb-1">
                          <span className="typography-body-sm mt-2.5 text-gray-700 dark:text-custom-text-primary">
                            I have existing documents to upload for this project
                          </span>
                        </div>
                      </label>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-custom-border mt-4">
                <button
                  type="submit"
                  disabled={
                    isLoading || !isFormValid() || (!isChanged && isEdit)
                  }
                  className={`px-6 py-2.5 bg-primary text-primary-foreground rounded-md hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                    isLoading || !isFormValid() || (!isChanged && isEdit)
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  title={
                    !isFormValid()
                      ? "Please enter the Title and Description of the project"
                      : "Click to create the project"
                  }
                >
                  {isLoading ? "Processing..." : "Continue"}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      {openFlowModel ? (
        <ProjectCreationFlow onClose={() => setOpenFlowModel(false)} />
      ) : (
        // <></>
        ""
      )}
    </>
  );
};

export default CreateProjectModal;
