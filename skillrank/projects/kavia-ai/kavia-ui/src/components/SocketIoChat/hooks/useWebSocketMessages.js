import { useEffect, useRef, useCallback } from 'react';

export const useWebSocketMessages = ({
  wsConnection,
  handleInitialMessages,
  safeAddOrUpdateMessageInState,
  autoScroll,
  isAtBottom,
  scrollToBottom,
  isManualScrollingStillActive,
  setIsAiTyping,
  setIsReady,
  updateFileOperations,
  setMessages,
  messages,
  fileOperationsMap,
  setFileOperationsMap,
  openAccordions,
  setOpenAccordions,
  pendingFileOperations,
  handleRollbackStatusUpdate,
  processMessageFileUpdates,
  isLoadingHistoricalMessages,
  historicalMessagesLoadedRef,
  fetchInitialMessages,
  taskId
}) => {
  // Refs for periodic fetch mechanism
  const periodicFetchIntervalRef = useRef(null);
  const hasLlmMessagesRef = useRef(false);

  // Function to start periodic fetch when no LLM messages are received
  const startPeriodicFetch = useCallback(() => {
    // Clear any existing interval
    if (periodicFetchIntervalRef.current) {
      clearInterval(periodicFetchIntervalRef.current);
    }

    // Only start if we have the required dependencies and no LLM messages yet
    if (!fetchInitialMessages || !taskId || hasLlmMessagesRef.current) {
      return;
    }

    console.log('Starting periodic fetch for messages - WebSocket may not be receiving data properly');
    
    periodicFetchIntervalRef.current = setInterval(() => {
      console.log('Periodic fetch interval triggered. hasLlmMessagesRef.current =', hasLlmMessagesRef.current);

      // Stop fetching if LLM messages have been received
      if (hasLlmMessagesRef.current) {
        console.log('LLM messages already received, stopping periodic fetch from within interval');
        if (periodicFetchIntervalRef.current) {
          clearInterval(periodicFetchIntervalRef.current);
          periodicFetchIntervalRef.current = null;
        }
        return;
      }

      console.log('Periodic fetch: calling fetchInitialMessages until LLM response received');
      fetchInitialMessages(taskId);
    }, 10000); // 5 seconds interval
  }, [fetchInitialMessages, taskId]);

  // Function to stop periodic fetch when LLM messages are received
  const stopPeriodicFetch = useCallback(() => {
    console.log('stopPeriodicFetch called - setting hasLlmMessagesRef to true');
    console.log('Before: hasLlmMessagesRef.current =', hasLlmMessagesRef.current);
    console.log('Before: periodicFetchIntervalRef.current =', !!periodicFetchIntervalRef.current);

    hasLlmMessagesRef.current = true;
    if (periodicFetchIntervalRef.current) {
      console.log('Stopping periodic fetch - LLM response received');
      clearInterval(periodicFetchIntervalRef.current);
      periodicFetchIntervalRef.current = null;
      console.log('Periodic fetch interval cleared');
    } else {
      console.log('No periodic fetch interval to clear');
    }

    console.log('After: hasLlmMessagesRef.current =', hasLlmMessagesRef.current);
    console.log('After: periodicFetchIntervalRef.current =', !!periodicFetchIntervalRef.current);
  }, []);

  useEffect(() => {
    if (!wsConnection) return;

    // Add connection error handling
    const handleConnectionError = (error) => {
      setIsAiTyping(false);
      // Show error message to user
      if (setMessages && typeof setMessages === 'function') {
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          const errorMessage = "Connection error. Please refresh the page and try again.";

          if (lastMessage && lastMessage.content === errorMessage) {
            return prev;
          }

          return [
            ...prev,
            {
              id: Date.now(),
              content: errorMessage,
              msg_type: "error",
              timestamp: new Date().toISOString()
            }
          ];
        });
      }
    };

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Debug logging for agent_message type
        if (data.type === 'agent_message') {
          
        }

        // Priority handling: check if we should queue this message
        const isSocketMessage = data.type !== 'initial_messages' && data.type !== 'ready_check';
        const isUserMessage = data.data?.msg_type === 'user' || data.type === 'message_received' || data.type === 'input_received';
        const shouldQueue = (isLoadingHistoricalMessages || (!historicalMessagesLoadedRef.current && isSocketMessage)) && !isUserMessage;

        // Auto-scroll for important message events
        const shouldAutoScroll = ['message_received', 'message_chunk', 'message_resolved', 'agent_message'].includes(data.type);

        if (shouldAutoScroll && autoScroll && isAtBottom()) {
          // Use setTimeout to scroll after DOM updates
          setTimeout(() => scrollToBottom(), 50);
        }

        switch (data.type) {
          case "ready_check":
            setIsReady(true);
            break;

          case 'initial_messages':
            // Handle both cases: data.data as array or as object with messages property
            let messagesToProcess = [];
            if (Array.isArray(data.data)) {
              messagesToProcess = data.data;
            } else if (data.data && Array.isArray(data.data.messages)) {
              messagesToProcess = data.data.messages;
            }

            // Pass the correct messages array to handleInitialMessages
            if (messagesToProcess.length > 0) {
              handleInitialMessages(messagesToProcess);
            }

            // Check if we have actual conversational LLM messages (not SYSTEM messages)
            if (messagesToProcess.length > 0) {
              console.log('Processing initial messages. Total messages:', messagesToProcess.length);
              console.log('Message types found:', messagesToProcess.map(m => ({
                id: m?.id,
                msg_type: m?.msg_type,
                content: m?.content?.substring(0, 50) + '...'
              })));

              const hasConversationalLlm = messagesToProcess.some(message => {
                const isLlm = message && message.msg_type === 'llm';
                if (isLlm) {
                  console.log('Found LLM message:', { id: message.id, msg_type: message.msg_type, content: message.content?.substring(0, 100) });
                }
                return isLlm;
              });

              console.log('Has conversational LLM messages:', hasConversationalLlm);
              console.log('Current hasLlmMessagesRef.current:', hasLlmMessagesRef.current);
              console.log('Periodic fetch interval exists:', !!periodicFetchIntervalRef.current);

              if (hasConversationalLlm) {
                console.log('Found conversational LLM messages, stopping periodic fetch');
                // stopPeriodicFetch();
              } else {
                console.log('No LLM messages found, continuing periodic fetch');
              }

              // Process file_updates for each message
              messagesToProcess.forEach(message => {
                if (message && message.file_updates) {
                  processMessageFileUpdates(message);
                }
              });
            }
            break;

          case 'file_update':
            if (data.data) {
              const { message_id, operation, file_name, is_end, id } = data.data;

              if (message_id && file_name) {
                // Find messages to attach this operation to
                const possibleMessages = messages.filter(m => m.msg_type === 'llm');

                // Try to find matching messages
                const exactMatch = possibleMessages.find(m => m.id === message_id);
                const partialMatch = !exactMatch && possibleMessages.find(m =>
                  (typeof m.id === 'string' && typeof message_id === 'string' &&
                  (m.id.includes(message_id) || message_id.includes(m.id)))
                );

                // Use the best match or most recent message
                const targetMessage = exactMatch || partialMatch ||
                  (possibleMessages.length > 0 ? possibleMessages[possibleMessages.length - 1] : null);

                if (targetMessage) {
                  // For first file operation in a message, auto-open the accordion
                  const existingOperations = fileOperationsMap[targetMessage.id] || [];
                  if (existingOperations.length === 0) {
                    // Auto-open the accordion for the first file operation
                    const updatedAccordions = {...openAccordions};
                    updatedAccordions[targetMessage.id] = true;
                    setOpenAccordions(updatedAccordions);
                  }

                  updateFileOperations(targetMessage.id, operation || 'write', file_name, !!is_end, id);
                } else {
                  // Fallback for when no message is found
                  updateFileOperations(message_id, operation || 'write', file_name, !!is_end, id);
                }
              }
            }
            break;

          case 'deployment_status':
            // Deployment status is now handled via props from CodeDiscussionPanel
            // to avoid duplicate processing. The parent component listens for
            // deployment_status messages and passes them as props to ChatInterface.
            // This prevents the same message from being processed twice.
            break;

          case 'message_received':
            // When a user message is received, prepare for AI typing
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }
            
            // Stop periodic fetch when conversational LLM message is received (not SYSTEM messages)
            if (data.data.msg_type === 'llm') {
              // stopPeriodicFetch();
            }

            // Add message without forcing scroll
            safeAddOrUpdateMessageInState(data.data, data.type);

            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }

            // Only auto-scroll for important messages when user hasn't manually scrolled
            if (autoScroll && !isManualScrollingStillActive() && ['user', 'system_message'].includes(data.data.msg_type)) {
              // Use a slight delay to ensure message is rendered
              setTimeout(() => {
                // Double-check user hasn't scrolled during the delay
                if (!isManualScrollingStillActive()) {
                  scrollToBottom();
                }
              }, 100);
            }
            break;

          case 'message_chunk':
            // AI is typing when receiving chunks
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(true);
            }

            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }

            // Always use the batched update for chunks to reduce flickering
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'message_resolved':
            // AI finished typing
            if (data.data.msg_type === 'llm') {
              setIsAiTyping(false);
            }
            
            // Process file_updates if present
            if (data.data && data.data.file_updates) {
              processMessageFileUpdates(data.data);
            }
            
            safeAddOrUpdateMessageInState(data.data, data.type);
            break;

          case 'agent_message':
            // Only stop periodic fetch for actual conversational LLM messages, not SYSTEM messages
            if (data.data && data.data.msg_type === 'llm') {
              // stopPeriodicFetch();
            }
            
            try {
              // Handle agent messages with streaming status
              if (data.data && data.data.status === "streaming") {
                setIsAiTyping(true);

                // Look for an existing streaming message to update
                const streamingMessage = messages.find(m =>
                  m.msg_type === 'llm' && m.status === 'streaming'
                );

                if (streamingMessage) {
                  // Update existing streaming message with REPLACEMENT (not append)
                  safeAddOrUpdateMessageInState({
                    ...streamingMessage,
                    ...data.data,
                    content: data.data.content, // Replace content for streaming agent messages
                    status: 'streaming'
                  }, 'agent_message_streaming'); // Special type to indicate content replacement

                  // Auto-scroll if at bottom
                  if (autoScroll && isAtBottom()) {
                    setTimeout(() => scrollToBottom(), 50);
                  }
                } else {
                  // Create a new streaming message
                  safeAddOrUpdateMessageInState({
                    ...data.data,
                    id: data.data.id || Date.now(),
                    type: "agent_message",
                    status: "streaming",
                    timestamp: data.data.timestamp || new Date().toISOString()
                  }, 'agent_message_streaming');

                  // Auto-scroll if at bottom
                  if (autoScroll && isAtBottom()) {
                    setTimeout(() => scrollToBottom(), 50);
                  }
                }
              } else if (data.data) {
                // Validate required fields for agent messages
                if (!data.data.id) {
                  console.warn('Agent message missing ID, generating one');
                  data.data.id = `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                }
                
                if (!data.data.timestamp) {
                  data.data.timestamp = new Date().toISOString();
                }

                // Ensure msg_type is set correctly
                if (!data.data.msg_type) {
                  data.data.msg_type = 'llm';
                }

                // Regular agent message handling
                safeAddOrUpdateMessageInState(data.data, data.type);

                // Handle status-specific logic
                if (data.data.status === 'completed' || data.data.status === 'done') {
                  setIsAiTyping(false);
                  // Final scroll to bottom when message completes
                  if (autoScroll) {
                    setTimeout(() => scrollToBottom(), 150);
                  }
                } else if (data.data.status === 'needs_response' && data.data.requires_resolution) {
                  // Stop typing indicator for messages that need user response
                  setIsAiTyping(false);
                  
                  // Auto-scroll to show the message that needs response
                  if (autoScroll) {
                    setTimeout(() => scrollToBottom(), 100);
                  }
                }
              }
            } catch (error) {
              console.error('Error processing agent_message:', error, data);
              setIsAiTyping(false);
              
              // Add error message to chat
              if (setMessages && typeof setMessages === 'function') {
                setMessages(prev => [
                  ...prev,
                  {
                    id: `error_${Date.now()}`,
                    content: "Error processing message. Please try again.",
                    msg_type: "error",
                    timestamp: new Date().toISOString(),
                    status: "error"
                  }
                ]);
              }
            }
            break;

          case 'message_status':
          case 'message_added':
          case 'command_response':
            safeAddOrUpdateMessageInState(data.data, data.type);
            if (data.data.msg_type === 'user') {
              setIsAiTyping(true);
            }
            // Stop periodic fetch when conversational LLM message is received (not SYSTEM messages)
            if (data.data.msg_type === 'llm') {
              // stopPeriodicFetch();
            }
            break;

          case 'needs_response':
            if (data.data.requires_resolution == true) {
              setIsAiTyping(false);
            }
            break;

          case 'switch_to_checkpoints_status':
            // Handle rollback status response
            if (handleRollbackStatusUpdate) {
              handleRollbackStatusUpdate(data);
            }
            
            // Handle message reversion when status is completed
            if (data.data && data.data.status === 'completed' && data.data.message_id) {
              const targetMessageId = data.data.message_id;
              
              // Find the index of the target message
              const targetMessageIndex = messages.findIndex(msg =>
                msg.id === targetMessageId ||
                (typeof msg.id === 'string' && typeof targetMessageId === 'string' &&
                 (msg.id.includes(targetMessageId) || targetMessageId.includes(msg.id)))
              );
              
              if (targetMessageIndex !== -1) {
                // Revert messages - keep messages up to and including the target message
                const revertedMessages = messages.slice(0, targetMessageIndex + 1);
                
                // Update messages state to revert to the checkpoint
                if (setMessages && typeof setMessages === 'function') {
                  setMessages(revertedMessages);
                }
                
                // Clear file operations for removed messages
                if (setFileOperationsMap && typeof setFileOperationsMap === 'function') {
                  const updatedFileOpsMap = {};
                  revertedMessages.forEach(msg => {
                    if (fileOperationsMap[msg.id]) {
                      updatedFileOpsMap[msg.id] = fileOperationsMap[msg.id];
                    }
                  });
                  setFileOperationsMap(updatedFileOpsMap);
                }
                
                // Clear accordion states for removed messages
                if (setOpenAccordions && typeof setOpenAccordions === 'function') {
                  const updatedAccordions = {};
                  revertedMessages.forEach(msg => {
                    if (openAccordions[msg.id] !== undefined) {
                      updatedAccordions[msg.id] = openAccordions[msg.id];
                    }
                  });
                  setOpenAccordions(updatedAccordions);
                }
                
                // Stop AI typing indicator after rollback
                setIsAiTyping(false);
                
                console.log(`Reverted to message ID: ${targetMessageId}, keeping ${revertedMessages.length} messages`);
              } else {
                console.warn(`Target message ID ${targetMessageId} not found for rollback`);
              }
            }
            break;

          case 'checkpoint_created':
            // Handle checkpoint creation messages
            if (data.data) {
              // Ensure required fields are set
              if (!data.data.id) {
                data.data.id = `checkpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
              }
              
              if (!data.data.timestamp) {
                data.data.timestamp = new Date().toISOString();
              }

              // Set msg_type to checkpoint for proper rendering
              data.data.msg_type = 'checkpoint';
              
              // Add the checkpoint message to state
              safeAddOrUpdateMessageInState(data.data, data.type);
              
              // Stop AI typing indicator if it was on
              setIsAiTyping(false);
              
              // Auto-scroll to show the checkpoint message
              if (autoScroll) {
                setTimeout(() => scrollToBottom(), 100);
              }
            }
            break;

          case 'error':
            setIsAiTyping(false); // Stop typing animation on error
            break;

          default:
            // For any other message types, just process normally
            if (data.data) {
              // Process file_updates if present
              if (data.data && data.data.file_updates) {
                processMessageFileUpdates(data.data);
              }
              
              safeAddOrUpdateMessageInState(data.data, data.type);
            }
            break;
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    };

    // Create stable close handler to prevent memory leaks from anonymous functions
    const handleClose = () => {
      setIsAiTyping(false);
    };

    // Register the event listeners
    wsConnection.addEventListener('message', handleMessage);
    wsConnection.addEventListener('error', handleConnectionError);
    wsConnection.addEventListener('close', handleClose);

    return () => {
      // Clean up event listeners for this specific effect
      if (wsConnection) {
        wsConnection.removeEventListener('message', handleMessage);
        wsConnection.removeEventListener('error', handleConnectionError);
        wsConnection.removeEventListener('close', handleClose);
      }
      
      // Clean up periodic fetch interval
      if (periodicFetchIntervalRef.current) {
        clearInterval(periodicFetchIntervalRef.current);
        periodicFetchIntervalRef.current = null;
      }
    };
  }, [
    wsConnection,
    handleInitialMessages,
    safeAddOrUpdateMessageInState,
    autoScroll,
    scrollToBottom,
    setIsAiTyping,
    setIsReady,
    updateFileOperations,
    setMessages,
    messages,
    isAtBottom,
    isManualScrollingStillActive,
    fileOperationsMap,
    openAccordions,
    setOpenAccordions,
    pendingFileOperations,
    handleRollbackStatusUpdate,
    processMessageFileUpdates,
    stopPeriodicFetch
  ]);

  // Effect to start periodic fetch when WebSocket is connected and no user messages
  useEffect(() => {
    if (wsConnection && fetchInitialMessages && taskId) {
      // Start periodic fetch after a short delay to allow initial messages to load
      const timeoutId = setTimeout(() => {
        startPeriodicFetch();
      }, 2000); // Wait 2 seconds before starting periodic fetch

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [wsConnection, fetchInitialMessages, taskId, startPeriodicFetch]);

  // Effect to reset LLM message flag when task changes
  useEffect(() => {
    hasLlmMessagesRef.current = false;
  }, [taskId]);
}; 