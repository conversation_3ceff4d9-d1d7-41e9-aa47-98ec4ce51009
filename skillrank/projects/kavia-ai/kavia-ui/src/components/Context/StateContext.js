// StateContext.js
"use client"
import React, { createContext, useState, useEffect } from 'react';

export const StateContext = createContext();

export const StateProvider = ({ children }) => {

  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isVertCollapse, setIsVertCollapse] = useState(true);
  const [verticalPanelState, setVerticalPanelState] = useState('closed');
  const [drawerLabel, setDrawerLabel] = useState('timeline');
  const [activeLeftPanelTab, setActiveLeftPanelTab] = useState('timeline'); // New state for left panel tabs

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        if (window.innerWidth < 1024 && !isCollapsed) {
          setIsCollapsed(false);
        }
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isCollapsed]);

  return (
    <StateContext.Provider value={{
      isCollapsed,
      setIsCollapsed,
      isVertCollapse,
      setIsVertCollapse,
      drawerLabel,
      setDrawerLabel,
      verticalPanelState,
      setVerticalPanelState,
      activeLeftPanelTab,
      setActiveLeftPanelTab
    }}>
      {children}
    </StateContext.Provider>
  );
};