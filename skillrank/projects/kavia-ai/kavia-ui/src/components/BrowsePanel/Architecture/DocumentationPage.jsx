"use client";

import React, { useEffect, useState, useContext } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import {
  getDocumentation,
  downloadDocumentationPDF,
  syncDocumentationToS3,
} from "@/utils/documentationAPI";
import { renderHTML } from "@/utils/helpers";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import NavigationSidebar from "@/components/Documentation/NavigationSidebar";
import DocumentContent from "@/components/Documentation/DocumentContent";
import MobileNavToggle from "@/components/Documentation/MobileNavToggle";
import { Loading2 } from "@/components/Loaders/Loading";

const DocumentationPage = () => {
  // Context and hooks
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();
  const params = useParams();


  // State management
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [syncingToS3, setSyncingToS3] = useState(false);
  const [documentation, setDocumentation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeSection, setActiveSection] = useState("");
  const [generatingPdf, setGeneratingPdf] = useState(false);

  const cleanContentFromPTags = (content) => {
    
    // Simply remove p tags
    return content
      .replace(/<p>/g, '')  // Remove all opening p tags
      .replace(/<\/p>/g, ''); // Remove all closing p tags
  };
  


  // Fetch documentation data
  useEffect(() => {
    const fetchDocumentation = async () => {
      try {
        const data = await getDocumentation(params?.projectId);
        if (data?.length > 0) {
          const processedData = {
            doc: data[0].doc,
            sections: data[0].sections || [],
          };
          setDocumentation(processedData);
        } else {
          setDocumentation(null);
        }
        setLoading(false);
      } catch (err) {
        setError(err?.message || "Error fetching documentation");
        setLoading(false);
      }
    };

    if (params?.projectId) {
      fetchDocumentation();
    }
  }, [params?.projectId]);

// Handle scroll spy for active section
useEffect(() => {
  const handleScroll = () => {
    if (!documentation?.sections) return;

    const mainContent = document.getElementById("main-content");
    if (!mainContent) return;

    // Get all section elements
    const sectionIds = documentation.sections.map((section) =>
      `section-${section.id}`
    );

    // Find the section currently in view
    let currentSection = "";
    // Reverse the array to check from bottom to top
    // This ensures we highlight the correct section when multiple sections are in view
    for (const sectionId of [...sectionIds].reverse()) {
      const element = document.getElementById(sectionId);
      if (element) {
        const rect = element.getBoundingClientRect();
        // Adjusted threshold - consider a section active when it's closer to the top
        if (rect.top <= 300) {  // Increased threshold
          currentSection = sectionId;
          break;
        }
      }
    }

    // Only update if we found a section and it's different from current
    if (currentSection && currentSection !== activeSection) {
      setActiveSection(currentSection);
    }
  };

  const mainContent = document.getElementById("main-content");
  if (mainContent) {
    // Debounce the scroll handler to improve performance
    let timeout;
    const debouncedHandler = () => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(handleScroll, 100);
    };

    mainContent.addEventListener("scroll", debouncedHandler);
    // Initial check for active section
    handleScroll();
    return () => {
      mainContent.removeEventListener("scroll", debouncedHandler);
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }
}, [documentation, activeSection]);
  // Handle section navigation
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    const mainContent = document.getElementById("main-content");
    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80, // Account for header
        behavior: "smooth",
      });
      setIsNavOpen(false); 
    }
  };

  // Handle S3 sync
  const handleSyncToS3 = async () => {
    try {
      setSyncingToS3(true);
      await syncDocumentationToS3(params?.projectId);
      showAlert("Documentation saved successfully", "success");
    } catch (error) {
      
      showAlert(error.message || "Failed to save documentation", "error");
    } finally {
      setSyncingToS3(false);
    }
  };

  // Handle navigation to documents
  const navigateToDocuments = () => {
    router.push(`/projects/${params?.projectId}/documents?type=SAD`);
  };

  // Handle PDF generation
  const generatePDF = async () => {
    try {
      setGeneratingPdf(true);
      await downloadDocumentationPDF(params?.projectId);
      showAlert("PDF Download started", "success");
    } catch (error) {
      showAlert("Error generating PDF: " + error, "error");
    } finally {
      setGeneratingPdf(false);
    }
  };

  const handleUpdateSection = async (sectionId, newContent) => {
    try {
      // Show loading state or progress indicator if needed
      await updateDocumentationSection(params?.projectId, sectionId, newContent);
      
      // Refresh the documentation data
      const updatedData = await getDocumentation(params?.projectId);
      if (updatedData?.length > 0) {
        const processedData = {
          doc: updatedData[0].doc,
          sections: updatedData[0].sections || [],
        };
        setDocumentation(processedData);
      }
      
      showAlert("Section updated successfully", "success");
    } catch (error) {
      
      showAlert(error.message || "Failed to update section", "error");
    }
  };
  

  // Loading state
  if (loading) {
    return <Loading2 />;
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-screen p-4">
        <div className="text-red-500 text-center bg-red-50 p-6 rounded-lg shadow max-w-md">
          <h2 className="typography-body-lg font-weight-semibold mb-2">
            Error Loading Documentation
          </h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // No documentation state
  if (!documentation?.doc) {
    return (
      <div>
        <EmptyStateView type="sadDocumentation" />
      </div>
    );
  }
  const handleSectionUpdate = async (sectionId, propertyKey, newContent) => {
    try {
      if (!sectionId) {
        throw new Error('Section ID is missing');
      }

      const data = await getDocumentation(params?.projectId);
      if (data?.length > 0) {
        const processedData = {
          doc: data[0].doc,
          sections: data[0].sections || [],
        };
        setDocumentation(processedData);
        showAlert("Section updated successfully", "success");
      }
    } catch (error) {
      
      showAlert(`Failed to update section: ${error.message}`, "error");
    }
  };
  // Main render
  return (
    <div className="relative flex min-h-screen max-h-screen overflow-hidden bg-gray-50">  
      {isNavOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 lg:hidden"
          onClick={() => setIsNavOpen(false)}
        />
      )}

      {/* Navigation Sidebar */}
      <div
        className={`
          fixed lg:relative
          inset-y-0 left-0
          w-[85vw] sm:w-[60vw] md:w-[40vw] lg:w-[25vw] xl:w-[20vw]
          transform
          ${isNavOpen ? "translate-x-0" : "-translate-x-full"}
          lg:translate-x-0
          transition-transform duration-200 ease-in-out
          z-40 lg:z-0
          h-screen
          overflow-hidden
        `}
      >
        <NavigationSidebar
          documentation={documentation}
          activeSection={activeSection}
          scrollToSection={scrollToSection}
          onGeneratePDF={generatePDF}
          onSyncToS3={handleSyncToS3}
          onNavigateToDocuments={navigateToDocuments}
          generatingPdf={generatingPdf}
          syncingToS3={syncingToS3}
          projectId={params?.projectId}
          refreshDocumentation={async () => {
            const data = await getDocumentation(params?.projectId);
            if (data?.length > 0) {
              setDocumentation({
                doc: data[0].doc,
                sections: data[0].sections || [],
              });
            }
          }}
        />
      </div>

      {/* Main Content */}
      <main
        id="main-content"
        className="flex-1 relative w-full lg:w-[calc(100%-25vw)] xl:w-[calc(100%-20vw)] 
                   h-[80%] overflow-y-auto overflow-x-hidden 
                   transition-all duration-200 ease-in-out"
      >
        <DocumentContent
          documentation={documentation}
          renderHTML={renderHTML}
          onUpdateSection={handleSectionUpdate}  
          projectId={params?.projectId}
        />
      </main>
      {/* Mobile Navigation Toggle */}
      <MobileNavToggle
        isOpen={isNavOpen}
        onToggle={() => setIsNavOpen(!isNavOpen)}
      />
    </div>
  );
};

export default DocumentationPage;
