"use client";
import React, { useEffect, useState, useContext } from "react";
import "@/styles/tabs/architecture/container.css";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { fetchContainerWithComponent, fetchChildNodes } from "@/utils/api";
import { getContainerFunctionalRequirements } from "@/utils/api";
import { getPastCodeGenerationTasks } from "@/utils/batchAPI";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import en from "@/en.json";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from "@/components/UIComponents/Buttons/IconButton";
import { ArrowLeft, BookOpen, Database, Lock } from "lucide-react";
import { updateNodeByPriority } from "@/utils/api";
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import NavigationTree from "@/components/Modal/NavigationTreeComponent";
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import { useResponsiveDimensions } from "@/utils/responsiveness";
import PlaceholderMessage from "@/components/Modal/PlaceHolderMessage";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import { PLATFORMS, frameworks, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { BranchSelector } from "@/components/Git/BranchSelector";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import { getRepository } from "@/utils/repositoryAPI";
import DatabaseDetails from "@/components/Database/DatabaseDetails";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import DatabaseAuthorization from "@/components/Modal/DbModal";
import { buildProjectUrl } from "@/utils/navigationHelpers";

const Page = () => {
  const { showAlert } = useContext(AlertContext);
  const [ContainerData, setContainerData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [functionalRequirements, setFunctionalRequirements] = useState(null);
  const [error, setError] = useState(null);
  const { setContainerIdVal, setComponentData } = useContext(ArchitectureContext);
  const params = useParams();
  const pathname = usePathname();
  const router = useRouter();
  const projectId = params.projectId;
  const searchParams = useSearchParams();
  const containerId = params.containerId;
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const { mainContentWidth, contentHeight, calculateDimensions } = useResponsiveDimensions();
  const [isRepoConfigured, setIsRepoConfigured] = useState(false);
  const [databaseInfo, setDatabaseInfo] = useState(null);
  const [isDatabaseVisible, setIsDatabaseVisible] = useState(false);
  const [loadingDatabase, setLoadingDatabase] = useState(false);

  // Code generation state variables
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: null });
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [isPastTasksCodeModalOpen, setIsPastTasksCodeModalOpen] = useState(false);
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [isPastTaskLoading, setIsPastTaskLoading] = useState(false);
  const { isVisible } = useCodeGeneration();

  const [repositoryState, setRepositoryState] = useState({
    state: 'initial', // initial, loading, success, error
    data: '' // repository data
  });

  const [showDbModal, setShowDbModal] = useState(false);

  const fetchRepositoryDetails = async () => {
      try {

        setRepositoryState({
          state: 'loading',
          data: null
        });


        const response = await getRepository(projectId, containerId);

        if (response.repository) {
          setRepositoryState({
            state: 'success',
            data: response.repository
          });
        } else {
          setRepositoryState({
            state: 'error',
            data: null
          });
        }
      } catch (err) {
        setRepositoryState({
          state: 'error',
          data: null
        });
        console.error("Error fetching repository details:", err);
        throw new Error('Failed to fetch repository details');
      }
    };


      useEffect(() => {
        if (containerId && projectId) {
          fetchRepositoryDetails();
        }
      }, [projectId, containerId]);



  const fetchData = async () => {
    sessionStorage.setItem("containerId", containerId);
    try {
      const data = await fetchContainerWithComponent(projectId, containerId);
      setContainerIdVal(containerId);
      setContainerData(data);
      setComponentData(data?.data?.components);

      // Check if repository is configured
      if (data?.data?.container?.properties?.repository_id) {
        setIsRepoConfigured(true);
      }

      // Set platform and framework if available
      if (data?.data?.container?.properties?.platform) {
        const platformData = PLATFORMS.find(p => p.key === data.data.container.properties.platform);
        setCurrentPlatform(platformData ? {
          key: platformData.key,
          label: platformData.label,
          icon: null // We'll set the icon when rendering, not in the state
        } : { key: "generic", label: "Generic", icon: null });
      }

      if (data?.data?.container?.properties?.framework) {
        const frameworkData = frameworks.find(f => f.key === data.data.container.properties.framework) ||
                              backendFrameworks.find(f => f.key === data.data.container.properties.framework) ||
                              mobileFrameworks.find(f => f.key === data.data.container.properties.framework);
        if (frameworkData) {
          setCurrentFramework(frameworkData);
        }
      }

      if (data?.data?.container?.properties?.ImplementedRequirementIDs) {
        const requirements = await getContainerFunctionalRequirements(
          projectId,
          containerId
        );
        setFunctionalRequirements(requirements);
      }
    } catch (err) {
      console.error(err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const requirementHeaders = [
    { key: "id", label: "ID" },
    { key: "Title", label: "Title" },
    { key: "Type", label: "Type" },
    { key: "Description", label: "Description" },
  ];

  const requirementTableData = functionalRequirements?.functional_requirements?.map((req) => ({
    id: req.id,
    Title: req.properties.Title,
    Type: <span className="bg-gray-100 rounded-3xl px-3 typography-caption p-1 font-weight-medium">
      {req.properties.Type}
    </span>,
    Description: req.properties.Description,
  }));

  useEffect(() => {
    fetchData();
  }, [projectId, containerId, pathname]);

  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    setIsGeneratingCode(false);
    }
  },[searchParams])

  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Container Details"
        message={en.UnableToLoadContainerDetails}
        onRetry={() => fetchData()}
        panelType="main"
      />
    );
  }

  const headers = [
    { key: "id", label: "Id" },
    { key: "title", label: "Title" },
    { key: "type", label: "Type" },
    { key: "description", label: "Description" },
  ];

  const cleanDescription = (description) => {
    if (!description) return '';

    return description
      .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
      .replace(/\*\*/g, '')        // Remove bold (**text**)
      .replace(/\*/g, '')          // Remove italics (*text*)
      .replace(/`/g, '')           // Remove code ticks (`code`)
      .replace(/\n\n/g, ' ')       // Replace double line breaks with space
      .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
      .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
      .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
      .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
      .trim();                     // Trim extra whitespace
  };


  const tableData = ContainerData?.data?.components.map((data) => ({
    id: data.id,
    title: data.properties.Title,
    type: data.properties.Type,
    description: cleanDescription(data.properties.Description),
  }));

  const handleRowClick = (Id) => {
    router.push(buildProjectUrl(projectId, `architecture/component/${Id}`));
  };

  const handleUpdateContainer = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", containerId);
    newSearchParams.set("node_type", "Container");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  // Added the missing handleDatabaseConfig function
  const handleDatabaseConfig = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", containerId);
    newSearchParams.set("node_type", "Database");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleShowDatabase = async () => {
    if (!isDatabaseVisible && !databaseInfo) {
      setLoadingDatabase(true);
      try {
        const response = await fetchChildNodes(containerId, "Container", "Database");
        setDatabaseInfo(response);
        setIsDatabaseVisible(true);
      } catch (error) {
        console.error("Error fetching database information:", error);
        showAlert("Failed to fetch database information", "error");
      } finally {
        setLoadingDatabase(false);
      }
    } else {
      setIsDatabaseVisible(!isDatabaseVisible);
    }
  };

  const updateProps = {
    onUpdateClick: handleUpdateContainer,
    buttonText:
      ContainerData && ContainerData?.data?.components?.length > 0
        ? "Update Container"
        : "Create Container",
  };



  const handleBack = () => {
    if (sessionStorage.getItem("querySet")) {
      const backTabs = Number(sessionStorage.getItem("querySet"));
      sessionStorage.removeItem("querySet");
      if (window.history.length > Number(backTabs) * (-1)) {
        window.history.go(backTabs);
      }
      else {
        router.push(buildProjectUrl(projectId, 'architecture/architecture-requirement'));
      }
    }
    else {
      router.back();
    }
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", containerId);
    newSearchParams.set("discussion_type", "Container");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleRepoDetailsOpen = () => setShowRepoDetails(true);

  const handleRepoDetailsClose = (success = false) => {
    setShowRepoDetails(false);
    if (success) {
      setIsRepoConfigured(true);
      showAlert('Repository configured successfully', 'success');
      fetchData(); // Refresh data to get updated repository info
    }
  };

  const handleFunctionalRequirementClick = (id, type) => {
    router.push(buildProjectUrl(projectId, `architecture/container/${containerId}/FunctionalRequirement/${id}`));
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(containerId, key, value);

      if (response === "success") {
        setContainerData((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            container: {
              ...prev.data.container,
              properties: {
                ...prev.data.container.properties,
                [key]: value,
              },
            },
          },
        }));
        showAlert("Content updated successfully", "success");
      } else {
        throw new Error("Update failed");
      }
    } catch (error) {
      console.error(error);
      showAlert("Failed to update content", "error");
    }
  };

  // Code generation functions
  const handlePlatformChange = (platformData) => {
    setCurrentPlatform({
      key: platformData.key,
      label: platformData.label,
      icon: null
    });
    handlePropertyUpdate("platform", platformData.key);

    if(platformData.key === "mobile")
      setCurrentFramework(mobileFrameworks[0]);
    else if(platformData.key === "web")
      setCurrentFramework(frameworks[0]);
    else if(platformData.key === "backend")
      setCurrentFramework(backendFrameworks[0]);
    else
      setCurrentFramework(frameworks[0]);
  }

  const handleFrameworkChange = (newFramework) => {
    try {
      handlePropertyUpdate("framework", newFramework.key);
      setCurrentFramework(newFramework);
      showAlert("Framework updated successfully", "success");
    } catch (error) {
      console.error(error);
      showAlert("Failed to update framework", "error");
    }
  };

  const handleSetPastTasks = (tasks, skip) => {
    let newTasks = [];
    newTasks.push(...tasks);
    setPastTasks(newTasks);
  }

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    setIsPastTaskLoading(true);
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        containerId,
        currentLimit,
        currentSkip
      );
      handleSetPastTasks(result.tasks, currentSkip);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      console.error(error);
      showAlert("Failed to fetch past code generation tasks", "error");
    } finally {
      setIsPastTaskLoading(false);
    }
  };

  const handleViewPastCodeGeneration = async () => {
    await fetchPastTasks();
    setIsPastTasksCodeModalOpen(true);
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const handleGenerateCode = () => {
    setIsGeneratingCode(true);
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await updateNodeByPriority(containerId, 'branch', newBranch);
      showAlert('Branch updated successfully', 'success');
      fetchData(); // Refresh container data
    } catch (error) {
      console.error(error);
      showAlert('Failed to update branch', 'error');
    }
  };

  const BranchSelection = () => {
    return (
      <BranchSelector
        projectId={projectId}
        containerId={containerId}
        currentBranch={ContainerData?.data?.container?.properties?.branch}
        onUpdate={handleBranchUpdate}
        tooltip={"Select branch"}
        className="w-full"
      />
    )
  }

  const treeData = [...Object.entries(ContainerData?.model?.Container?.ui_metadata || {})
    .filter(
      ([key, value]) =>
        !["Title", "Type", "ImplementedRequirementIDs",].includes(key) && value.hidden !== true &&
        ContainerData?.data?.container?.properties?.hasOwnProperty(key)
    )
    .map(([key, value]) => ({
      id: `${(value.Label || key).toLowerCase().replace(/[_\s]+/g, '-')}`,
      name: (value.Label || key).replace(/[_-]/g, ' '),
      children: [],
    })),
  ...(ContainerData?.data?.components?.length > 0 && !isDatabaseVisible
    ? [{
      id: "system-components",
      name: "System Components",
      children: [],
    }]
    : []),

  ...(functionalRequirements && !isDatabaseVisible
    ? [{
      id: "implemented-functional-requirements",
      name: "Implemented Functional Requirements",
      children: [],
    }]
    : []),

  ...(ContainerData?.data?.interfaces?.length > 0 && !isDatabaseVisible
    ? [{
      id: "related-interfaces",
      name: "Related Interfaces",
      children: [],
    }]
    : []),
  ]

  const handleScrollToSection = (id) => {
    const element = document.getElementById(id);
    const mainContent = document.getElementById("main-content")
    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80,
        behavior: "smooth",
      });
    }
  };

  // Updated database logic: Show database button if HasDatabase property exists, disable if false
  const hasDatabaseProperty = ContainerData?.data?.container?.properties?.hasOwnProperty('HasDatabase');
  const hasDatabaseEnabled = ContainerData?.data?.container?.properties?.HasDatabase === true;
  const shouldShowDatabaseButton = hasDatabaseProperty;
  const isDatabaseButtonDisabled = hasDatabaseProperty && !hasDatabaseEnabled;

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-200">
        <div className="relative px-2 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {ContainerData?.data?.container?.properties?.Title ||
                    "Container Details"}
                </h2>
                <div className="flex items-center gap-1">
                  <Badge
                    type={ContainerData?.data?.container?.properties?.Type}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isDatabaseVisible && (
                <div className="flex items-center gap-2">
                  <DynamicButton
                    variant="primary"
                    icon={Lock}
                    onClick={() => setShowDbModal(true)}
                    text={"Authorize"}
                    disabled={loadingDatabase}
                    loading={loadingDatabase}
                  />
                </div>
              )}

              <ConfigureButtons
                updateProps={{
                  ...updateProps,
                  disabled: !isRepoConfigured || (isDatabaseVisible && !ContainerData?.data?.container?.properties?.HasDatabase),
                  tooltip: !isRepoConfigured 
                    ? 'Configure repository first' 
                    : (isDatabaseVisible && !ContainerData?.data?.container?.properties?.HasDatabase)
                    ? "HasDatabase property must be true to configure database"
                    : undefined,
                  buttonText: isDatabaseVisible ? "Update Database" : updateProps.buttonText,
                  onUpdateClick: isDatabaseVisible ? handleDatabaseConfig : handleUpdateContainer
                }}
              />

              {/* Show Database button if HasDatabase property exists, disable if false */}
              {shouldShowDatabaseButton && (
                <DynamicButton
                  variant="primary"
                  icon={Database}
                  onClick={handleShowDatabase}
                  text={!isDatabaseVisible ? 'Database' : 'Container'}
                  disabled={isDatabaseButtonDisabled || loadingDatabase}
                  loading={loadingDatabase}
                  tooltip={isDatabaseButtonDisabled ? "Database is disabled for this container" : undefined}
                />
              )}

              <DynamicButton
                variant="primary"
                icon={BookOpen}
                onClick={handleViewPastDiscussion}
                text="History"
              />
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="relative flex max-h-[74vh] overflow-hidden bg-white-50">
      <div>
        {treeData && (
          <NavigationTree treeData={treeData} handleScrollToSection={handleScrollToSection}/>
        )}
      </div>
      <main
        id="main-content"
        className={`
            flex-1
            relative
            overflow-y-auto
            overflow-x-hidden
            transition-all
            duration-300
            ease-in-out
          `}
        style={{
          width: mainContentWidth,
        }}
      >
        <div className="w-full pl-3 pr-4">
          <div className="mb-4">
            <HeaderSection />
          </div>

          {ContainerData ? (
            <div>
              {!isDatabaseVisible ? (
                <PropertiesRenderer
                  properties={ContainerData?.data?.container?.properties}
                  metadata={ContainerData?.model?.Container?.ui_metadata}
                  to_skip={[
                    "configuration_state",
                    "Type",
                    "Title",
                    "ImplementedRequirementIDs",
                  ]}
                  onUpdate={handlePropertyUpdate}
                />
              ) : (
                <div id="database-details" className="mb-4">
                  <DatabaseDetails 
                    databaseInfo={databaseInfo}
                    loading={loadingDatabase}
                    hasDatabase={hasDatabaseEnabled}
                  />
                </div>
              )}

              {!isDatabaseVisible && (
                <>
                  <div id="system-components" className="relatedContentDiv ">
                    {ContainerData?.data?.components?.length > 0 ? (
                      <>
                        <TableComponent
                          data={tableData}
                          onRowClick={handleRowClick}
                          headers={headers}
                          sortableColumns={{ id: true, title: true, type: true }}
                          itemsPerPage={20}
                          title={en.ChildComponentsHeading}
                        />
                      </>
                    ) : (
                      <div className="mb-4">
                        <PlaceholderMessage type="components" message="No components are currently available" subMessage="Add some new components to get started." />
                      </div>
                    )}
                  </div>
                  <div id="implemented-functional-requirements" className="relatedContentDiv mt-3">
                    {functionalRequirements && (
                      <div className="relatedContentDiv mt-3">
                        <TableComponent
                          data={requirementTableData}
                          headers={requirementHeaders}
                          sortableColumns={{ Title: true, Type: true }}
                          itemsPerPage={20}
                          title="Implemented Functional Requirements"
                          onRowClick={handleFunctionalRequirementClick}
                        />
                      </div>
                    )}
                    <div id="related-interfaces">
                      {ContainerData?.data?.interfaces?.length > 0 ? (
                        <TableComponent
                          data={ContainerData.data.interfaces.map((interfaces) => ({
                            id: interfaces.id,
                            title: interfaces.properties.Title,
                            type: interfaces.type,
                            description: interfaces.properties.Description,
                          }))}
                          onRowClick={(id) =>
                            router.push(
                              buildProjectUrl(projectId, `architecture/interfaces/${id}`)
                            )
                          }
                          headers={headers}
                          sortableColumns={{ id: true, title: true, type: true }}
                          itemsPerPage={20}
                          title={en.InterfacesHeading}
                        />
                      ) : (
                        <div className="mb-3">
                          <PlaceholderMessage type="interfaces" message="No interfaces are currently available" subMessage="Add a new interfaces to get started." />
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          ) : (
            <p className="notFound">
              <EmptyStateView type="containerDetails" />
            </p>
          )}

          {/* Repository Modal */}
          {showRepoDetails && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <RepositoryDetailsModal
                  open={true}
                  projectId={projectId}
                  containerId={containerId}
                  onClose={handleRepoDetailsClose}
                  onSuccess={() => handleRepoDetailsClose(true)}
                />
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Code Generation Modals and Components */}
      {isGeneratingCode && (
        <CodeGenerationHandler
          projectId={projectId}
          itemId={containerId}
          onComplete={() => {
            setIsGeneratingCode(false);
          }}
        />
      )}
      {isVisible && <CodeGenerationModal />}
      {isPastTasksCodeModalOpen && (
        <PastTasksModal
          isOpen={isPastTasksCodeModalOpen}
          onClose={() => setIsPastTasksCodeModalOpen(false)}
          tasks={pastTasks}
          totalCount={totalCount}
          limit={limit}
          skip={skip}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isPastTaskLoading}
        />
      )}
      {codeGenSetupModal && (
        <CodeGenerationSetupModal
          onClose={() => setCodeGenSetupModal(false)}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform.key ? 
            PLATFORMS.find(p => p.key === currentPlatform.key) || 
            PLATFORMS.find(p => p.key === "generic") 
            : PLATFORMS.find(p => p.key === "generic")}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleGenerateCode}
          repository={repositoryState.data}
          currentBranch={ContainerData?.data?.container?.properties?.branch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          isGeneratingCode={isGeneratingCode}
          projectId={projectId}
          containerId={parseInt(containerId)}
          hideActionButton={false}
          handleRepoChange={(repo) => {
            setRepositoryState({
              state: 'success',
              data: repo
            });
            showAlert('Repository configured successfully', 'success');
            // Close the repository modal if it's open
            setShowRepoDetails(false);
          }}
        />
      )}
      {showDbModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <DatabaseAuthorization
            onCancel={() => setShowDbModal(false)}
            initialData={databaseInfo && databaseInfo.length > 0 ? databaseInfo[0].properties : undefined}
          />
        </div>
      )}
    </div>
  );
};

export default Page;