"use client";
import React, { useRef, useCallback } from "react";
import { ChevronsUpDown } from "lucide-react";
import { Listbox } from "@headlessui/react";
import { Check } from "lucide-react";

// Tag component for LLM Model
const LlmModelTag: React.FC<{ tag: string; color: string }> = React.memo(
  ({ tag, color }) => {
    const tagClasses = {
      green:
        "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
      purple:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
      indigo:
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300",
    } as const;

    const tagClass =
      tagClasses[color as keyof typeof tagClasses] ||
      "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";

    return (
      <span className={`px-2 py-0.5 rounded text-xs font-medium ${tagClass}`}>
        {tag}
      </span>
    );
  }
);

LlmModelTag.displayName = "LlmModelTag";

// Option component for LLM Model dropdown
const LlmModelOption: React.FC<{
  option: any;
  selected: boolean;
  active?: boolean;
}> = React.memo(({ option, selected, active }) => {
  const tags = Array.isArray(option.tag)
    ? option.tag
    : [option.tag].filter(Boolean);
  const tagColors = Array.isArray(option.tagColor)
    ? option.tagColor
    : [option.tagColor].filter(Boolean);

  return (
    <div className="flex items-center w-full">
      <div className="flex-1 min-w-0 flex items-center">
        <span className="flex items-center pr-2 text-orange-600 dark:text-primary">
          <Check
            className={`h-5 w-5 ${selected ? "visible" : "invisible"}`}
            aria-hidden="true"
          />
        </span>
        <span
          className={`block truncate typography-body-sm transition-none ${
            active
              ? "text-orange-900 dark:text-primary"
              : "text-gray-900 dark:text-custom-text-primary"
          } ${selected ? "font-semibold" : "font-normal"}`}
        >
          {option.label}
        </span>
      </div>
      {tags.length > 0 && (
        <span className="flex gap-1 ml-auto">
          {tags.map((tag: string, idx: number) => (
            <LlmModelTag
              key={`${tag}-${idx}`}
              tag={tag}
              color={tagColors[idx] || "gray"}
            />
          ))}
        </span>
      )}
    </div>
  );
});

LlmModelOption.displayName = "LlmModelOption";

interface LlmModelOption {
  label: string;
  value: string;
  tag: string | string[];
  tagColor: string | string[];
}

interface LlmModelSelectProps {
  llmModel: string;
  setLlmModel: (value: string) => void;
  llmModelOptions: LlmModelOption[];
  isError?: boolean;
  label?: string;
}

const LlmModelSelect: React.FC<LlmModelSelectProps> = ({
  llmModel,
  setLlmModel,
  llmModelOptions,
  isError = false,
  label = "LLM Model",
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Memoize selected model to prevent unnecessary re-renders
  const selectedModel = React.useMemo(() => {
    const found = llmModelOptions.find((opt) => opt.value === llmModel);
    console.error("LlmModelSelect - Current llmModel:", llmModel);
    console.error(
      "LlmModelSelect - Available options:",
      llmModelOptions.map((opt) => ({ label: opt.label, value: opt.value }))
    );
    console.error("LlmModelSelect - Found selected model:", found);
    return found;
  }, [llmModelOptions, llmModel]);

  // Memoize tags and colors
  const { tags, tagColors } = React.useMemo(() => {
    if (!selectedModel) return { tags: [], tagColors: [] };

    return {
      tags: Array.isArray(selectedModel.tag)
        ? selectedModel.tag
        : [selectedModel.tag].filter(Boolean),
      tagColors: Array.isArray(selectedModel.tagColor)
        ? selectedModel.tagColor
        : [selectedModel.tagColor].filter(Boolean),
    };
  }, [selectedModel]);

  // Handle model selection with proper callback
  const handleModelChange = useCallback(
    (value: string) => {
      console.error("LlmModelSelect - Model selected:", value);
      setLlmModel(value);
    },
    [setLlmModel]
  );

  return (
    <div>
      <label className="block typography-body-sm font-weight-medium text-gray-700 dark:text-custom-text-primary mb-1">
        <h4 className="typography-body font-weight-medium mb-1 text-gray-700 dark:text-custom-text-primary">
          {label} <span className="text-red-600">*</span>
        </h4>
      </label>

      <Listbox value={llmModel} onChange={handleModelChange}>
        <div className="relative mt-1">
          <Listbox.Button
            ref={buttonRef}
            className={`relative w-full cursor-pointer rounded-lg bg-white dark:bg-custom-bg-secondary py-2 pl-3 pr-10 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm border transition-colors ${
              isError
                ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                : "border-gray-300 dark:border-custom-border hover:border-gray-400 dark:hover:border-gray-600"
            }`}
          >
            <div className="flex items-center justify-between w-full">
              <span className="block truncate text-gray-900 dark:text-custom-text-primary">
                {selectedModel?.label ||
                  `Model: ${llmModel}` ||
                  "Select a model"}
              </span>

              {tags.length > 0 && (
                <div className="flex items-center gap-1 ml-2">
                  {tags.map((tag: string, idx: number) => (
                    <LlmModelTag
                      key={`selected-${tag}-${idx}`}
                      tag={tag}
                      color={tagColors[idx] || "gray"}
                    />
                  ))}
                </div>
              )}

              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronsUpDown
                  className="h-5 w-5 text-gray-400 dark:text-gray-500"
                  aria-hidden="true"
                />
              </span>
            </div>
          </Listbox.Button>

          <Listbox.Options className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white dark:bg-custom-bg-secondary py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm border border-gray-300 dark:border-custom-border">
            {llmModelOptions.map((option) => (
              <Listbox.Option
                key={option.value}
                className={({ active, selected }) =>
                  `relative cursor-pointer select-none py-2 px-3 transition-colors ${
                    active ? "bg-orange-100 dark:bg-primary/10" : ""
                  } ${selected ? "bg-orange-50 dark:bg-primary/5" : ""}`
                }
                value={option.value}
              >
                {({ selected, active }) => (
                  <LlmModelOption
                    option={option}
                    selected={selected}
                    active={active}
                  />
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </div>
      </Listbox>
    </div>
  );
};

export default LlmModelSelect;
