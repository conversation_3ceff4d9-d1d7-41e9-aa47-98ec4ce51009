"use client"
import React, { useContext, useEffect } from 'react'
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import { useUser } from '@/components/Context/UserContext';
import { X } from "lucide-react"
import NewProductCard from './newProductCard'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/KaviaLongLogo'
import kavia<PERSON>ogo from "@/../public/logo/kavia_logo.svg";

import { getProducts, createCheckoutSession, getLatestSubscription, getProductDetailsByPriceId } from '@/utils/paymentAPI'
import { useState } from 'react'
import LoginSignupModal from '@/components/Modal/LoginSignupModal';
import Image from 'next/image';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import ThemeTransition from '@/components/ThemeTransition';

const FEATURES_MAP = {
    'Free': [
        'Create Web Application',
        'Figma Design Import',
        'Public Projects',
        'One Click Deployment'
    ],
    'Premium': [
        'Individual User',
        'Plan & Build New Applications',
        'Create Web & Mobile Applications',
        'Design to Code Conversion',
        'Ingest & Modify Existing Codebase',
        'Integrate with GitHub',
        'Advanced AI Features',
        'Priority Support'
    ],
    'Teams': [
        'All Premium Features',
        'Up to 20 Users',
        'Multi-user Collaboration',
        'Organization GitHub Access',
        'Team Analytics Dashboard',
        'Advanced Security Features',
        'Dedicated Support'
    ]
}

function Page() {
    const { user_id } = useUser();
    const [productsLoading, setProductsLoading] = useState(true)
    const [products, setProducts] = useState([])
    const [isLoggedIn, setIsLoggedIn] = useState(undefined);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedPrices, setSelectedPrices] = useState({});
    const [currentPlan, setCurrentPlan] = useState(null);
    const [currentPrice, setCurrentPrice] = useState(null);
    const [currentProductName, setCurrentProductName] = useState(null);

    const router = useRouter();

    // Apply dark theme for pricing page (similar to home page)
    useEffect(() => {
        document.documentElement.classList.add('dark');

        // Cleanup when leaving pricing page
        return () => {
            document.documentElement.classList.remove('dark');
        };
    }, []);

    const { showAlert } = useContext(AlertContext);

    const organizeProducts = (products) => {
        return {
            free: products.find(p => p.product_name === 'Free'),
            premium: products.filter(p => p.product_name.startsWith('Premium')),
            teams: products.filter(p => p.product_name.startsWith('Teams'))
        };
    };

    useEffect(() => {
        const idToken = Cookies.get('idToken');
        const tenantId = Cookies.get('tenant_id');
        setIsLoggedIn(!!idToken);

        const fetchData = async () => {
            try {
                // First fetch products
                const productsData = await getProducts();
                setProducts(productsData);

                // Then fetch subscription if user is logged in
                let subscription = null;
                if (tenantId && user_id) {
                    subscription = await getLatestSubscription(user_id);
                }

                // Organize products
                const organized = organizeProducts(productsData);

                if (subscription && subscription.price_id) {
                    const productDetails = await getProductDetailsByPriceId(subscription.price_id);

                    setCurrentPlan(productDetails.productName.toLowerCase());
                    setCurrentProductName(productDetails.productName);

                    // Find the product with matching price_id and set it as selected
                    const allProducts = [organized.free, ...organized.premium, ...organized.teams];
                    const currentProduct = allProducts.find(p => p.price_id === subscription.price_id);
                     const matchingProduct = productsData.find(product => 
                    product.product_name === productDetails.productName
                );
                
                // Set currentPrice from display_credits if found, otherwise fallback to credits
                setCurrentPrice(matchingProduct?.display_credits || productDetails.credits);

                    if (currentProduct) {

                        // Set the selected price based on the current subscription
                        const selectedPrices = {
                            premium: productDetails.productName.toLowerCase().startsWith('premium') ? currentProduct.price : organized.premium[0]?.price,
                            premiumplan: productDetails.productName.toLowerCase().startsWith('premium') ? currentProduct.product_name.replace("Premium ", "") : organized.premium[0]?.product_name.replace("Premium ", ""),
                            premiumCredits: productDetails.productName.toLowerCase().startsWith('premium') ? matchingProduct?.display_credits : organized.premium[0]?.display_credits,
                            teams: productDetails.productName.toLowerCase().startsWith('teams') ? currentProduct.price : organized.teams[0]?.price,
                            teamsCredits: productDetails.productName.toLowerCase().startsWith('teams') ? currentProduct.credits : organized.teams[0]?.display_credits,
                            teamsplan: productDetails.productName.toLowerCase().startsWith('teams') ? currentProduct.product_name.replace("Teams ", "") : organized.teams[0]?.product_name.replace("Teams ", "")
                        };
                        setSelectedPrices(selectedPrices);
                    }
                } else {
                    // Default selection if no subscription
                    const initialPrices = {
                        premium: organized.premium[0]?.price,
                        teams: organized.teams[0]?.price,
                        premiumplan: organized.premium[0]?.product_name.replace("Premium ", ""),
                        premiumTokens: organized.premium[0]?.credits,
                        teamsTokens: organized.teams[0]?.credits,
                        teamsplan: organized.teams[0]?.product_name.replace("Teams ", "")
                    };
                    setSelectedPrices(initialPrices);
                }
            } catch (error) {

                // showAlert('There was an error loading your subscription data. Defaulting to Free plan.',"error");

                // Set default free tier data
                setCurrentPlan('free');
                setCurrentPrice(0);
                setCurrentProductName('Free');
                setSelectedPrices({
                    premium: null,
                    teams: null,
                    premiumplan: null,
                    premiumTokens: 1000,
                    teamsTokens: 1000,
                    teamsplan: null
                });
            } finally {
                setProductsLoading(false);
            }
        };

        fetchData();
    }, [user_id]);

    const handleProductClick = async (priceId) => {
        try {
            if (!priceId) {

                return;
            }
            const response = await createCheckoutSession(priceId);
            window.open(response.session.url);
        } catch (error) {

        }
    };

    const handleContactUs = () => {
        window.open('https://kavia.ai/waitlist', '_blank');
    };

    const renderProductCards = () => {
        if (productsLoading) {
            return (
                <div className='flex justify-center items-center h-[30vh]'>
                    <div className='animate-spin rounded-full w-[3rem] h-[3rem] border-t-2 border-b-2 border-white'></div>
                </div>
            );
        }

        const organized = organizeProducts(products);

        return (
            <div className='w-[90%] lg:w-[80%] xl:w-[70%] mx-auto'>
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                    {/* Free Plan */}
                    {organized.free && (
                        <NewProductCard
                            planType="free"
                            productName={organized.free.product_name}
                            productDescription={organized.free.product_description}
                            creidts={organized.free.display_credits}
                            features={FEATURES_MAP['Free']}
                            icon={<Image src={kaviaLogo} alt="Kavia Logo" className="w-6 h-6" />}
                            onClick={() => router.push('/')}
                            isLoggedIn={isLoggedIn}
                            currentPlan={false}
                            currentProductName={currentProductName}
                            Restrict={currentPrice}
                            dynamicCredits={organized.free.display_credits}
                        />
                    )}

                    {/* Premium Plans */}
                    {organized.premium.length > 0 && (
                        <NewProductCard
                            planType="premium"
                            productName="Premium"
                            productDescription="Best Plan with Advanced Features for Individual Users"
                            credits={organized.premium[0].display_credits}
                            prices={organized.premium.map(p => p.price)}
                            selectedPrice={selectedPrices.premium}
                            onPriceSelect={(price) => {
                                const selectedProduct = organized.premium.find(p => p.price === price);
                                setSelectedPrices(prev => ({
                                    ...prev,
                                    premium: price,
                                    premiumCredits: selectedProduct.display_credits,
                                    premiumplan: selectedProduct.product_name.replace("Premium ", "")

                                }));
                            }}
                            features={FEATURES_MAP['Premium']}
                            icon={<Image src={kaviaLogo} alt="Kavia Logo" className="w-6 h-6" />}
                            onClick={() => {
                                if (isLoggedIn) {
                                    const selectedProduct = organized.premium.find(
                                        p => p.price === selectedPrices.premium
                                    );
                                    handleProductClick(selectedProduct.price_id);
                                } else {
                                    setIsModalOpen(true);
                                }
                            }}
                            isLoggedIn={isLoggedIn}
                            currentPlan={selectedPrices.premiumplan}
                            dynamicCredits={selectedPrices.premiumCredits}
                            currentProductName={currentProductName}
                            Restrict={currentPrice}

                        />
                    )}

                    {/* Teams Plans */}
                    {organized.teams.length > 0 && (
                        <NewProductCard
                            planType="teams"
                            productName="Teams"
                            productDescription="Best Plan for Collaborative Teams"
                            credits={organized.teams[0].credits}
                            prices={organized.teams.map(p => p.price)}
                            selectedPrice={selectedPrices.teams}
                            onPriceSelect={(price) => {
                                const selectedProduct = organized.teams.find(p => p.price === price);
                                setSelectedPrices(prev => ({
                                    ...prev,
                                    teams: price,
                                    teamsCredits: selectedProduct.credits,
                                    teamsplan: selectedProduct.product_name.replace("Teams ", "")
                                }));
                            }}
                            features={FEATURES_MAP['Teams']}
                            icon={<Image src={kaviaLogo} alt="Kavia Logo" className="w-6 h-6" />}
                            onClick={() => {
                                if (isLoggedIn) {
                                    const selectedProduct = organized.teams.find(
                                        p => p.price === selectedPrices.teams
                                    );
                                    handleProductClick(selectedProduct.price_id);
                                } else {
                                    setIsModalOpen(true);
                                }
                            }}
                            isLoggedIn={isLoggedIn}
                            currentPlan={selectedPrices.teamsplan}
                            dynamicCredits={selectedPrices.teamsCredits}
                            currentProductName={currentProductName}
                            Restrict={currentPrice}
                        />
                    )}
                    <div className="bg-gradient-to-tr from-card from-70% to-primary/10 border border-orange-500/50 rounded-lg p-8 flex-1 relative">
                        {/* Title and Logo Row */}
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-white text-lg font-semibold">Enterprise</h3>
                            <Image src={kaviaLogo} alt="Kavia Logo" className="w-6 h-6" />
                        </div>

                        {/* Description */}
                        <p className="font-inter text-body-sm font-light min-h-12 max-w-60 text-wrap text-muted-foreground">
                            Best for Individual with Advanced Features
                        </p>

                        {/* CTA Button */}
                        <div className="text-center mb-8">
                            <button
                                onClick={handleContactUs}
                                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground  font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                            >
                                Contact Us
                            </button>
                        </div>

                        {/* Features List */}
                        <div className="space-y-3 text-sm">
                            {[
                                "Create Web/Mobile Application",
                                "Figma Design Import",
                                "One Click Deployment",
                                "External Integrations",
                                "Query Public Codebase",
                                "Private Projects",
                                "Integrate with GitHub",
                                "Plan and Build Scalable Projects",
                                "Ingest & Modify Existing Codebase",
                                "Multi-User Collaboration",
                                "Organization GitHub Access",
                                "Team Based Access Control",
                                "Custom/Enterprise Support",
                                "Custom LLM and Workflow Configurations",
                                "Custom AWS Deployment"
                            ].map((feature, idx) => (
                                <div key={idx} className="flex items-center text-gray-300">
                                    <span className="text-orange-500 mr-3">✓</span>
                                  <span className="text-body-sm text-card-foreground">{feature}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                </div>
            </div>
        );
    };

    return (
        <ThemeTransition delay={50} duration={200}>
            <div className='min-h-screen bg-background overflow-hidden pt-16 pb-8 flex flex-col relative'>
                {/* Background gradient effect */}
                <div className="absolute -top-80 -right-40 w-1/2 h-96 rounded-full bg-gradient-to-br from-primary/60 to-primary/30 blur-3xl opacity-40" />
                <div className="absolute -bottom-80 -left-40 w-1/2 h-96 rounded-full bg-gradient-to-tr from-primary/40 to-primary/20 blur-3xl opacity-30" />

                {/* Header with logo and close button */}
                <div className='fixed top-0 left-0 right-0 flex items-center justify-between gap-2 p-8 w-full z-10 backdrop-blur-sm bg-background/80'>
                    <KaviaLongLogo />
                    <button
                        className="bg-muted/20 hover:bg-muted/30 p-2 rounded-full transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50"
                        onClick={() => router.back()}
                    >
                        <X className="h-6 w-6 text-foreground" />
                        <span className="sr-only">Close</span>
                    </button>
                </div>

                {/* Main content */}
                <div className='flex flex-col items-center flex-grow relative pt-8'>
                    <div className='text-center mb-[5vh] md:mb-[8vh] w-[90%] md:w-[80%]'>
                        <h1 className='font-inter text-heading-2 md:text-heading-1 leading-tight font-semibold text-foreground mb-[2vh]'>
                            Choose Your Plan
                        </h1>
                        {currentProductName ? (
                            <p className='font-inter text-body md:text-body-lg leading-relaxed text-muted-foreground'>
                                Current Plan: {currentProductName}
                            </p>
                        ) : (
                            <p className='font-inter text-body md:text-body-lg leading-relaxed text-muted-foreground'>
                                Select the perfect plan for your needs
                            </p>
                        )}
                    </div>

                    {renderProductCards()}
                </div>

                {/* Login/Signup Modal */}
                {isModalOpen && (
                    <LoginSignupModal
                        handleClick={(path) => router.push(path)}
                        setIsModalOpen={setIsModalOpen}
                    />
                )}
            </div>
        </ThemeTransition>
    )
}

export default Page