"use client";
import React, { useState, useEffect, useContext, useRef } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams, usePathname } from 'next/navigation';
import { getPastMaintenanceTasks, updateTask, controlTask } from "@/utils/batchAPI";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useWebSocket } from '@/components/Context/WebsocketContext';
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { GitBranch, Folder, ClipboardIcon, CheckIcon, Info, X, RefreshCw, AlertTriangle } from 'lucide-react';
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import RepositoryDetailsModal from '@/components/Modal/RepositoryModal';
import { getkginfo } from '@/utils/gitAPI';
import { formatUTCToLocal, formatDateTime } from "@/utils/datetime";
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ConfirmationModal } from '@/components/CodeGenrationPanel/LoadingComponents';
import Cookies from "js-cookie";
import { getRepositoryField } from "@/utils/repositoryAPI";

const CodeMaintenanceInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-primary-50 rounded-md p-2 border-l-2 border-primary-400 mb-2">
      <div className="text-primary-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">About Code Maintenance</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Maintain and update existing code. Select repositories and perform maintenance tasks
        </i>
      </div>
    </div>
  );
};

const RepositoryCard = ({ repository, onSelect, isSelected }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(repository.git_url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm p-3 flex flex-col gap-2 hover:shadow-md transition-all duration-200 ${
      isSelected
        ? 'border border-primary-400 bg-primary-50/30'
        : 'border border-gray-200 hover:border-primary-300'
    }`}>
      <div className="flex items-start gap-3">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onSelect(repository)}
          className="w-4 h-4 flex-shrink-0 text-primary-600 rounded border-gray-300 focus:ring-primary-500 mt-0.5"
        />
        <div className="min-w-0 flex-1">
          <h3 className={`typography-body font-weight-semibold mb-1 leading-tight ${
            isSelected ? 'text-primary-700' : 'text-gray-800'
          }`}>
            {repository.repository_name}
          </h3>
          <p className="typography-body-sm mb-2 text-gray-500">{repository.service}</p>
          <span className="inline-block px-2 py-0.5 typography-body-sm rounded-full bg-primary-100 text-primary-700">
            {repository.service == "github" ? repository.repo_type : "files"}
          </span>
        </div>
      </div>

      <div className="border-t pt-3">
        <h4 className="text-gray-600 font-weight-medium typography-body-sm mb-2">Connected Repository</h4>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 typography-body-sm bg-gray-50 rounded-md p-2">
              {repository.service !== "localFiles" ?
                <>
                  <span className="flex-1 truncate text-gray-600 hover:text-clip">
                    {repository.git_url.replace("https://","")}
                  </span>
                  <button
                    className={`flex-shrink-0 p-1 rounded-md transition-colors ${
                      copied
                        ? 'bg-green-50 text-green-600'
                        : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={copyToClipboard}
                    aria-label="Copy to clipboard"
                  >
                    {copied ?
                      <CheckIcon size={12} className="transition-all duration-200" /> :
                      <ClipboardIcon size={12} className="transition-all duration-200" />
                    }
                  </button>
                </>
                :
                <>
                  <Folder size={14} className="text-gray-500 flex-shrink-0" />
                </>
              }
            </div>
            {repository.service !== "localFiles" &&
              <div className="flex items-center gap-2 typography-body-sm text-gray-500 mt-1 max-w-full">
                <GitBranch size={12} className="flex-shrink-0" />
                <span className="truncate" title={repository.branches[0]?.name || 'No branch'}>
                  {repository.branches[0]?.name || 'No branch'}
                </span>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  );
};

const RepositoryList = ({ repositories, isLoading, onSelect, selectedRepos }) => {
  if (isLoading) {
    return (
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!repositories || repositories.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No repositories available</p>
        <p className="typography-body-sm text-gray-400 mt-2">Configure repositories to get started</p>
      </div>
    );
  }

  return (
    <div className="grid gap-2 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {repositories.map((repo) => (
        <RepositoryCard
          key={repo.repo_id}
          repository={repo}
          onSelect={onSelect}
          isSelected={selectedRepos.all_repositories || selectedRepos.repositories.includes(repo.repo_id)}
        />
      ))}
    </div>
  );
};

// Enhanced Manifest Preview Modal Component
const ManifestPreviewModal = ({ manifest, onProceed, onCancel, isLoading, projectId, onManifestUpdate, logInfo }) => {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [updatedManifest, setUpdatedManifest] = useState(manifest || '');
  const [controller, setController] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isStartingMaintenance, setIsStartingMaintenance] = useState(false);
  const [maintenanceProgress, setMaintenanceProgress] = useState('');
  const { showAlert } = useContext(AlertContext);
  
  const hasNoManifest = !manifest || manifest.trim() === '';
  
  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, [controller]);

  useEffect(() => {
    setUpdatedManifest(manifest || '');
    setHasUnsavedChanges(false);
  }, [manifest]);

  useEffect(() => {
    if (isStartingMaintenance && logInfo) {
      setMaintenanceProgress(logInfo);
    }
  }, [logInfo, isStartingMaintenance]);

  const handleManifestChange = (e) => {
    setUpdatedManifest(e.target.value);
    setHasUnsavedChanges(e.target.value !== (manifest || ''));
  };

  const handleSaveManifest = async () => {
    if (!updatedManifest.trim()) {
      showAlert('Manifest data is required', 'error');
      return;
    }

    setIsSaving(true);
    
    try {
      let parsedData;
      try {
        if (updatedManifest.includes('\n') && updatedManifest.includes(':')) {
          parsedData = updatedManifest;
        } else {
          parsedData = JSON.parse(updatedManifest);
        }
      } catch (parseError) {
        parsedData = updatedManifest;
      }

      const { updateRepositoryField } = await import("@/utils/repositoryAPI");
      await updateRepositoryField(projectId, 'project_manifest', parsedData);

      showAlert('Project manifest saved successfully!', 'success');
      setHasUnsavedChanges(false);
      setIsEditMode(false);
      
      if (typeof onManifestUpdate === 'function') {
        onManifestUpdate(updatedManifest);
      }
    } catch (error) {
      console.error('Error updating manifest:', error);
      showAlert(error.message || 'Failed to update manifest', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateManifest = async () => {
    setIsRegenerating(true);
    setGenerationProgress('Initializing manifest generation...');
    setUpdatedManifest('');

    const abortController = new AbortController();
    setController(abortController);

    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}/repository/generate_manifest/${projectId}/`;
      
      await fetchEventSource(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${Cookies.get('idToken')}`,
          'Content-Type': 'application/json',
          'X-Tenant-Id': Cookies.get('tenant_id'),
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        signal: abortController.signal,
        openWhenHidden: true,
        onopen: (response) => {
          if (response.status !== 200) {
            showAlert('Failed to connect to manifest generation service', 'error');
            setIsRegenerating(false);
            setGenerationProgress('');
            abortController.abort();
            return Promise.reject(new Error('Failed to connect'));
          }
          return Promise.resolve();
        },
        onmessage: (event) => {
          try {
            const data = JSON.parse(event.data);
            
            switch (data.status) {
              case 'starting':
              case 'progress':
                setGenerationProgress(data.message || 'Processing...');
                break;
                
              case 'streaming':
                if (data.content) {
                  setUpdatedManifest(prev => prev + data.content);
                }
                if (data.partial_manifest) {
                  setGenerationProgress('Streaming manifest content...');
                }
                break;
                
              case 'complete':
                setGenerationProgress('Generation complete!');
                if (data.manifest && data.manifest.content) {
                  setUpdatedManifest(data.manifest.content);
                }
                setTimeout(() => {
                  setIsRegenerating(false);
                  setGenerationProgress('');
                  abortController.abort();
                  setHasUnsavedChanges(true);
                  showAlert('Manifest generated successfully! Please save to apply changes.', 'success');
                }, 1000);
                break;
                
              case 'yaml_ready':
                if (data.yaml_content) {
                  setUpdatedManifest(data.yaml_content);
                }
                break;
                
              case 'error':
                showAlert(data.message || 'Failed to generate manifest', 'error');
                setGenerationProgress('');
                setIsRegenerating(false);
                abortController.abort();
                break;
                
              default:
                setGenerationProgress(data.message || 'Processing...');
            }
          } catch (parseError) {
            console.error('Error parsing manifest generation response:', parseError);
            showAlert('Error processing server response', 'error');
            setIsRegenerating(false);
            setGenerationProgress('');
            abortController.abort();
          }
        },
        onerror: (error) => {
          console.error('Manifest generation error:', error);
          showAlert('Error during manifest generation', 'error');
          setIsRegenerating(false);
          setGenerationProgress('');
          abortController.abort();
          return null;
        },
        onclose: () => {
          setController(null);
          if (isRegenerating) {
            setIsRegenerating(false);
            setGenerationProgress('');
          }
        }
      });
    } catch (error) {
      console.error('Error starting manifest generation:', error);
      showAlert(error.message || 'Failed to start manifest generation', 'error');
      setIsRegenerating(false);
      setGenerationProgress('');
      abortController.abort();
    }
  };

  const handleStopGeneration = () => {
    if (controller) {
      controller.abort();
    }
    setIsRegenerating(false);
    setGenerationProgress('');
  };

  const handleDiscardChanges = () => {
    setUpdatedManifest(manifest || '');
    setHasUnsavedChanges(false);
    setIsEditMode(false);
  };

  const handleProceedWithoutManifest = () => {
    setIsStartingMaintenance(true);
    setMaintenanceProgress('Starting code maintenance without manifest...');
    onProceed(null);
  };

  const handleProceedWithManifest = () => {
    setIsStartingMaintenance(true);
    setMaintenanceProgress('Starting code maintenance...');
    onProceed(updatedManifest || manifest);
  };

  const handleModalCancel = () => {
    if (isStartingMaintenance) return;
    onCancel();
  };
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleModalCancel} />
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] flex flex-col border border-gray-200">
        <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4 bg-gradient-to-r from-primary-50 to-primary-50">
          <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
            {hasNoManifest ? 'Project Manifest Setup' : 'Project Manifest Review'}
            {isEditMode && ' - Editor'}
            {isStartingMaintenance && ' - Starting Code Maintenance...'}
          </h2>
          <div className="flex items-center gap-3">
            <button
              className="text-gray-400 hover:text-gray-600 rounded-md p-2 hover:bg-white/80 transition-colors"
              onClick={handleModalCancel}
              aria-label="Close"
              disabled={isRegenerating || isStartingMaintenance}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="px-6 py-4 flex-1 overflow-y-auto">
          {isStartingMaintenance && (
            <div className="mb-6 p-6 text-center">
              <div className="flex flex-col items-center gap-4">
                <RefreshCw className="w-8 h-8 animate-spin text-primary-600" />
                <span className="typography-body text-gray-700">
                  Starting code maintenance...
                </span>
              </div>
            </div>
          )}

          {!isStartingMaintenance && hasNoManifest ? (
            <div className="mb-6">
              <div className="flex items-start gap-3 p-4 bg-amber-50 rounded-lg border border-amber-200 mb-4">
                <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="typography-body font-weight-semibold text-amber-800 mb-2">
                    No Project Manifest Found
                  </h3>
                  <p className="typography-body-sm text-amber-700 mb-3">
                    A project manifest helps understand your project structure and guides the code maintenance process more effectively. 
                    Without it, the maintenance process may be less targeted and preview will not be started automatically.
                  </p>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="typography-body-sm text-amber-700">Generate a manifest for better results</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="typography-body-sm text-amber-700">Or proceed without manifest (preview won't start automatically)</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="typography-body font-weight-medium text-gray-800">
                    Create Project Manifest
                  </h3>
                  <div className="flex items-center gap-2">
                    {!isRegenerating && (
                      <button
                        onClick={handleGenerateManifest}
                        className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                      >
                        Generate Manifest
                      </button>
                    )}
                    {isRegenerating && (
                      <button
                        onClick={handleStopGeneration}
                        className="px-3 py-1 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <X className="w-3 h-3" />
                        Stop
                      </button>
                    )}
                  </div>
                </div>
                
                {isRegenerating && generationProgress && (
                  <div className="mb-4 p-3 bg-gradient-to-r from-primary-50 to-primary-50 rounded border border-primary-200">
                    <div className="flex items-center gap-3">
                      <RefreshCw className="w-4 h-4 animate-spin text-primary-600" />
                      <span className="typography-body-sm text-primary-800 font-weight-medium">
                        {generationProgress}
                      </span>
                    </div>
                  </div>
                )}
                
                <textarea
                  value={updatedManifest}
                  onChange={handleManifestChange}
                  className="w-full min-h-[400px] max-h-[500px] p-3 border rounded font-mono text-sm bg-white resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Generate a manifest or manually enter project manifest data in YAML or JSON format..."
                  disabled={isRegenerating}
                />
              </div>

              {updatedManifest.trim() !== '' && (
                <div className="mt-3 flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <CheckIcon className="w-4 h-4 text-green-600" />
                    <span className="typography-body-sm text-green-800 font-medium">
                      Manifest is ready to save and use for code maintenance.
                    </span>
                  </div>
                  <button
                    onClick={handleSaveManifest}
                    disabled={isSaving}
                    className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                      isSaving
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white shadow-sm'
                    }`}
                  >
                    {isSaving ? (
                      <RefreshCw className="w-3 h-3 animate-spin" />
                    ) : (
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    )}
                    <span>{isSaving ? 'Saving...' : 'Save Manifest'}</span>
                  </button>
                </div>
              )}
            </div>
          ) : !isStartingMaintenance ? (
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="typography-body font-weight-medium text-green-700">
                  Project manifest found and will be used for code maintenance
                </span>
              </div>
              <p className="typography-body-sm text-gray-600 mb-4">
                The following manifest will be used to understand your project structure and guide the code maintenance process.
              </p>

              {isRegenerating && generationProgress && (
                <div className="mb-4 p-4 bg-gradient-to-r from-primary-50 to-primary-50 rounded-lg border border-primary-200">
                  <div className="flex items-center gap-3">
                    <RefreshCw className="w-5 h-5 animate-spin text-primary-600" />
                    <span className="typography-body text-primary-800 font-weight-medium">
                      {generationProgress}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="typography-body font-weight-medium text-gray-800">
                    {isEditMode ? 'Edit Manifest' : 'Current Manifest'}
                  </h3>
                  <div className="flex items-center gap-2">
                    {!isRegenerating && !isEditMode && (
                      <>
                        <button
                          onClick={() => setIsEditMode(true)}
                          className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                        >
                          Edit Manifest
                        </button>
                        <button
                          onClick={handleGenerateManifest}
                          className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                        >
                          Regenerate Manifest
                        </button>
                      </>
                    )}
                    {isEditMode && (
                      <button
                        onClick={handleDiscardChanges}
                        className="typography-body-sm text-gray-600 hover:text-gray-700 underline font-weight-medium"
                      >
                        Cancel
                      </button>
                    )}
                    {isRegenerating && (
                      <button
                        onClick={handleStopGeneration}
                        className="px-3 py-1 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <X className="w-3 h-3" />
                        Stop
                      </button>
                    )}
                  </div>
                </div>
                
                {isEditMode ? (
                  <textarea
                    value={updatedManifest}
                    onChange={handleManifestChange}
                    className="w-full min-h-[500px] max-h-[600px] p-3 border rounded font-mono text-sm bg-white resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter project manifest data in YAML or JSON format..."
                    disabled={isRegenerating}
                  />
                ) : (
                  <pre className="typography-body-sm font-mono text-gray-700 bg-white rounded border p-3 overflow-x-auto whitespace-pre-wrap min-h-[500px] max-h-[600px] overflow-y-auto">
                    {updatedManifest || manifest}
                  </pre>
                )}
              </div>

              {hasUnsavedChanges && (
                <div className="mt-3 flex items-center justify-between p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="typography-body-sm text-amber-800 font-medium">
                      {isEditMode ? 'You have unsaved changes.' : 'Generated manifest is ready to save.'}
                    </span>
                  </div>
                  {!isEditMode && hasUnsavedChanges && (
                    <button
                      onClick={handleSaveManifest}
                      disabled={isSaving}
                      className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                        isSaving
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-amber-600 hover:bg-amber-700 text-white shadow-sm'
                      }`}
                    >
                      {isSaving ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      )}
                      <span>{isSaving ? 'Saving...' : 'Save Now'}</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          ) : null}
        </div>
        
        <div className="flex justify-end gap-3 px-6 py-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-primary-50/30">
          <button
            type="button"
            className="px-6 py-2 typography-body-sm bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm"
            onClick={handleModalCancel}
            disabled={isLoading || isRegenerating || isStartingMaintenance}
          >
            Cancel
          </button>
          
          {!isStartingMaintenance && hasNoManifest && updatedManifest.trim() === '' && (
            <button
              type="button"
              className={`px-6 py-2 typography-body-sm rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isLoading || isRegenerating
                  ? "bg-gray-300 cursor-not-allowed text-gray-500"
                  : "bg-amber-600 hover:bg-amber-700 text-white"
              }`}
              onClick={handleProceedWithoutManifest}
              disabled={isLoading || isRegenerating}
              title="Proceed without manifest - preview will not start automatically"
            >
              <AlertTriangle className="w-4 h-4" />
              <span>Proceed Without Manifest</span>
            </button>
          )}
          
          {!isStartingMaintenance && hasUnsavedChanges && (
            <button
              type="button"
              onClick={handleSaveManifest}
              disabled={isSaving}
              className={`px-6 py-2 typography-body-sm rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isSaving
                  ? "bg-amber-300 cursor-not-allowed text-amber-700"
                  : "bg-amber-600 hover:bg-amber-700 text-white"
              }`}
            >
              {isSaving ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              )}
              <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
            </button>
          )}
          
          {!isStartingMaintenance && (updatedManifest.trim() !== '' || !hasNoManifest) && (
            <button
              type="button"
              className={`px-6 py-2 typography-body-sm text-white rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isLoading || isRegenerating || hasUnsavedChanges
                  ? "bg-primary-300 cursor-not-allowed"
                  : "bg-primary-600 hover:bg-primary-700"
              }`}
              onClick={handleProceedWithManifest}
              disabled={isLoading || isRegenerating || hasUnsavedChanges}
              title={hasUnsavedChanges ? "Please save your changes before proceeding" : ""}
            >
              {isLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
              {isLoading ? 'Starting...' : 'Proceed with Code Maintenance'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const CodeMaintenancePage = () => {
  const [pastTasks, setPastTasks] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitiating, setIsInitiating] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [kgInfo, setKgInfo] = useState({ details: [] });
  const [showRepositoryModal, setShowRepositoryModal] = useState(false);
  const [selectedContainerId, setSelectedContainerId] = useState(null);
  const [selectedRepos, setSelectedRepos] = useState({
    all_repositories: true,
    repositories: []
  });
  const [isAllSelected, setIsAllSelected] = useState(true);
  const [isRepoListLoading, setIsRepoListLoading] = useState(false);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [sessionsTableData, setSessionsTableData] = useState([]);

  // Code Maintenance Handler state
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [activeSessions, setActiveSessions] = useState([]);
  const [showActiveSessionsModal, setShowActiveSessionsModal] = useState(false);
  const [isStoppingTasks, setIsStoppingTasks] = useState(false);
  const [logInfo, setLogInfo] = useState('');
  const [controller, setController] = useState(null);
  const [plannedTaskId, setPlannedTaskId] = useState(null);
  const hasGeneratedCode = useRef(false);

  // Manifest related state
  const [manifestData, setManifestData] = useState(null);
  const [isCheckingManifest, setIsCheckingManifest] = useState(false);
  const [showManifestPreview, setShowManifestPreview] = useState(false);

  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { showAlert } = useContext(AlertContext);
  const { isVisible, setIsVisible, setIsCodeMaintenance, setCurrentIframeUrl } = useCodeGeneration();
  const { connectToSession, disconnectFromSession, getConnection, refreshRepos, setRefreshRepos } = useWebSocket();
  const projectId = params.projectId;

  const checkProjectManifest = async () => {
    try {
      setIsCheckingManifest(true);
      const response = await getRepositoryField(projectId, 'project_manifest');
      
      if (response.field_value) {
        const manifestContent = typeof response.field_value === 'string' 
          ? response.field_value 
          : JSON.stringify(response.field_value, null, 2);
        setManifestData(manifestContent);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking manifest:', error);
      return false;
    } finally {
      setIsCheckingManifest(false);
    }
  };

  const proceedWithCodeMaintenance = async (updatedManifest = null) => {
    const currentManifest = updatedManifest || manifestData;
    
    setIsInitiating(true);
    setLogInfo('');
    setIsGeneratingCode(true);
    hasGeneratedCode.current = false;
    setIsCompleted(false);
    setIsCodeMaintenance(true);
    sessionStorage.setItem("isCodeMaintenance", "true");

    let storedSelectedRepos = sessionStorage.getItem("selectedRepos");
    if (!storedSelectedRepos) {
      const filteredRepos = selectedRepos.all_repositories
        ? selectedRepos.all_repositories
        : selectedRepos.repositories.filter(repo => repo.repo_id);
      sessionStorage.setItem("selectedRepos", JSON.stringify(filteredRepos));
    }

    const abortController = new AbortController();
    setController(abortController);

    try {
      if (!hasGeneratedCode.current) {
        hasGeneratedCode.current = true;

        let url = `${process.env.NEXT_PUBLIC_API_URL}/batch/start_code_maintenance/${projectId}/`;

        fetchEventSource(
          url,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Cookies.get('idToken')}`,
              'Content-Type': 'application/json',
              'X-Tenant-Id': Cookies.get('tenant_id'),
            },
            body: JSON.stringify({
              request_timestamp: new Date().toISOString(),
              selectedrepos: selectedRepos.all_repositories
                ? { all_repositories: true }
                : { all_repositories: false, repositories: selectedRepos.repositories },
            }),
            signal: abortController.signal,
            openWhenHidden: true,
            onopen: (response) => {
              if (response.status !== 200) {
                showAlert("Something went wrong!", "error");
                abortController.abort();
                setShowManifestPreview(false);
                confirmClose();
              }
              return Promise.resolve();
            },
            onmessage: (event) => {
              try {
                const data = JSON.parse(event.data);

                if (data.task_id) {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("task_id", data.task_id);
                  router.push(`${pathname}?${newSearchParams.toString()}`);
                }

                if (data.planned_job_id) {
                  setPlannedTaskId(data.planned_job_id);
                  connectToSession(data.planned_job_id);
                }

                if (data.message) {
                  setLogInfo(data.message);
                }

                if (data.end === true) {
                  setShowManifestPreview(false);
                  
                  if (data.task_id) {
                    setIsCompleted(true);
                    setTimeout(() => {
                      if (data.iframe) {
                        setCurrentIframeUrl(data.iframe);
                      }
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("task_id", data.task_id);
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                      setIsVisible(true);
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                      fetchPastTasks();
                    }, 3000);
                  } else if (data.error && data.error.includes("Maximum number of active code maintenance sessions reached")) {
                    try {
                      const sessions = JSON.parse(data.active_sessions);
                      setActiveSessions(sessions);
                      setShowActiveSessionsModal(true);
                      abortController.abort();
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                    } catch (parseError) {
                      showAlert(data.error, "error");
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                    }
                  } else {
                    showAlert(data.message || data.error || "Unable to start task", "error");
                    setIsGeneratingCode(false);
                    setIsInitiating(false);
                  }
                  abortController.abort();
                }
              } catch (error) {
                abortController.abort();
                setShowManifestPreview(false);
                showAlert("Error processing response", "error");
                setIsGeneratingCode(false);
                setIsInitiating(false);
              }
            },
            onerror: (error) => {
              showAlert("Error in code maintenance: " + error, "error");
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              abortController.abort();
              setShowManifestPreview(false);
              setIsGeneratingCode(false);
              setIsInitiating(false);
              hasGeneratedCode.current = false;
              return null;
            },
            onclose: () => {
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              if (!isCompleted) {
                setShowManifestPreview(false);
              }
              setIsGeneratingCode(false);
              setController(null);
              setIsInitiating(false);

              if (!isCompleted) {
                hasGeneratedCode.current = false;
              }

              fetchPastTasks();
            }
          }
        );
      }
    } catch (error) {
      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
      abortController.abort();
      setShowManifestPreview(false);
      showAlert("Failed to start code maintenance", "error");
      setIsGeneratingCode(false);
      setIsInitiating(false);
      hasGeneratedCode.current = false;
    }
  };

  const calculateDuration = (startTime, endTime) => {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end.getTime() - start.getTime();
      const seconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      const weeks = Math.floor(days / 7);
      const years = Math.floor(days / 365.25);

      const remainingWeeks = Math.floor((days % 365.25) / 7);
      const remainingDays = Math.floor(days % 7);
      const remainingHours = Math.floor(hours % 24);
      const remainingMinutes = Math.floor(minutes % 60);
      const remainingSeconds = Math.floor(seconds % 60);

      if (years >= 1) {
        return `${years}y ${remainingWeeks}w`;
      } else if (weeks >= 1) {
        return `${weeks}w ${remainingDays}d`;
      } else if (days >= 1) {
        return `${days}d ${remainingHours}h`;
      } else if (hours >= 1) {
        return `${hours}h ${remainingMinutes}m`;
      } else if (minutes >= 1) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `<1m`;
      }
    } catch (error) {
      return '0';
    }
  };

  const handleFieldUpdate = async (rowId, field, value) => {
    try {
      await updateTask(rowId, { [field]: value });
      const updatedData = pastTasks.map(item =>
        item._id === rowId ? { ...item, [field]: value } : item
      );
      setPastTasks(updatedData);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const handleStartMaintenance = async () => {
    if (!selectedRepos.all_repositories && selectedRepos.repositories.length === 0) {
      showAlert("Please select at least one repository to start code maintenance session", "error");
      return;
    }

    if (kgInfo?.detail === "No repository is found") {
      showAlert("No repositories found. Please configure repositories first.", "error");
      return;
    }

    const hasManifest = await checkProjectManifest();
    setShowManifestPreview(true);
  };

  const handleClose = () => setShowConfirmModal(true);

  const confirmClose = () => {
    if (controller) controller.abort();
    setShowConfirmModal(false);
    setIsGeneratingCode(false);
    setIsInitiating(false);
    hasGeneratedCode.current = false;
    setIsVisible(false);
    setCurrentIframeUrl(null);
    setPlannedTaskId(null);
  };

  const cancelClose = () => setShowConfirmModal(false);

  const handleStopTask = async (taskId) => {
    try {
      await controlTask(taskId, "stop", projectId);
      setActiveSessions(prevSessions => prevSessions.filter(session => session.job_id !== taskId));
      showAlert(`Successfully stopped task ${taskId}`, "success");
      setPlannedTaskId(null);
      if (activeSessions.length <= 1) {
        setShowActiveSessionsModal(false);
        setIsInitiating(false);
      }
    } catch (error) {
      showAlert(`Failed to stop task: ${error.message}`, "error");
    }
  };

  const handleStopAllTasks = async () => {
    try {
      setIsStoppingTasks(true);
      await Promise.all(
        activeSessions.map(session =>
          controlTask(session.job_id, "stop", projectId)
            .catch(error => { })
        )
      );
      showAlert("Successfully stopped all tasks", "success");
      setShowActiveSessionsModal(false);
      setIsInitiating(false);
    } catch (error) {
      showAlert(`Failed to stop all tasks: ${error.message}`, "error");
    } finally {
      setIsStoppingTasks(false);
    }
  };

  const fetchKgInfo = async () => {
    try {
      setIsRepoListLoading(true);
      const data = await getkginfo(projectId, undefined, true);
      setKgInfo(data);
      
      if (selectedRepos.all_repositories || selectedRepos.repositories.length > 0) {
        if (selectedRepos.all_repositories) {
          setSelectedRepos({
            all_repositories: true,
            repositories: []
          });
        } else {
          const validRepoIds = data.details?.map(repo => repo.repo_id) || [];
          const filteredRepos = selectedRepos.repositories.filter(repoId =>
            validRepoIds.includes(repoId)
          );

          setSelectedRepos({
            all_repositories: false,
            repositories: filteredRepos
          });
        }
      }
    } catch (error) {
      showAlert("Failed to fetch repository information", "error");
    } finally {
      setIsRepoListLoading(false);
    }
  };

  const handleRepoSelect = (repo) => {
    setSelectedRepos(prev => {
      if (prev.all_repositories) {
        const allRepoIds = kgInfo.details.map(r => r.repo_id);
        return {
          all_repositories: false,
          repositories: allRepoIds.filter(id => id !== repo.repo_id)
        };
      } else {
        const newRepos = prev.repositories.includes(repo.repo_id)
          ? prev.repositories.filter(id => id !== repo.repo_id)
          : [...prev.repositories, repo.repo_id];

        const allSelected = newRepos.length === kgInfo.details.length;
        return {
          all_repositories: allSelected,
          repositories: allSelected ? [] : newRepos
        };
      }
    });
    sessionStorage.setItem("selectedRepos", JSON.stringify(selectedRepos));
  };

  const handleSelectAll = () => {
    setSelectedRepos(prev => ({
      all_repositories: !prev.all_repositories,
      repositories: []
    }));
    setIsAllSelected(!selectedRepos.all_repositories);
    sessionStorage.setItem("selectedRepos", JSON.stringify(selectedRepos));
  };

  const handleConfigureRepository = (containerId) => {
    setSelectedContainerId(containerId);
    setShowRepositoryModal(true);
  };

  const handleRepositoryModalClose = () => {
    setShowRepositoryModal(false);
    fetchKgInfo();
    setRefreshRepos(true);
  };

  const handleSetPastTasks = (tasks, skip) => {
    let newTasks = [];
    newTasks.push(...tasks);
    setPastTasks(newTasks);
  };

  const format_task = (data) => {
    const tableData = data.map(task => {
      const startTime = task.start_time;
      const endTime = task.messages && task.messages.length > 0
        ? task.messages[task.messages.length - 1].timestamp
        : (task.status === 'RUNNING' ? new Date().toISOString() : startTime);

      return {
        session_name: task.session_name || 'Untitled Session',
        description: task.description || 'No description',
        messages_length: Array.isArray(task.messages) ? task.messages.length : 0,
        status: task.status,
        fullId: task._id,
        start_time: formatUTCToLocal(task.start_time),
        duration: `${calculateDuration(startTime, endTime)}`
      };
    });
    return tableData;
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    setIsHistoryLoading(true);
    try {
      const data = await getPastMaintenanceTasks(projectId, currentLimit, currentSkip);
      handleSetPastTasks(data.tasks, currentSkip);
      setTotalCount(data.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      showAlert("Failed to fetch past maintenance tasks", "error");
    } finally {
      setIsHistoryLoading(false);
    }
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const handleRowClick = (row) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("task_id", row.fullId);
    router.push(`${pathname}?${newSearchParams.toString()}`);
    setIsVisible(true);
  };

  const handleSort = (column, direction) => {
    const sortedData = [...sessionsTableData].sort((a, b) => {
      if (column === 'start_time') {
        const dateA = new Date(a.start_time);
        const dateB = new Date(b.start_time);
        return direction === 'asc' ? dateA - dateB : dateB - dateA;
      }
      return 0;
    });
    setSessionsTableData(sortedData);
  };

  const isStartButtonDisabled = () => {
    return isInitiating || 
           isCheckingManifest || 
           (!selectedRepos.all_repositories && selectedRepos.repositories.length === 0) || 
           kgInfo?.detail === "No repository is found";
  };

  useEffect(() => {
    setIsAllSelected(
      selectedRepos.repositories &&
      kgInfo?.details &&
      selectedRepos.repositories.length === kgInfo.details.length &&
      kgInfo.details.length > 0
    );
  }, [selectedRepos.repositories, kgInfo?.details]);

  useEffect(() => {
    fetchKgInfo();
  }, [projectId]);

  useEffect(() => {
    if (refreshRepos) {
      fetchKgInfo();
      setRefreshRepos(false);
    }
  }, [refreshRepos]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchKgInfo();
      }
    };

    const handleFocus = () => {
      fetchKgInfo();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  useEffect(() => {
    if (activeSessions.length > 0) {
      const formattedSessions = activeSessions.map(session => ({
        _id: session._id || '',
        job_id: session.job_id || '',
        fullId: session.job_id || '',
        session_name: session.session_name || `Session ${session.job_id.substring(0, 8)}`,
        status: session.status || 'UNKNOWN',
        start_time: formatDateTime(session.start_time, true),
        ip: session.ip || ''
      }));
      setSessionsTableData(formattedSessions);
    }
  }, [activeSessions]);

  useEffect(() => {
    fetchPastTasks();
  }, [projectId]);

  useEffect(() => {
    const taskId = searchParams.get('task_id');
    if (taskId) {
      setIsVisible(true);
    } else {
      fetchPastTasks();
    }
  }, [searchParams]);

  useEffect(() => {
    if (plannedTaskId) {
      const connection = getConnection(plannedTaskId);
      if (connection && connection.readyState === WebSocket.OPEN) {
        connection.send(JSON.stringify({
          type: 'client',
          task_id: plannedTaskId
        }));

        connection.onmessage = (event) => {
          setPlannedTaskId(null);
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set("task_id", plannedTaskId);
          router.push(`${pathname}?${newSearchParams.toString()}`);
          setIsVisible(true);
        };
      }
    }
  }, [plannedTaskId]);

  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
    };
  }, []);

  useEffect(() => {
    const handleRefresh = () => {
      fetchKgInfo();
    };

    window.addEventListener('refreshMaintenance', handleRefresh);
    return () => window.removeEventListener('refreshMaintenance', handleRefresh);
  }, []);

  useEffect(() => {
    const selectAllBtn = document.getElementById('maintenance-select-all-btn');
    const startSessionBtn = document.getElementById('maintenance-start-session-btn');

    // Only sync if we're on the maintenance tab
    const currentTab = new URLSearchParams(window.location.search).get('tab') || 'generation';
    if (currentTab !== 'maintenance') return;

    if (selectAllBtn) {
      const checkbox = selectAllBtn.querySelector('div > div');
      const text = selectAllBtn.querySelector('span');

      if (checkbox && text) {
        if (selectedRepos.all_repositories) {
          checkbox.classList.add('bg-primary', 'border-primary');
          checkbox.classList.remove('border-gray-400');
          checkbox.innerHTML = '<svg width="8" height="8" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.5 3L4 7.5L1.5 5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /></svg>';
          text.textContent = 'Deselect All';
        } else {
          checkbox.classList.remove('bg-primary', 'border-primary');
          checkbox.classList.add('border-gray-400');
          checkbox.innerHTML = '';
          text.textContent = 'Select All';
        }
      }

      selectAllBtn.onclick = handleSelectAll;
    }

    if (startSessionBtn) {
      if (!startSessionBtn.dataset.originalContent) {
        startSessionBtn.dataset.originalContent = startSessionBtn.innerHTML;
      }

      const isDisabled = isStartButtonDisabled();
      startSessionBtn.disabled = isDisabled || isInitiating || isCheckingManifest;

      if (isCheckingManifest) {
        startSessionBtn.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-3.5 w-3.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Checking Manifest...</span>
        `;
        startSessionBtn.classList.add('opacity-75', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-primary-600');
      } else if (isInitiating) {
        startSessionBtn.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-3.5 w-3.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Starting...</span>
        `;
        startSessionBtn.classList.add('opacity-75', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-primary-600');
      } else {
        startSessionBtn.innerHTML = startSessionBtn.dataset.originalContent || '<span>Start Session</span>';
        
        if (isDisabled) {
          startSessionBtn.classList.add('opacity-50', 'cursor-not-allowed');
          startSessionBtn.classList.remove('hover:bg-primary-600');
        } else {
          startSessionBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'opacity-75');
          startSessionBtn.classList.add('hover:bg-primary-600');
        }
      }

      if (!isInitiating && !isCheckingManifest) {
        startSessionBtn.onclick = handleStartMaintenance;
      } else {
        startSessionBtn.onclick = null;
      }
    }
  }, [selectedRepos, isInitiating, isCheckingManifest, kgInfo]);

  const activeSessionsHeaders = [
    {
      key: 'session_name',
      label: 'Session Name',
      render: (value, row) => (
        <span className="font-weight-medium">{value}</span>
      )
    },
    {
      key: 'job_id',
      label: 'Job ID',
      render: (value) => (
        <span className="typography-body-sm text-gray-600">{value.substring(0, 8)}...</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <span className={`px-2 py-1 typography-body-sm rounded-full ${value === 'RUNNING' ? 'bg-green-100 text-green-800' :
          value === 'SUBMITTED' ? 'bg-primary-100 text-primary-800' :
            'bg-gray-100 text-gray-800'
          }`}>
          {value}
        </span>
      )
    },
    {
      key: 'start_time',
      label: 'Start Time'
    },
    {
      key: 'action',
      label: 'Action',
      render: (_, row) => (
        <button
          className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded typography-body-sm"
          onClick={(e) => {
            e.stopPropagation();
            handleStopTask(row.job_id);
          }}
        >
          Stop
        </button>
      )
    }
  ];

  const tableData = format_task(pastTasks);

  return (
    <div className="flex flex-col">
      {/* Content */}
      <div className="bg-white">
        <div className="px-1 py-2">
          {/* Info Section */}
          <CodeMaintenanceInfo />

          {/* Repositories List */}
          <div className="pt-2">
            <RepositoryList
              repositories={kgInfo?.details || []}
              isLoading={isRepoListLoading}
              onSelect={handleRepoSelect}
              selectedRepos={selectedRepos}
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      <div className='p-4 flex-shrink-0'>
        {isVisible && <CodeGenerationModal />}
        {showRepositoryModal && (
          <RepositoryDetailsModal
            open={showRepositoryModal}
            onClose={handleRepositoryModalClose}
            projectId={projectId}
            containerId={selectedContainerId}
          />
        )}
      </div>

      {/* Enhanced Manifest Preview Modal with loading states */}
      {showManifestPreview && (
        <ManifestPreviewModal
          manifest={manifestData}
          onProceed={(updatedManifest) => proceedWithCodeMaintenance(updatedManifest)}
          onCancel={() => setShowManifestPreview(false)}
          isLoading={isInitiating}
          projectId={projectId}
          onManifestUpdate={setManifestData}
          logInfo={logInfo}
        />
      )}

      {showConfirmModal && (
        <ConfirmationModal
          onConfirm={confirmClose}
          onCancel={cancelClose}
        />
      )}

      {showActiveSessionsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <h2 className="typography-heading-4 text-black font-weight-semibold mb-4">Active Maintenance Sessions</h2>
              <p className="mb-4 text-red-500">
                Maximum number of active code maintenance sessions reached (limit: 3).
                Please stop some sessions and try again.
              </p>

              <div className="overflow-auto max-h-[50vh]">
                <TableComponent
                  data={sessionsTableData}
                  headers={activeSessionsHeaders}
                  sortableColumns={{ start_time: true, status: true }}
                  onSort={handleSort}
                  defaultSort={{ column: 'start_time', direction: 'desc' }}
                  emptyMessage="No active sessions found"
                />
              </div>

              <div className="mt-6 flex justify-end gap-4">
                <button
                  className="bg-gray-300 text-black hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 px-4 py-2 rounded"
                  onClick={() => {
                    setShowActiveSessionsModal(false);
                    setIsInitiating(false);
                    hasGeneratedCode.current = false;
                  }}
                >
                  Close
                </button>
                <button
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded flex items-center"
                  onClick={handleStopAllTasks}
                  disabled={isStoppingTasks}
                >
                  {isStoppingTasks ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : "Stop All Sessions"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CodeMaintenancePage;