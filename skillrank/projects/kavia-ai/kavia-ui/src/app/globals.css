@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;
@import "../styles/_app.css";
@import "../styles/components/FlashMessages.css";
@import "../styles/components/Toaster.css";
:root {
  --foreground-rgb: var(--foreground);
  --background-start-rgb: var(--muted);
  --background-end-rgb: var(--background);
  /* Font family */
  --font-family-primary: 'Inter', sans-serif;
  --font-family-code: 'Inter', monospace;
  /* Font sizes - Professional and readable */
  --font-size-xs: 0.75rem;     /* 12px - Small text, captions */
  --font-size-sm: 0.875rem;    /* 14px - Small body text */
  --font-size-base: 1rem;      /* 16px - Base body text */
  --font-size-md: 1rem;        /* 16px - Standard body text */
  --font-size-lg: 1.125rem;    /* 18px - Large body text */
  --font-size-xl: 1.25rem;     /* 20px - Small headings */
  --font-size-2xl: 1.5rem;     /* 24px - Medium headings */
  --font-size-3xl: 1.875rem;   /* 30px - Large headings */
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  /* Line heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  /* Letter spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  /* Code specific */
  --font-size-code: 0.875rem;  /* 14px - Code blocks and monospace */
  --line-height-code: 1.4;
  /* Using standard Tailwind spacing classes instead of custom variables */
}
@layer base {
  html {
    @apply antialiased scroll-smooth;
    height: 100%;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Inter', sans-serif;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    height: 100%;
    overflow-x: hidden;
  }

  /* Override all input focus styles to use only orange theme colors */
  input:focus,
  textarea:focus,
  select:focus {
    outline: none !important;
    border-color: hsl(var(--primary)) !important;
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
  }

  /* Focus styles for consistent theme */
  .modal input:focus,
  .modal textarea:focus,
  .modal select:focus,
  [role="dialog"] input:focus,
  [role="dialog"] textarea:focus,
  [role="dialog"] select:focus {
    outline: none !important;
    border-color: hsl(var(--primary)) !important;
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
    --tw-ring-color: hsl(var(--primary)) !important;
    --tw-ring-opacity: 0.2 !important;
  }

  /* Remove browser default focus styles */
  *:focus {
    outline: none !important;
  }

  /* Override for borderless textarea - completely remove all borders and shadows */
  .borderless-textarea,
  .borderless-textarea:focus,
  .borderless-textarea:hover,
  .borderless-textarea:active {
    border: none !important;
    border-width: 0 !important;
    border-style: none !important;
    border-color: transparent !important;
    outline: none !important;
    box-shadow: none !important;
    --tw-ring-shadow: none !important;
    --tw-ring-color: transparent !important;
    --tw-ring-opacity: 0 !important;
  }

  /* Ensure Tailwind focus ring classes use primary color */
  .focus\:ring-2:focus,
  .focus\:ring-1:focus {
    --tw-ring-color: hsl(var(--primary)) !important;
  }

  /* Override focus ring colors */
  .focus\:ring-primary-50:focus { --tw-ring-color: hsl(var(--primary-50)) !important; }
  .focus\:ring-primary-100:focus { --tw-ring-color: hsl(var(--primary-100)) !important; }
  .focus\:ring-primary-200:focus { --tw-ring-color: hsl(var(--primary-200)) !important; }
  .focus\:ring-primary-300:focus { --tw-ring-color: hsl(var(--primary-300)) !important; }
  .focus\:ring-primary-400:focus { --tw-ring-color: hsl(var(--primary-400)) !important; }
  .focus\:ring-primary:focus { --tw-ring-color: hsl(var(--primary-500)) !important; }
  .focus\:ring-primary-600:focus { --tw-ring-color: hsl(var(--primary)) !important; }
  .focus\:ring-primary-700:focus { --tw-ring-color: hsl(var(--primary-700)) !important; }
  .focus\:ring-primary-800:focus { --tw-ring-color: hsl(var(--primary-800)) !important; }
  .focus\:ring-primary-900:focus { --tw-ring-color: hsl(var(--primary-900)) !important; }

  /* Global responsive behavior */
  @media (max-width: 1023px) {
    body > *:not(.desktop-restriction-container) {
      @apply hidden;
    }
    .desktop-restriction-container {
      @apply flex fixed inset-0 z-50 bg-custom-bg-primary items-center justify-center w-screen h-screen overflow-hidden;
    }
  }
}
@layer components {
}
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  /* Right side padding for main content - reduced for more compact layout */
  .content-right-padding {
    padding-right: 0.75rem !important; /* 12px - more compact right side spacing */
  }
  /* Compact spacing utilities for better space utilization */
  .compact-padding {
    padding: 0.5rem !important;
  }
  .compact-margin {
    margin: 0.375rem !important;
  }
  .compact-gap {
    gap: 0.375rem !important;
  }
  /* Spacing utilities - using standard Tailwind classes instead of custom ones */

  /* Global backdrop blur utilities for consistent UI/UX */
  .backdrop-blur-enhanced {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  .backdrop-blur-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background-color: hsl(var(--semantic-gray-900) / 0.15);
  }

  .backdrop-blur-overlay {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    background-color: hsl(var(--semantic-gray-900) / 0.12);
  }

  .backdrop-blur-drawer {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    background-color: hsl(var(--semantic-gray-900) / 0.08);
  }
}
@keyframes highlight {
  0% { background-color: hsl(var(--primary) / 0.05); }
  100% { background-color: transparent; }
}
.command-suggestion.selected {
  @apply bg-primary-50;
}
/* Removed all global list styling that was interfering with markdown rendering */
/* Lists now use default browser styling with Tailwind classes applied via helpers.js */
.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
}
.mermaid-wrapper:fullscreen {
  z-index: 99999;
}
.mermaid-wrapper::-webkit-full-screen {
  z-index: 99999;
}
.mermaid-wrapper:-moz-full-screen {
  z-index: 99999;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}
.project-panel-content {
  max-width: 100%;
  word-wrap: break-word;
}
a {
  @apply text-primary;
}
.text-color {
  @apply text-custom-text-muted font-light;
}
.btn {
  @apply bg-primary rounded-md text-primary-foreground font-bold;
}
.pane {
  flex: 1;
  overflow: auto;
}
.split {
  display: flex;
  flex-direction: row;
}
.spinner {
  border: 4px solid hsl(var(--semantic-gray-900) / 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  @apply border-l-primary;
  animation: spin 1s ease infinite;
}
.spinner-empty {
  border: 4px solid hsl(var(--semantic-gray-900) / 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: hsl(var(--primary));
  animation: spin 1s ease infinite;
}
json {
  display: block;
  white-space: pre-wrap;
  color: hsl(var(--terminal-green));
  padding: 2px;
  border: solid 1px hsl(var(--terminal-green));
  background-color: hsl(var(--semantic-gray-800));
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  white-space: pre-wrap;
}
#svg-pan-zoom-controls {
  margin-top: -20px;
  scale: 0.5;
  transform: translateX(190%);
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.underline-on-hover {
  position: relative;
}
.underline-on-hover:hover::after {
  content: "";
  position: absolute;
  width: 100%;
  background: currentColor;
  bottom: -2px;
  left: 0;
}
/* Progress Bar Components - more compact */
@layer components {
  .progress {
    @apply flex justify-between w-[320px];
  }
  .progress-bar {
    @apply flex-1 h-1.5 mr-[8px] rounded;
  }
  .completed {
    @apply border-primary bg-primary;
  }
  .inprogress {
    @apply border-primary bg-custom-bg-primary;
  }
}
/* Tab content headers - neutral colors */
.tab-content-header-active {
  color: hsl(var(--semantic-gray-900));
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  border-color: hsl(var(--semantic-gray-700));
  border-bottom-width: 3px;
}
.tab-content-header {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: hsl(var(--semantic-gray-600));
}
/* Project panel components */
@layer components {
  .project-panel-heading {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: hsl(var(--foreground));
  }
  .project-panel-content {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: hsl(var(--foreground));
  }
  .project-panel-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
  }
}
/* Button components */
@layer components {
  .btn-primary:hover {
    background-color: hsl(var(--primary-600));
  }
  .pl-10 {
    padding-left: 7px;
  }
}
/* Table components */
@layer components {
  .custom-table {
    padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
    border-bottom: 1px solid hsl(var(--custom-border));
  }
  .table-heading {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: hsl(var(--custom-text-muted));
  }
  .table-content {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    color: hsl(var(--custom-text-primary));
  }
}
/* Modal components - enhanced blur and darker background for better UI/UX */
@layer components {
  .modal-container {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: hsl(var(--semantic-gray-900) / 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 50;
  }
  .modal-body {
    background-color: hsl(var(--background));
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px hsl(var(--semantic-gray-900) / 0.25);
    padding: 1rem;
    max-width: 28rem;
  }
  .modal-heading-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.375rem;
  }
}
/* Divider component - reduced margin for compact layout */
@layer components {
  .divider {
    width: 100%;
    border: 1px solid hsl(var(--semantic-gray-500));
    margin-bottom: 0.5rem;
  }
}
/* Architecture components */
@layer components {
  /* Buttons */
  .architecture-btn {
    background-color: hsl(var(--muted));
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    text-align: left;
    color: hsl(var(--foreground));
  }
  /* Table */
  .table-head {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    letter-spacing: 0.03em;
    text-align: left;
    color: hsl(var(--semantic-gray-500));
  }
  .table-row {
    font-size: var(--font-size-sm);
    text-align: left;
    color: hsl(var(--foreground));
  }
}
/* Tab components - neutral styling */
@layer components {
  /* Container for the tab buttons */
  .tab-container {
    display: flex;
    overflow-x: auto;
    background-color: hsl(var(--semantic-gray-50));
    border-bottom: 1px solid hsl(var(--custom-border));
    white-space: nowrap;
  }
  /* Individual tab buttons */
  .tab-button {
    color: hsl(var(--semantic-gray-600));
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease-in-out;
  }
  .tab-button:hover {
    color: hsl(var(--semantic-gray-800));
    background-color: hsl(var(--semantic-gray-100));
  }
  /* Active tab state */
  .tab-button.active {
    color: hsl(var(--semantic-gray-900));
    border-bottom: 2px solid hsl(var(--semantic-gray-700));
    background-color: hsl(var(--background));
  }
  /* Icon and text within tabs - more compact */
  .tab-button img {
    margin-right: 0.375rem;
  }
  .tab-button span {
    font-weight: var(--font-weight-semibold);
  }
  /* More button */
  /* Dropdown menu */
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: hsl(var(--custom-bg-primary));
    border: 1px solid hsl(var(--custom-border));
    border-radius: 0.25rem;
    box-shadow: 0 4px 6px -1px hsl(var(--semantic-gray-900) / 0.1);
    display: none;
    flex-direction: column;
    z-index: 10;
  }
  .dropdown-menu.show {
    display: flex;
  }
  .dropdown-item {
    cursor: pointer;
    transition: background-color 0.3s;
  }
  .dropdown-item:hover {
    background-color: hsl(var(--primary-50));
  }
}
/* Responsive styling */
@media (max-width: 768px) {
}
/* Gutter and panel styles */
.gutter1, .gutter {
  position: relative;
  background-color: hsl(var(--border));
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: visible;
}
.gutter1 {
  width: 1px;
  min-width: 1px;
}
.gutter:hover .toggle-button {
  opacity: 1;
  margin-top: 29px;
}
.gutter:hover .toggle-button {
  background-color: hsl(var(--primary));
  color: white;
}
.gutter-vertical:hover .toggle-button-vertical {
  opacity: 1;
  margin-left: 29px;
}
.gutter-vertical:hover .toggle-button-vertical {
  background-color: hsl(var(--primary));
  color: white;
}
/* Panel styles */
.panel {
  transition: width 0.2s ease, flex-basis 0.2s ease;
  position: relative;
}
.panel.collapsed {
  overflow: hidden;
}
.collapse-toggle:hover {
  background-color: hsl(var(--primary-100));
}
/* Typography classes */
@layer components {
  /* Base typography classes */
  .typography-heading-1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
  }
  .typography-heading-2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
  }
  .typography-heading-3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
  }
  .typography-heading-4 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
  }
  .typography-heading-5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
  }
  .typography-heading-6 {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
  }
  .typography-body-lg {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
  }
  .typography-body {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
  }
  .typography-body-sm {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
  }
  .typography-caption {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-tight);
  }

  /* Font weight utility classes */
  .font-weight-light {
    font-weight: var(--font-weight-light);
  }
  .font-weight-normal {
    font-weight: var(--font-weight-normal);
  }
  .font-weight-medium {
    font-weight: var(--font-weight-medium);
  }
  .font-weight-semibold {
    font-weight: var(--font-weight-semibold);
  }
  .font-weight-bold {
    font-weight: var(--font-weight-bold);
  }
  /* Legacy typography classes mapped to new system */
  .text-font {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: #2A3439;
    letter-spacing: var(--letter-spacing-wide);
  }
  .icon-font {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
  }
  .table-header {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: #4B5563;
  }
  .table-val {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: #2A3439;
  }
  /* Panel text styles */
  .user-panel-content {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
  }
  .user-panel-info {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-bold);
    color: #374151;
  }
  .user-panel-sub-head {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
  }
  /* Modal text styles */
  /* Accordion text styles */
  .accordion-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: #374151;
  }
  /* Other text styles */
  .breadcrumb-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    color: hsl(var(--semantic-gray-600));
  }
  .divison-val {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: hsl(var(--semantic-gray-600));
  }
  .text-link {
    font-size: var(--font-size-lg);
    color: hsl(var(--semantic-gray-600));
  }
}
@keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.animate-blink {
  animation: blink 1s infinite;
}
.custom-table-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all ease-in-out;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
/* Professional table styling improvements */
.professional-table-header {
  background: linear-gradient(to bottom, hsl(var(--semantic-gray-50)), hsl(var(--semantic-gray-100)));
  border-bottom: 2px solid hsl(var(--semantic-gray-200));
}
.professional-table-row:hover {
  background-color: hsl(var(--primary) / 0.05);
  transition: background-color 0.15s ease-in-out;
}
.professional-table-sort-icon {
  transition: color 0.2s ease-in-out;
}
.professional-table-sort-icon:hover {
  color: hsl(var(--primary) / 0.8);
}
/* Optimized table layout for better space utilization */
.table-compact-text {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-compact-text.multiline {
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  line-height: 1.2;
}
.custom-bread-crumb-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all ease-in-out;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: justify;
}
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(136, 136, 136, 0.5) transparent;
}
::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: rgba(136, 136, 136, 0.5);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(85, 85, 85, 0.7);
}
.banner-sp652-div ol,
.banner-sp652-div ul {
  list-style-type: disc;
  margin-left: 1rem;
  padding-left: 1rem;
}
.banner-sp652-div ol {
  list-style-type: decimal;
  margin-left: 1rem;
  padding-left: 1rem;
}
.banner-sp652-div ul li,
.banner-sp652-div ol li {
  margin: 0.5rem 0;
  line-height: 1.5;
}
.gradient-text {
  background: linear-gradient(to right,
      hsl(var(--primary-400)),
      hsl(var(--primary-600)),
      hsl(var(--primary)));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 100% 100%;
}
@keyframes gradientX {
  0% {
    background-position: 300% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.animate-gradient-x {
  animation: gradientX 2s ease infinite;
}
.bg-conic-gradient {
  background-image: conic-gradient(from 0deg, transparent, hsl(var(--primary)), hsl(var(--primary) / 0.5), transparent, transparent, hsl(var(--primary) / 0.3), transparent);
}
.bg-conic-gradient-light {
  background-image: conic-gradient(from 0deg, transparent, hsl(var(--primary)), hsl(var(--primary) / 0.5), transparent, transparent, hsl(var(--primary) / 0.3), transparent);
}
/* Single traveling line gradient for ChatInput */
.bg-conic-gradient-single-line {
  background-image: conic-gradient(from 0deg, transparent 0deg, transparent 60deg, hsl(var(--primary)) 90deg, hsl(var(--primary)) 120deg, transparent 150deg, transparent 360deg);
}
@keyframes gradient-rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes gradient-rotation-fast {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.animate-gradient-rotation {
  animation: gradient-rotation 3s linear infinite;
}
.animate-gradient-rotation-disabled {
  animation: gradient-rotation 1s linear infinite;
}
.animate-gradient-rotation-fast {
  animation: gradient-rotation-fast 1.5s linear infinite;
}
.animate-gradient-rotation-fast-disabled {
  animation: gradient-rotation-fast 0.8s linear infinite;
}
/* Make the wrapper take up the full space */
.conic-gradient-wrapper {
  width: 100%;
  height: 100%;
  transform-origin: center center;
}
.metric-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px hsl(var(--semantic-gray-900) / 0.1), 0 10px 10px -5px hsl(var(--semantic-gray-900) / 0.04);
}
.loading-spinner {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
/* Add to your globals.css */
.method-patch { background-color: hsl(var(--semantic-purple-100)); color: hsl(var(--semantic-purple-700)); }
/* Scrollbar styling for better UX */
.space-y-4::-webkit-scrollbar {
  width: 6px;
}
.space-y-4::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}
.space-y-4::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}
.space-y-4::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}