<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hash Data Retrieval</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>KAVIA Hash Data Retrieval Test</h1>
        
        <div class="test-section info">
            <h3>Test Instructions:</h3>
            <p>1. Click "Generate Test URL" to create a URL with sample hash data</p>
            <p>2. Click "Test Hash Retrieval" to test the parsing functionality</p>
            <p>3. Check the results below</p>
        </div>

        <div class="test-section">
            <h3>Actions:</h3>
            <button onclick="generateTestURL()">Generate Test URL</button>
            <button onclick="testHashRetrieval()">Test Hash Retrieval</button>
            <button onclick="clearHash()">Clear Hash</button>
        </div>

        <div class="test-section">
            <h3>Current URL:</h3>
            <pre id="currentUrl"></pre>
        </div>

        <div class="test-section">
            <h3>Test Results:</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // Copy the utility functions from our implementation
        const getKaviaWebsiteDataFromHash = () => {
            try {
                const hash = window.location.hash;
                
                if (!hash || !hash.includes('#data=')) {
                    return {
                        success: true,
                        data: null,
                        hasData: false,
                        message: 'No KAVIA website data found in URL hash'
                    };
                }
                
                const hashParams = new URLSearchParams(hash.substring(1));
                const encodedData = hashParams.get('data');
                
                if (!encodedData) {
                    return {
                        success: true,
                        data: null,
                        hasData: false,
                        message: 'No data parameter found in hash'
                    };
                }
                
                const jsonData = atob(encodedData);
                const userData = JSON.parse(jsonData);
                
                if (userData.prompt !== undefined && userData.stack && userData.platform) {
                    return {
                        success: true,
                        data: userData,
                        hasData: true
                    };
                } else {
                    return {
                        success: false,
                        data: null,
                        hasData: false,
                        error: 'Invalid data structure'
                    };
                }
                
            } catch (error) {
                console.error('Error retrieving KAVIA website data from hash:', error);
                return {
                    success: false,
                    data: null,
                    hasData: false,
                    error: error.message
                };
            }
        };

        function updateCurrentUrl() {
            document.getElementById('currentUrl').textContent = window.location.href;
        }

        function generateTestURL() {
            const testData = {
                prompt: "Create a task management app with team collaboration features and real-time notifications",
                stack: "Apps",
                platform: "Fullstack",
                frontendFramework: "React JS",
                backendFramework: "FastAPI",
                timestamp: Date.now()
            };
            
            const jsonData = JSON.stringify(testData);
            const encodedData = btoa(jsonData);
            
            window.location.hash = `#data=${encodedData}`;
            updateCurrentUrl();
            
            showResult('info', 'Test URL generated successfully!', testData);
        }

        function testHashRetrieval() {
            const result = getKaviaWebsiteDataFromHash();
            
            if (result.success && result.hasData) {
                showResult('success', 'Hash data retrieved successfully!', result.data);
            } else if (result.success && !result.hasData) {
                showResult('info', 'No hash data found', { message: result.message });
            } else {
                showResult('error', 'Error retrieving hash data', { error: result.error });
            }
        }

        function clearHash() {
            window.location.hash = '';
            updateCurrentUrl();
            showResult('info', 'Hash cleared', null);
        }

        function showResult(type, message, data) {
            const resultsDiv = document.getElementById('results');
            const resultHtml = `
                <div class="${type}">
                    <h4>${message}</h4>
                    ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
                </div>
            `;
            resultsDiv.innerHTML = resultHtml;
        }

        // Initialize
        updateCurrentUrl();
        
        // Update URL display when hash changes
        window.addEventListener('hashchange', updateCurrentUrl);
    </script>
</body>
</html>
